/* groovylint-disable <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DuplicateNumberLiteral, DuplicateStringLiteral */
/* groovylint-disable <PERSON>D<PERSON>, PublicMethodsBeforeNonPublicMethods, VariableTypeRequired, FieldTypeRequired */
package org.assure.restapi

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.rest.RestApi

/**
* Assure APIs methods abstraction
*/
class AssureRestApi extends RestApi {

    RestApi api
    def context
    String tokenUrl
    String apiKey
    String token

    AssureRestApi(String url, String credential, String tokenUrl, String apiKey) {
        super(url, credential)
        context = JenkinsContext.getContext()
        this.tokenUrl = tokenUrl
        this.apiKey = apiKey
    }

    void setToken() {
        this.token = getToken()        
    }

    /**
     * Invoke authorizer to get token to be used with Environment Service.
     * @return Valid token.
     */
    String getToken() {
        return post(tokenUrl, null, 'POST', [], [200], 'APPLICATION_FORM', 'NOT_SET').access_token
    }

    /**
     * Compose the headers required by the Environment Service API.
     * @return List with the headers.
     */
    List getHeader() {        
        return [[name: 'x-api-key', value: "${apiKey}", maskValue: true], 
                [name: 'Authorization', value: "Bearer ${this.token}", maskValue: true]]
    }    

    /**
     * Wrapper for HTTP GET operations that will retry after getting a new token in case that the first attempt
     * returns an Unauthorized response. This is used to avoid the implementation of this retry on every single method.
     * @param url Endpoint of Environment Service to be invoked.
     * @return Operation response.
     */
    Map getWithCustomAuth(String url) {        
        def response = get(url, [200, 401], getHeader())       
        if (response.message && response.message == 'Unauthorized') {
            token = getToken()
            response = get(url, [200], getHeader())
        }

        return response
    }

    /**
     * Wrapper for HTTP POST operations that will retry after getting a new token in case that the first attempt
     * returns an Unauthorized response. This is used to avoid the implementation of this retry on every single method.
     * @param url Endpoint of Environment Service to be invoked.
     * @return Operation response.
     */
    Map postWithCustomAuth(String url, String details, String method, String contentType = 'APPLICATION_JSON') {
        def response = post(url, details, method, getHeader(), [200, 201, 202, 204, 401], contentType)
        if (response.message && response.message == 'Unauthorized') {
            token = getToken()
            response = post(url, details, method, getHeader(), [200, 201, 202, 204], contentType)
        }
        return response
    }

}
