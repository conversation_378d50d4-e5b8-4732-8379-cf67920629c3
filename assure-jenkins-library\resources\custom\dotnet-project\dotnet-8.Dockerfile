FROM mcr.microsoft.com/dotnet/sdk:8.0

ENV PIP_NO_EXTERNAL_ENV=1
ENV PIP_BREAK_SYSTEM_PACKAGES=1

# Nuget
RUN apt-get update \
    && apt-get -y install nuget \
    && apt-get clean
    
# Mono-complete for certmgr
RUN apt-get -y install mono-complete \
    && apt-get clean

# Install certificate for Artifactory
RUN yes | certmgr -ssl https://artifactory.platformdxc-mg.com

RUN wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
RUN chmod a+x /usr/local/bin/yq

#Checkov
RUN apt-get install -y python3-pip \
    && pip3 install --upgrade pip setuptools \
    && pip3 install -U checkov \
    && apt-get clean



