{"Version": "2012-10-17", "Statement": [{"Sid": "BucketAccess", "Effect": "Allow", "Action": ["s3:PutObject", "s3:PutObjectAcl", "s3:GetBucketLocation", "s3:GetEncryptionConfiguration", "s3:GetBucketPolicy", "s3:ListBucket", "s3:ListBucketVersions", "s3:GetObject", "s3:GetObjectAcl", "s3:DeleteObject", "s3:DeleteObjectVersion", "s3:GetObjectVersion"], "Resource": ["${s3_bucket_arn}", "${s3_bucket_arn}/*", "${fts_bucket_arn}", "${fts_bucket_arn}/*"]}, {"Sid": "DynamoDBAccessToTrainingStatusTable", "Effect": "Allow", "Action": ["dynamodb:*", "logs:CreateLogGroup"], "Resource": ["${training_status_table_arn}", "${training_status_table_arn}/index/*", "${reports_table_arn}", "${reports_table_arn}/index/*"]}]}