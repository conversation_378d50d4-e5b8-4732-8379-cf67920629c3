#!/usr/bin/env groovy

import org.pdxc.rest.GitApi
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.assure.util.WarningUtils
import org.assure.pushpipeline.PushPipelineUtils
import org.assure.utilitypipeline.SonarPipelineUtils

/**
 * Pipeline creation and execution.
 *
 * @param stagesMap Specific data for each stage.
 * @param dockerPath Full path and name of a dockerFile to be used on the pipeline. If not provided, default is used.
 * @return void
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'sonar.Dockerfile') {

    // Pipeline name
    String pipelineName = 'sonar'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the dockerFile
    String dockerName
    // Current repository name
    def repoName
    // List of repositories to scan
    def repositories
    // Scan satuses
    def failedScans = []
    def successScans = []
    // Docker registry credentials
    def buildDockerRegistryCreds
    def buildDockerRegistryUrl = null
    // Timestamp
    String currentTime
    // Number of max parallel executions for the repositories scan stage
    def executionLimits = 10
    // Coverage reports
    Map coverage = [:]

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 1, unit: 'HOURS')
        }

        stages {
            stage('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        sonarUtils = new SonarPipelineUtils()
                        warningUtils = new WarningUtils()
                        dockerName = "${pipelineName}.Dockerfile"
                    }
                }
            }
            stage('Pipeline info') {
                steps {
                    script {
                        configData = sonarUtils.pipelineInfoSteps(pipelineName)
                        def now = new Date()
                        currentTime = now.format("yyyy-MM-dd.HH:mm", TimeZone.getTimeZone('UTC'))

                        sonarUtils.setSonarDockerAgent(dockerPath, dockerName)
                        (buildDockerRegistryUrl, buildDockerRegistryCreds) = pushUtils.getDockerRegistryUrlAndCreds(configData, dockerName, pipelineName)
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo("Configured repository name matches current repository: ${repoName}")
                    }
                }
            }
            stage('Repositories Scan') {
                agent {
                    dockerfile {
                        args '-u root:root'
                        filename "${pipelineName}.Dockerfile"
                        reuseNode true
                        registryCredentialsId 'assure-docker-lambda'
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('📥 Get repositories'){
                        steps{
                            script{
                                catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                    def failedRepositories
                                    def listName = ValuesUtils.getVariable(configData, 'listFileName', 'repositories')
                                    def repos = sonarUtils.getRepositories(listName)
                                    (repositories, failedRepositories) = sonarUtils.cleanRepositoriesList(repos)
                                    if (!failedRepositories.isEmpty()) {
                                        error("-- ❌️ -- Missing repository name/s. Please Check your list items.")
                                    }
                                    echo('Loaded repositories: \n\n' + repositories.join('\n\n'))
                                }
                            }
                        }
                    }
                    stage('📲 Scan repositories'){
                        steps {
                            script {
                                def parallelExecution = [:]
                                def parallelCount = 0

                                repositories.each { repo ->
                                    parallelExecution[repo.repoName] = {
                                        stage(repo.repoName) {
                                            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                def successfullScan
                                                def repoCoverage
                                                sonarUtils.cloneRepository(configData, repo)

                                                def repositoryName = sonarUtils.checkJavascriptLanguage(repo.repoName)

                                                repoCoverage = sonarUtils.getSonarReport(repositoryName)
                                                successfullScan = sonarUtils.checkQualityGate(repositoryName)

                                                if (repoCoverage != null) {
                                                    coverage[repo.repoName] = repoCoverage
                                                } else {
                                                    error("-- ❌️ -- No coverage report found for ${repo.repoName}. It is onboarded in the Push pipelines? Please check your configuration.")
                                                }

                                                if (successfullScan != false) {
                                                    successScans.add(repo.repoName)
                                                } else {
                                                    failedScans.add(repo.repoName)
                                                }
                                            }
                                        }
                                    }
                                    parallelCount++
                                    if (parallelCount >= executionLimits) {
                                        parallel(parallelExecution)
                                        parallelExecution = [:]
                                        parallelCount = 0
                                    }
                                }
                                if (!parallelExecution.isEmpty()) {
                                    parallel(parallelExecution)
                                }
                            }
                        }
                    }
                    stage('📜 Generate Log Files'){
                        steps {
                            script {
                                echo ('Generating log files...')

                                if (successScans.size() > 0 || failedScans.size() > 0) {
                                    sonarUtils.compressAndArchiveLogs(currentTime)
                                } else {
                                    error("-- ❌️ -- No repositories scanned. Please check the log.")
                                }
                            }
                        }
                    }
                    stage('📧 Send notifications'){
                        steps {
                            script {
                                echo ('Sending notifications...')
                                List<String> emailRecipients = ValuesUtils.getVariableArrayList(configData, 'emailList', 'repositories')
                                sonarUtils.sendEmailNotifications(emailRecipients, successScans, failedScans, currentTime, coverage)
                            }
                        }
                    }
                }
            }
        }
        post {
            always {
                script {
                    postActions(configData)
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}