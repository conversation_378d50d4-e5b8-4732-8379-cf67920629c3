package org.assure.util

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.DefaultConfiguration
import org.pdxc.util.Utils
import org.pdxc.exception.ContextNotSetException
import groovy.json.JsonOutput
import org.pdxc.util.ValuesUtils

/**
* Utility class
*/
class WarningUtils {
    private WarningUtils () {}

    def context = JenkinsContext.getContext()

    void createWarning(String pipelineName, def configData ) {
        String message

        def warnings = context.libraryResource "warnings/warnings.yml"
        context.writeFile file: "warnings.yml", text: warnings
        def defaultConfigYml = context.readYaml file: "warnings.yml"
        context.sh(script: "rm -rf warnings.yml", label:"Remove unnecesary file")

        message = createWarningMessages(pipelineName, message, defaultConfigYml)

        message = createDeprecationMessages(message, configData, defaultConfigYml)

        setBuildResult()

        if (message != '') {
            context.currentBuild.description =  "🤖 Automation team warnings: ${message}"
        }
    }

    def createWarningMessages(String pipelineName, String message, def defaultConfigYml) {
        List pipelineWarns = ValuesUtils.getVariableArrayList(defaultConfigYml, 'pipelineWarns')

        pipelineWarns.each { warn ->
            if (warn.pipelineName == pipelineName) {
                message = warn.message
            }
        }

        return message
    }

    def createDeprecationMessages(String message, def configData, def defaultConfigYml) {
        List deprecations = ValuesUtils.getVariableArrayList(defaultConfigYml, 'deprecations')

        def nodeVersion = ValuesUtils.getVariable(configData, 'nodeVersion') || "0.0.0"
        def npmVersion = ValuesUtils.getVariable(configData, 'npmVersion') || "0.0.0"
        def dotnetVersion = ValuesUtils.getVariable(configData, 'dotnetVersion') || "0.0.0"
        def pythonVersion = ValuesUtils.getVariable(configData, 'pythonVersion') || "0.0.0"
        def javaVersion = ValuesUtils.getVariable(configData, 'javaVersion') || "0.0.0"

        deprecations.each { deprecation ->
            if (nodeVersion != null && nodeVersion == deprecation.nodeVersion) {
                message = message + '' + deprecation.message
            }
            if (npmVersion != null && npmVersion == deprecation.npmVersion) {
                message = message + '' + deprecation.message
            }
            if (dotnetVersion != null && dotnetVersion == deprecation.dotnetVersion) {
                message = message + '' + deprecation.message
            }
            if (pythonVersion != null && pythonVersion == deprecation.pythonVersion) {
                message = message + '' + deprecation.message
            }
            if (javaVersion != null && javaVersion == deprecation.javaVersion) {
                message = message + '' + deprecation.message
            }
            
        }

        return message
    }

    void setBuildResult() {
        if (context.currentBuild.result == 'SUCCESS') {
            context.currentBuild.displayName = context.currentBuild.displayName.replace("#", "") + ' - 💃 Way to go!'
        } else {
            context.currentBuild.displayName = context.currentBuild.displayName.replace("#", "") + ' - ☠️ Something went wrong'
        }
    }
}

