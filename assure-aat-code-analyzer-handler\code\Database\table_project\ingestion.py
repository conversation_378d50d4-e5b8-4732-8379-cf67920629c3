import json
import os
import shutil
import time
import zipfile
from urllib.parse import urlparse

import git
from aws_lambda_powertools import Logger

# Initialize structured logger
logger = Logger(service="Code-Analyzer")

# Conditional imports depending on the environment (Lambda or local)
if "AWS_LAMBDA_FUNCTION_NAME" in os.environ:
    from Utils_CodeAnalyzer import Utils_S3  # import layer Utils_CodeAnalyzer_layer
    from Utils_CodeAnalyzer import Utils_config_parser  # import layer Utils_CodeAnalyzer_layer
else:
    import Utils_S3
    import Utils_config_parser


class CodeIngestor:
    """
    Handles code ingestion from various sources (ZIP, GitHub, local, or S3),
    applies optional filters, uploads ingested code to an S3 target folder.
    """

    def __init__(self, target_folder, s3_manager):
        """
        Initialize the CodeIngestor instance.

        Args:
            target_folder (str): The S3 folder path where code will be uploaded.
            s3_manager (S3FileManager): Object to manage S3 file operations.
        """
        self.target_prefix = target_folder.rstrip('/') + '/'
        self.temp_path = "/tmp/" if "AWS_LAMBDA_FUNCTION_NAME" in os.environ else ""
        self.apply_filters = False
        self.included_folders = None
        self.included_files = None
        self.s3_manager = s3_manager

        # GitHub credentials (optional)
        self.git_username = os.environ.get("GIT_USERNAME", "")
        self.git_token = os.environ.get("GIT_TOKEN", "")
        self.ingested_code_s3_uri = ""
        # Mapping ingestion types to their handler methods
        self.ingestion_methods = {"zip": self._ingest_zip,
                                  "github": self._ingest_github,
                                  "local": self._ingest_local,
                                  "s3": self._ingest_s3}

    def _get_filters(self, apply_filters, filters):
        """
        Extract and normalize folder/file filters from user input.
        """
        self.apply_filters = apply_filters
        folders = filters.get("folders", ["all"])
        files = filters.get("files", ["all"])

        if len(folders) == 0 or folders == ["all"]:
            folders = None
        if len(files) == 0 or files == ["all"]:
            files = None

        if not folders and not files:
            self.apply_filters = False
        if folders:
            folders = [s.lower() for s in folders]
        if files:
            files = [s.lower() for s in files]

        self.included_folders = folders
        self.included_files = files

    def _delete_temp_folder(self, folder_name, retries=3, delay=2):
        """
        Safely delete temporary folder with retries.

        Args:
            folder_name (str): Name of the folder inside './tmp/' to delete.
            retries (int): Number of retry attempts (default: 3).
            delay (int): Seconds to wait between retries (default: 2).
        """
        temp_path = f'{self.temp_path}{folder_name}'

        for attempt in range(retries):
            try:
                if os.path.exists(temp_path):
                    for root, dirs, files in os.walk(temp_path):
                        for d in dirs:
                            os.chmod(os.path.join(root, d), 0o777)
                        for f in files:
                            os.chmod(os.path.join(root, f), 0o777)
                    shutil.rmtree(temp_path)
                    logger.info(f"Successfully deleted temporary folder: {temp_path}")
                    return
            except PermissionError as e:
                logger.info(f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {delay} seconds...")
                time.sleep(delay)

        logger.info(f"Error: Failed to delete {temp_path} after {retries} attempts.")

    def _create_temp_folder(self, folder_name):
        """
        Create a temporary folder in the /tmp/ directory.

        Args:
            folder_name (str): Folder to create.
        """
        temp_path = f'{self.temp_path}{folder_name}'
        self._delete_temp_folder(folder_name)
        os.makedirs(temp_path, exist_ok=True)
        logger.debug(f"Created temporary folder: {temp_path}")
        return temp_path

    def _generate_standardized_response(self, status_code, message, upload_result):
        """
        Standard format for API responses.
        """
        return {"statusCode": status_code,
                "body": json.dumps({"message": message,
                                    "upload_result": upload_result,
                                    "ingested_code_s3_uri": self.ingested_code_s3_uri})}

    def _get_files_to_copy(self, src):
        """
        Determine which files to upload based on filters.
        """
        paths_to_copy = []
        if not self.apply_filters:
            # Return all files from the source recursively
            for root, _, files in os.walk(src):
                for file in files:
                    paths_to_copy.append(os.path.join(root, file))
        else:
            if self.included_folders is None:
                # Include all folders and filter by file extensions
                for root, _, files in os.walk(src):
                    for file in files:
                        # If included_file_extensions is None, include all files
                        if self.included_files is None or file.split('.')[-1].lower() in self.included_files:
                            paths_to_copy.append(os.path.join(root, file))
            else:
                # Filter folders first (based on included_folders)
                valid_folders = [folder for folder in self.included_folders if os.path.isdir(os.path.join(src, folder))]
                for folder in valid_folders:
                    folder_path = os.path.join(src, folder)
                    for root, _, files in os.walk(folder_path):
                        for file in files:
                            # If included_file_extensions is None, include all files in valid folders
                            if self.included_files is None or file.split('.')[-1].lower() in self.included_files:
                                paths_to_copy.append(os.path.join(root, file))

        return paths_to_copy

    def _upload_files_to_s3(self, source_dir):
        """
        Upload a filtered or full set of files to S3.

        Args:
            source_dir (str): Directory containing files to upload.
        """
        count = 0
        folder_name = os.path.basename(os.path.normpath(source_dir))
        target_folder = self.target_prefix.rstrip("/") + "/" + folder_name
        self.s3_manager.delete_folder(target_folder)
        for file_path in self._get_files_to_copy(source_dir):
            # Remove a temporary path from file_path
            relative_path = file_path.replace(self.temp_path, '')

            # Replace source_dir with folder_name
            relative_path = relative_path.replace(source_dir, folder_name)

            # Remove any leading slashes
            relative_path = relative_path.lstrip('/')

            # Join with target_prefix
            s3_key = f"{self.target_prefix}{relative_path}"

            # Replace backslashes with forward slashes for S3 compatibility
            s3_key = s3_key.replace("\\", "/")
            try:
                self.s3_manager.upload_file(file_path, s3_key)
                count += 1
            except Exception as e:
                logger.error(f"Upload failed: {file_path} -> {s3_key}: {e}")
        self.ingested_code_s3_uri = f"s3://{self.s3_manager.bucket_name}/{target_folder}"
        return f"Uploaded {count} files"

    def _ingest_github(self, repo_url):
        """
        Clone a GitHub repo shallowly and upload its content to S3.
        """
        repo_url = repo_url.rstrip("/")
        parsed_url = urlparse(repo_url)

        repo_name = os.path.splitext(os.path.basename(parsed_url.path))[0]
        repo_path = self._create_temp_folder(repo_name)

        try:
            access_url = f"https://{self.git_username}:{self.git_token}@{parsed_url.netloc}{parsed_url.path}"
            logger.info(f"Cloning {repo_url}")
            git.Repo.clone_from(access_url, repo_path, depth=1)  # Shallow clone
            upload_result = self._upload_files_to_s3(repo_path)
            return self._generate_standardized_response(200,
                                                        f"GitHub ingestion complete: {repo_url}",
                                                        upload_result)
        except Exception as e:
            logger.error(f"GitHub ingestion failed: {e}")
            raise
        finally:
            self._delete_temp_folder(repo_name)

    def _ingest_local(self, local_path):
        """
        Upload content from local file system.
        """
        if not os.path.isdir(local_path) or not os.access(local_path, os.R_OK):
            raise ValueError(f"Invalid local path: {local_path}")
        logger.info(f"Ingesting local directory: {local_path}")
        result = self._upload_files_to_s3(local_path)
        return self._generate_standardized_response(200,
                                                    f"Local ingestion complete: {result}",
                                                    result)

    def _ingest_zip(self, zip_path):
        """
        Extract a ZIP file locally, apply filters, and upload to S3.
        """
        if not zip_path.lower().endswith(".zip") or not os.path.isfile(zip_path):
            raise ValueError(f"Invalid ZIP file path: {zip_path}")

        logger.info(f"Extracting ZIP: {zip_path}")
        zip_name = os.path.splitext(os.path.basename(zip_path))[0]
        extract_path = self._create_temp_folder(zip_name)

        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)

            logger.info(f"ZIP extracted to: {extract_path}")
            upload_result = self._upload_files_to_s3(extract_path)
            return self._generate_standardized_response(200,
                                                        f"ZIP ingestion complete: {upload_result}",
                                                        upload_result)
        except zipfile.BadZipFile as e:
            raise ValueError(f"Invalid ZIP file: {zip_path}") from e
        except Exception as e:
            logger.error(f"Failed to extract or upload ZIP contents: {e}")
            raise
        finally:
            self._delete_temp_folder(zip_name)

    def _ingest_s3(self, s3_uri):
        """
        Unified S3 ingestion method:
        - If the S3 path ends with `.zip`, treats it as a ZIP file.
        - If it's a file (not a zip), uploads the single file.
        - Otherwise, treats it as a folder/prefix.

        Args:
            s3_uri (str): Full S3 URI (e.g., 's3://bucket/folder/', 's3://bucket/file.zip', 's3://bucket/script.py')
        """
        parsed = urlparse(s3_uri)
        if parsed.scheme != "s3" or not parsed.netloc or not parsed.path:
            raise ValueError(f"Invalid S3 URI: {s3_uri}")

        source_bucket = parsed.netloc.lower().strip()
        s3_path = parsed.path.lstrip('/')
        is_zip = s3_path.lower().endswith(".zip")
        is_file = not s3_path.endswith("/") and not is_zip

        if is_zip:
            item_name = os.path.splitext(os.path.basename(s3_path))[0]
        elif is_file:
            item_name = os.path.splitext(os.path.basename(s3_path))[0]  # remove extension
        else:  # folder
            item_name = os.path.basename(s3_path.rstrip('/'))  # folder name
        temp_dir = self._create_temp_folder(item_name)

        if self.s3_manager.bucket_name.lower().strip() == source_bucket:
            s3_mgr = self.s3_manager
        else:
            s3_mgr = Utils_S3.S3FileManager(source_bucket)
        try:
            if is_zip:
                # ZIP ingestion
                local_zip_path = os.path.join(temp_dir, os.path.basename(s3_path))
                logger.info(f"Downloading ZIP from S3: s3://{source_bucket}/{s3_path}")
                s3_mgr.download_file(s3_path, local_zip_path)

                logger.info(f"Extracting ZIP: {local_zip_path}")
                with zipfile.ZipFile(local_zip_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)

            elif is_file:
                # Single file ingestion
                file_name = os.path.basename(s3_path)
                local_file_path = os.path.join(temp_dir, file_name)
                logger.info(f"Downloading single file from S3: s3://{source_bucket}/{s3_path}")
                s3_mgr.download_file(s3_path, local_file_path)

                # Apply filters if any
                ext = file_name.split('.')[-1].lower()
                if self.apply_filters and self.included_files and ext not in self.included_files:
                    logger.info(f"File skipped due to filter: {file_name}")
                    return self._generate_standardized_response(200,
                                                                "File skipped due to filters",
                                                                "0 files uploaded")

            else:
                # Folder ingestion
                logger.info(f"Downloading folder from S3: s3://{source_bucket}/{s3_path}")
                s3_mgr.download_folder(s3_path, temp_dir)

            logger.info("Uploading content to destination S3 location...")
            upload_result = self._upload_files_to_s3(temp_dir)
            return self._generate_standardized_response(200,
                                                        f"S3 ingestion complete: {upload_result}",
                                                        upload_result)
        except Exception as e:
            logger.error(f"S3 ingestion failed: {e}")
            raise
        finally:
            self._delete_temp_folder(item_name)

    def ingest_code(self, ingestion_type, code_path, apply_filters, filters):
        """
        Entry point to trigger the appropriate ingestion method.
        """
        self._get_filters(apply_filters, filters)
        method = self.ingestion_methods.get(ingestion_type)
        if not method:
            raise ValueError(f"Unsupported ingestion type: {ingestion_type}")
        return method(code_path)


def get_ingestion_target_folder(app_yaml, ingestion_type):
    """
    Resolve destination S3 path for ingested code using config.
    """
    destination_folder = Utils_config_parser.get_nested(app_yaml, "system_config", "ingestion_types",
                                                        ingestion_type, "destination_folder", default="")
    landing_zone_path = Utils_config_parser.get_nested(app_yaml, "project_config", "data_zones",
                                                       "landing_zone", "s3_path", default="")
    return f"{landing_zone_path}{destination_folder}"


def get_ingestion_type(code_path):
    """
    Determine the ingestion type based on an input path.
    """
    path_lower = code_path.lower()
    if code_path.startswith("s3://"):
        return "s3"
    if path_lower.endswith(".zip"):
        return "zip"
    if "github." in path_lower:
        return "github"
    if os.path.exists(code_path):  # Covers both file and directory
        return "local"

    raise ValueError(f"Ingestion type is not identified for path: {code_path}")


def execute(user_config, s3_bucket_name, application_configuration_file):
    """
    Lambda entry point for code ingestion.
    """
    try:
        # Download application config file
        temp_path = "/tmp/" if "AWS_LAMBDA_FUNCTION_NAME" in os.environ else ""
        s3_manager = Utils_S3.S3FileManager(s3_bucket_name)
        downloaded_file_path = f"{temp_path}app.yaml"
        s3_manager.download_file(application_configuration_file, downloaded_file_path)

        # Parse configuration using ConfigYamlParser
        client_name = user_config.get("client_name", "").strip()
        app_instance = user_config.get("application_instance", "").strip()
        app_type = user_config.get("application_type", "").strip()
        app_version = user_config.get("application_version", "").strip()
        parser = Utils_config_parser.ConfigYamlParser(file_path=downloaded_file_path,
                                                      client_name=client_name,
                                                      app_instance=app_instance,
                                                      app_type=app_type,
                                                      app_version=app_version)
        app_yaml = parser.parse()

        # Extract ingestion config
        ingestion_conf = user_config.get("ingestion", {})
        code_path = ingestion_conf.get("code_path", "")
        ingestion_type = get_ingestion_type(code_path)
        apply_filters = ingestion_conf.get("apply_filters", False)
        filters = ingestion_conf.get("filters", {})

        if not ingestion_type or not code_path:
            raise ValueError("Both 'ingestion_type' and 'code_path' must be provided.")

        target_folder = get_ingestion_target_folder(app_yaml, ingestion_type)
        ingestor = CodeIngestor(target_folder, s3_manager)

        ingestor.ingest_code(ingestion_type, code_path, apply_filters, filters)
        s3_uri = ingestor.ingested_code_s3_uri
        logger.info(f"Ingested code S3 URI: {s3_uri}")
        return s3_uri

    except Exception as e:
        logger.exception("Code ingestion failed")
        raise
