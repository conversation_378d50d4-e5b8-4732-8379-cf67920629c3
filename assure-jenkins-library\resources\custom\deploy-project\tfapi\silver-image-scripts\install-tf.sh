#
# We do this in a script because we don't want to polute the Dockerfile
# with lots of copy-paste code and we want to do a lot of complex stuff.
#
# These SHOULD be the only lines you ned to change when updating supported TF versions
#
# TF versions are "named" - the current names are the major versions, but any string
# will do as long as it is unique. These names are what can be specified in conf.yml or
# the request payload.
#
# The ACTUAL versions these name point to MUST be "." separate numeric parts, otherwise 
# some comparisons in the service scripting will not work.
#
# The script installs terraform in /usr/local/lib subfolders named after the version name
# and writes an 'env.sh" script into that folder
#
#declare -A TERRAFORM_VERSIONS=(      [0.11]=0.11.15 [0.12]=0.12.31 [0.13]=0.13.7 [0.14]=0.14.11 [0.15]=0.15.5 [1.2]=1.2.9 [1.3]=1.3.3 )
#declare -A TERRAFORM_UPGRADE_FROMS=(                [0.12]=0.11    [0.13]=0.12   [0.14]=0.13    [0.15]=0.14   [1.2]=0.15  [1.3]=1.2 )

# OC-13570
#declare -A TERRAFORM_VERSIONS=(      [0.11]=0.11.15 [0.12]=0.12.31 [0.13]=0.13.7 [0.15]=0.15.5 [1.2]=1.2.9 [1.3]=1.3.3 [1.4]=1.4.6 )
#declare -A TERRAFORM_UPGRADE_FROMS=(                [0.12]=0.11    [0.13]=0.12   [0.15]=0.13   [1.2]=0.13  [1.3]=0.13 [1.4]=0.13 )

declare -A TERRAFORM_VERSIONS=(      [0.11]=0.11.15 [0.12]=0.12.31 [0.13]=0.13.7 [1.4]=1.4.6 )
declare -A TERRAFORM_UPGRADE_FROMS=(                [0.12]=0.11    [0.13]=0.12   [1.4]=0.13 )

#
# You most likely won't need to touch what's below here
#
USER_HOME=${TF_USER_HOME:-$HOME}
TERRAFORM_ENV_SH="${USER_HOME}/terraform-env.sh"
TERRAFORM_VERSION_ENV_SH="tf-env.sh"

#
# Initialize the env shell script that we build
#
echo '###' >> ${TERRAFORM_ENV_SH}
echo '### Terraform home locations' >> ${TERRAFORM_ENV_SH}
echo '###' >> ${TERRAFORM_ENV_SH}

#
# Convert the version number into an integer
#
function get_version_number {
	local tf_version=$1
	IFS='.' read -r -a tf_version_parts <<< "$tf_version"
	[[ ${#tf_version_parts[@]} -eq 3 ]] || return 1
	local version_number=0
	for part in ${tf_version_parts[@]}; do
		version_number=$((version_number * 100 + part))
	done
	echo $version_number
}

#
# Install the supported versions and complete the environment shell script
#
function install_terraform_versions {
	TERRAFORM_VERSION_NAMES="${1:-${!TERRAFORM_VERSIONS[@]}}"
	local tf_binary_base_dir="${2}"
	local tf_version tf_version_name tf_location RC tf_upgrade_from_version tf_upgrade_from_version_number tf_upgrade_from_version_string

	cat << EOF > "${TERRAFORM_ENV_SH}"

TERRAFORM_VERSION_FOLDER_PREFIX=${tf_binary_base_dir}/terraform-
TERRAFORM_ENV_FILE_NAME=${TERRAFORM_VERSION_ENV_SH}
function unset_vars {
	unset TERRAFORM_VERSION 
	unset TERRAFORM_HOME 
	unset TERRAFORM_VERSION_STRING 
	unset TERRAFORM_VERSION_NUMBER 
	unset TERRAFORM_MAJOR_VERSION_NUMBER 
	unset TERRAFORM_UPGRADE_FROM_VERSION
	unset TERRAFORM_UPGRADE_FROM_VERSION_STRING
	unset TERRAFORM_UPGRADE_FROM_VERSION_NUMBER 
	unset TERRAFORM_UPGRADE_FROM_MAJOR_VERSION_NUMBER
}

##
## Set the env vars found in the TF home if found
##
function get_terraform_version_details {
	local version_sh="\${TERRAFORM_VERSION_FOLDER_PREFIX}\${1}/\${TERRAFORM_ENV_FILE_NAME}"
	unset_vars
	if [[ -f "\$version_sh" ]]; then
		. "\$version_sh"
	fi
}

##
## Do the above and add the TF home to the PATH
##
function add_terraform_home_to_path {
	get_terraform_version_details "\$1"
	[[ "\$TERRAFORM_HOME" ]] || { echo "Could not get Terraform details for version '\$TERRAFORM_VERSION'" >&2; return 1; }
	PATH=\$TERRAFORM_HOME:\$PATH
	return 0
}
EOF

	#
	# For each supported version
	#
	for tf_version_name in $TERRAFORM_VERSION_NAMES

	do
		tf_version="${TERRAFORM_VERSIONS[$tf_version_name]}"
		[[ "$tf_version" ]] || { echo "ERROR: Terraform version named $tf_version_name not supported"; exit 1; }
		#
		# Figure out the "comparable version integer"
		#
		tf_version_number=$(get_version_number $tf_version)
		RC=$?
		[[ $RC -eq 0 ]] || { echo "Ignoring version ${tf_version}: invalid structure"; continue; }

		#
		# Derive the various env vars (major version, home dir, env var name)
		#
		tf_dirname=${tf_binary_base_dir}/terraform-${tf_version_name}

		#
		# Download it from Hashicorp, unzip it, make it executable
		#
		tf_location=https://releases.hashicorp.com/terraform/${tf_version}/terraform_${tf_version}_linux_amd64.zip

		echo "Installing Terraform version ${tf_version} from ${tf_location}"
		[ -e "${tf_dirname}/terraform" ] && rm "${tf_dirname}/terraform"
		curl "${tf_location}" -o terraform-${tf_version}.zip \
        	&& unzip terraform-${tf_version}.zip -d ${tf_dirname} \
			&& chmod +x "${tf_dirname}/terraform"
		RC=$?
		if [ $RC -ne 0 ]; then
			echo "Installation of Terraform $tf_version failed" >&2
		else
			#
			# It's all OK, set up the env.sh content for this version
			#
			tf_upgrade_from_version="${TERRAFORM_UPGRADE_FROMS[$tf_version_name]:-none}"
			echo "export TERRAFORM_VERSION=\"${tf_version_name}\"" > ${tf_dirname}/${TERRAFORM_VERSION_ENV_SH}
			echo "export TERRAFORM_HOME=\"$tf_dirname\"" >> ${tf_dirname}/${TERRAFORM_VERSION_ENV_SH}
			echo "export TERRAFORM_VERSION_STRING=\"${tf_version}\"" >> ${tf_dirname}/${TERRAFORM_VERSION_ENV_SH}
			echo "export TERRAFORM_VERSION_NUMBER=\"${tf_version_number}\"" >> ${tf_dirname}/${TERRAFORM_VERSION_ENV_SH}
			echo "export TERRAFORM_MAJOR_VERSION_NUMBER=\"$((tf_version_number / 100))\"" >> ${tf_dirname}/${TERRAFORM_VERSION_ENV_SH}
			echo "export TERRAFORM_UPGRADE_FROM_VERSION=\"$tf_upgrade_from_version\"" >> ${tf_dirname}/${TERRAFORM_VERSION_ENV_SH}

			#
			# Add major-version => name into main env
			#
			echo "export TERRAFORM_MAJOR_VERSION_$((tf_version_number / 100))=\"$tf_version_name\"" >> "${TERRAFORM_ENV_SH}"

			if [[ "$tf_upgrade_from_version" != "none" ]]; then
				tf_upgrade_from_version_string=${TERRAFORM_VERSIONS[$tf_upgrade_from_version]}
				tf_upgrade_from_version_number=$(get_version_number $tf_upgrade_from_version_string)
				echo "export TERRAFORM_UPGRADE_FROM_VERSION_STRING=\"$tf_upgrade_from_version_string\"" >> ${tf_dirname}/${TERRAFORM_VERSION_ENV_SH}
				echo "export TERRAFORM_UPGRADE_FROM_VERSION_NUMBER=\"$tf_upgrade_from_version_number\"" >> ${tf_dirname}/${TERRAFORM_VERSION_ENV_SH}
				echo "export TERRAFORM_UPGRADE_FROM_MAJOR_VERSION_NUMBER=\"$((tf_upgrade_from_version_number / 100))\"" >> ${tf_dirname}/${TERRAFORM_VERSION_ENV_SH}
			fi
		fi
		rm -f terraform-${tf_version}.zip
	done
}

#
# Now a self test
#

function self_test {

	echo "Terraform major versions to test: $TERRAFORM_VERSION_NAMES"

	local tf_version_name this_tf_version_string
	#
	# For each supported version in the file
	#
	for tf_version_name in $TERRAFORM_VERSION_NAMES; do
		get_terraform_version_details "$tf_version_name"
		if [[ "$TERRAFORM_HOME" ]]; then
			#
			# Go to that version's home and execute 'terraform -version'
			# capturing the output string into an array
			#
			cd ${TERRAFORM_HOME} \
				&& this_tf_version_string=($(./terraform -version))
			RC=$?
			if [ $RC -eq 0 ]; then
				#
				# Get the major version from the 'terraform -version' output string
				#
				this_tf_version_string=${this_tf_version_string[1]#v} \

				#
				# Check that it's the same as the version and home we are testing
				#
				if [ "$this_tf_version_string" == "${TERRAFORM_VERSION_STRING}" ]; then
					echo "Terraform version ${tf_version_name} successfully installed in ${TERRAFORM_HOME} as ${this_tf_version_string}"
					continue
				else 
					echo "Terraform version ${tf_version_name} NOT successfully installed in ${TERRAFORM_HOME}: found ${this_tf_version_string} instead of ${TERRAFORM_VERSION_STRING}" >&2
				fi
			else
				echo "Terraform version ${tf_version_name} NOT successfully verified in ${TERRAFORM_HOME}: $TERRAFORM_VERSION_NAMES" >&2
			fi
		else
			echo "Terraform version ${tf_version_name} not found in generated context" >&2
		fi
		echo "Terraform installation verification for major version $tf_version_name failed" >&2
		echo "TERRAFORM_HOME: ${TERRAFORM_HOME}" >&2
		return 1
	done
	return 0
}

#
# The first argument is the TF version
# Pipelines install just one version, TF service installs all
#
# The second argument is the installation location
#
#
install_terraform_versions "$1" "${2-/usr/local/lib}" 

#
# Ingest the generated file
#
. ${TERRAFORM_ENV_SH}

#
# Self test
#
self_test || { echo "Self test failed" >&2; exit 1; }
