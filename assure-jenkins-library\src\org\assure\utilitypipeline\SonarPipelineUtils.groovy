package org.assure.utilitypipeline

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.Utils
import org.pdxc.util.ValuesUtils
import org.pdxc.util.FileUtils
import org.pdxc.util.DefaultConfiguration
import org.pdxc.notification.MailNotification
import groovy.json.JsonOutput

/**
 * This class contains utility methods for the SonarQube pipeline.
 */
class SonarPipelineUtils {

    private SonarPipelineUtils() {}

    def context = JenkinsContext.getContext()

    /**
     * This method is used to set the Sonar Docker agent.
     * @param dockerPath The path to the Dockerfile.
     * @param dockerTargetName The name of the Dockerfile.
     */
    void setSonarDockerAgent(String dockerPath, String dockerTargetName) {
        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        def dockerFile
        if (exists) {
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: "conf.yml"
            def pipelineType = config.pipelineType
            def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)
            dockerFile = context.libraryResource "custom/${projectType}-project/${dockerTargetName}"   
        }
        context.writeFile file: dockerTargetName, text: dockerFile
    }

    /**
     * This method is used to set the default configuration for the pipeline.
     * @param pipelineName The name of the pipeline.
     * @return The configuration data.
     */
    def pipelineInfoSteps(String pipelineName) {
        def conf = context.libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
        context.writeFile file: "${pipelineName}-conf.yml", text: conf
        def defaultConfigYml = context.readYaml file: "${pipelineName}-conf.yml"
        def repoData = context.readYaml file: 'conf.yml'

        def configData = defaultConfigYml + repoData

        context.println 'Loaded configuration values: \n\n' + JsonOutput.prettyPrint(JsonOutput.toJson(configData))
        context.writeYaml file: 'conf.yml', data: configData, overwrite: true
        // Create log directory
        context.sh(script: "mkdir -p logs", label: 'Creating logs directory...')

        return configData
    }

    /**
     * This method is used to get the list of repositories from the configuration file.
     * @param listName The name of the list.
     * @return The list of repositories.
     */
    def getRepositories(def listName) {
        def repositories
        listName = ValuesUtils.removeStartEndChars(listName, '/', true, true)
        def listPath = "${listName}.yml"
        def exists = context.fileExists listPath
        if (!exists) {
            context.error("-- ❌️ -- No list file was selected. Please check your configuration")
        } else {
            context.echo("📋 Preparing the repository list...")
            def repoData = context.readYaml file: listPath
            repositories = ValuesUtils.getVariableArrayList(repoData, 'repositories', 'update')
            return repositories
        }
    }

    /**
     * This method is used to check if the package.json file exists in the repository.
     * @param repoName The name of the repository.
     * @return The name of the repository or the name of the package.json file.
     */
    def checkForPackageJsonFile(def repoName) { 
        def currentName
        repoName = ValuesUtils.removeStartEndChars(repoName, '/', true, true)
        def packageJsonFile = context.findFiles(glob: "${repoName}/**/package.json")

        // If the file exists save the path to a variable
        if (packageJsonFile.size() > 0) {
            context.echo("📋 Found package.json file in ${repoName} repository")
            def attr = 'name'
            def packageJsonPath = packageJsonFile[0].path
            currentName = FileUtils.getAttributeFromJsonFile(packageJsonPath, attr)
            currentName = currentName.replaceAll('\\.', '')
        } else {
            return repoName
        }
        return currentName
    }

    /**
     * This method is used to clean the list of repositories.
     * @param repositories The list of repositories.
     * @return The cleaned list of repositories and the failed repositories.
     */
    def cleanRepositoriesList(def repositories) {
        def cleanedRepositories = []
        def failedRepositories = []

        context.echo("📋 Checking the repository list...")

        repositories.each { repo ->
            def modifiedRepo = [:]
            def existingRepo
            if (repo.repoName != null && repo.repoName != '') {
                def scanOrganizationName = (repo.organization != null && repo.organization != '') ? repo.organization : 'assure'
                def scanBranchName = (repo.branch != null && repo.branch != '') ? repo.branch : 'master'

                modifiedRepo.put('repoName', repo.repoName)
                modifiedRepo.put('organization', scanOrganizationName)
                modifiedRepo.put('branch', scanBranchName)

                cleanedRepositories.add(modifiedRepo)    
            } else {
                failedRepositories.add(repo)  
            }
        }
        return [cleanedRepositories, failedRepositories]
    }

    /**
     * This method is used to check if the repository is a JavaScript project.
     * @param repoName The name of the repository.
     * @return The name of the package.json file or the name of the repository.
     */
    def checkJavascriptLanguage(def repoName) {
        def packageJsonName = checkForPackageJsonFile(repoName)
        if (packageJsonName != null) {
            return packageJsonName
        } else {
            return repoName
        }
    }

    /**
     * This method is used to clone the repository.
     * @param configData The configuration data.
     * @param repository The repository data.
     */
    void cloneRepository(def configData, def repository) {
        def branch = repository.branch 
        def organization = repository.organization
        def repoName = repository.repoName
        context.withCredentials([context.usernamePassword(credentialsId: configData.gitHubCredential, passwordVariable:'GITHUB_PASSWORD', usernameVariable:'GITHUB_USER')]) {
            context.sh(script: """
            git clone -b ${branch} https://${context.env.GITHUB_USER}:${context.env.GITHUB_PASSWORD}@${DefaultConfiguration.PDXC_GITHUB_HOST}/${organization}/${repoName}.git ${repoName}                                    
        """, label: 'Cloning the repository...')
        }
    }

    /**
     * This method is used to compress and archive the logs.
     * @param currentTime The current time.
     */
    void compressAndArchiveLogs(def currentTime) {
        context.echo("📁 Compressing and Archiving logs...")

        def logFileName = "SonarQube_logs-${currentTime}.zip"
        context.zip(glob: "", zipFile: "${logFileName}" , dir: "./logs/")
        context.archiveArtifacts artifacts: "${logFileName}"
    }

    /**
     * This method is used to send the email notifications.
     * @param emailRecipients The list of email recipients.
     * @param successScans The list of successful scans.
     * @param failedScans The list of failed scans.
     * @param currentTime The current time.
     * @param coverage The coverage data.
     */
    void sendEmailNotifications (List<String> emailRecipients, def successScans, def failedScans, def currentTime, def coverage) {
        context.echo("📧 Sending the generated logs...")
        // Builds the mail and sends the notification
        if (emailRecipients != null && emailRecipients.size() > 0) {
            MailNotification mail = new MailNotification()

            String emailTo = emailRecipients.join(',')
            def emailFrom = "<EMAIL>"
            def subject = "-- 🤖 -- SonarQube Utility Pipeline Scans Results"
            def emailBody = composeNotificationEmail( successScans, failedScans, coverage)
            def reportFile = "SonarQube_logs-${currentTime}.zip"

            mail.configureMail(emailTo, emailFrom, subject, emailBody, reportFile)
            mail.send()   
        } else {
            context.echo("-- ⚠️ -- No email recipients were selected. Check your config file.")
        }
    }

    /**
     * This method is used to compose the email notification.
     * @param successScans The list of successful scans.
     * @param failedScans The list of failed scans.
     * @param coverage The coverage data.
     * @return The email body.
     */
    def composeNotificationEmail(def successScans, def failedScans, def coverage) {
        def successStyle = "color:green"
        def failedStyle = "color:red"

        def header = '''
                    <!DOCTYPE html><html lang="en"><head>
                    <meta charset="UTF-8" /><meta http-equiv="X-UA-Compatible" content="IE=edge" /><meta name="viewport" content="width=device-width, initial-scale=1.0" /><title>SonarQube Scan Results</title></head><style>
                    #emailTable, #emailFooter {border: 0;height: 100%;width: 100%;border-spacing: 0;padding: 0;}
                    #emailContainer {border-collapse: collapse;font-family: Tahoma, Geneva, sans-serif;}
                    #emailContainer td {padding: 15px;}
                    #emailContainer thead th {background-color: #54585d;color: #ffffff;font-weight: bold;font-size: 12px;border: 1px solid #54585d; text-align: center; padding: 15px;}
                    #emailContainer tbody td {color: #636363;border: 1px solid #dddfe1;}
                    #emailContainer tbody tr {background-color: #f9fafb; border-bottom: 1px solid grey; padding: 0; text-align: center;}
                    #emailContainer tbody tr:nth-child(odd) {background-color: #ffffff;}
                    </style><body><table id="emailTable">
                    <th><h2>SonarQube Utility Pipeline Scans Results</h2></th>
                    <tr><td align="center" valign="top">
                    <p>Below you can see the scans of the selected repositories executed in the SonarQube Utility Pipeline.</p>
                    <table id="emailContainer"><thead><tr>
                    <th>🗃️ Scanned repositories</th>
                    <th>📜 Status</th>
                    <th>📖 Coverage</th>
                    </tr></thead><tbody>
                     '''
        def footer = '''
                    </tbody></table></td></tr></table></td><br />
                    <table border="0" cellpadding="0" cellspacing="0" width="100%" id="emailFooter"><tr>
                    <td align="center" valign="top">
                    <i> This is a message generated automatically by the SonarQube Utility Pipeline,
                    please do not reply to this email.</i><br /><br />
                    <i> If you need assistance or clarification on any of the topics covered in this
                    email, please use the established channels to contact the <b>Automation team</b>.
                    </i></td></tr></table></body></html>
                     '''

        def body = ''

        if (successScans != null && successScans.size() > 0) {
            successScans.each { scan ->
                def repoCoverage = coverage[scan].toFloat()
                body += """
                    <tr>
                    <td>${scan}</td>
                    <td style='${successStyle}'>SUCCESS</td>
                """
                if (repoCoverage != null && repoCoverage >= 85) {
                    body += "<td style='${successStyle}'>${repoCoverage}%</td>"
                } else {
                    body += "<td style='${failedStyle}'>${repoCoverage}%</td>"
                }
            }
        }
        if (failedScans != null && failedScans.size() > 0) {
            failedScans.each { scan ->
                def repoCoverage = coverage[scan].toFloat()
                body += """
                    <tr>
                    <td>${scan}</td>
                    <td style='${failedStyle}'>FAILED</td>
                """
                if (repoCoverage != null && repoCoverage >= 85) {
                    body += "<td style='${successStyle}'>${repoCoverage}%</td>"
                } else {
                    body += "<td style='${failedStyle}'>${repoCoverage}%</td>"
                }
            }
        }
        body += "</tr>"
        
        def message = header + body + footer
        return message
    }

    /**
     * This method is used to compose the status table.
     * @param responseProjectStatus The project status response.
     * @param projectStatus The project status.
     * @return The status table.
     */
    def composeStatus(def responseProjectStatus, def projectStatus) {
        def projectColor = (responseProjectStatus?.status != 'ERROR') ? 'green' : 'red'

        def statusElement = """ 
            <div class="tableTitle">Status</div>
            <div class="statusTable" id="status">
                <div class="row status ${projectColor}">
                    <div class="cell">${projectStatus}</div>
                </div>
            </div>
            """
        return statusElement
    }

    /**
     * This method is used to compose the overall status table.
     * @param overAllResponse The overall response.
     * @param responseProjectStatus The project status response.
     * @param coverage The coverage data.
     * @return The overall status table.
     */
    def composeOverallStatus(def overAllResponse, def responseProjectStatus, def coverage) {
        def bugs = overAllResponse.component.measures.find { measure -> measure.metric == 'bugs' }?.value
        def codeSmells = overAllResponse.component.measures.find { measure -> measure.metric == 'code_smells' }?.value            
        def vulnerabilities = overAllResponse.component.measures.find { measure -> measure.metric == 'vulnerabilities' }?.value            
        def securityHotspots = overAllResponse.component.measures.find { measure -> measure.metric == 'security_hotspots' }?.value            
        def duplicatedLines = overAllResponse.component.measures.find { measure -> measure.metric == 'duplicated_lines_density' }?.value
        def securityHotspotsErrorThreshold = overAllResponse.metrics.find { metric -> metric.key == 'security_hotspots_reviewed'}?.bestValue
        // def coverageErrorThreshold = responseProjectStatus?.conditions.find { conditions -> conditions.metricKey == 'new_coverage' }?.errorThreshold
        // def duplicatedLinesErrorThreshold = responseProjectStatus?.conditions.find { conditions -> conditions.metricKey == 'new_duplicated_lines_density' }?.errorThreshold

        def successStyle = "green-text"
        def failedStyle = "red-text"

        def coverageSuccessStyle = (coverage.toFloat() >= 85) ? successStyle : failedStyle
        def duplicatedLinesSuccessStyle = (duplicatedLines.toFloat() <= 3) ? successStyle : failedStyle

        def overAllTable = """
            <div class="repoTitle">Overall Status</div>
            <div class="table" id="overview">
            <div class="row header purple">
                <div class="cell centeredText">Bugs</div>
                <div class="cell centeredText">Code Smells</div>
                <div class="cell centeredText">Vulnerabilities</div>
                <div class="cell centeredText">Security Hotspots</div>
                <div class="cell centeredText">Coverage</div>
                <div class="cell centeredText">Duplicated Lines</div>
            </div>
        """

        overAllTable += """
                <div class="row">
                    <div class="cell centeredText">${bugs}</div>
                    <div class="cell centeredText">${codeSmells}</div>
                    <div class="cell centeredText">${vulnerabilities}</div>
                    <div class="cell centeredText">${securityHotspots}</div>
                    <div class="cell centeredText ${coverageSuccessStyle}">${coverage}%</div>
                    <div class="cell centeredText ${duplicatedLinesSuccessStyle}">${duplicatedLines}%</div>
                </div>
            </div>
        """
        return overAllTable         
    }

    /**
     * This method is used to compose the bugs table.
     * @param bugsResponse The bugs response.
     * @return The bugs table.
     */
    def composeBugsElement(def bugsResponse) {
        def bugsElement = """
            <div class="tableTitle">Bugs</div>
            <div class="table" id="bugs">
            <div class="row header red">
                <div class="cell">Component</div>
                <div class="cell">Line</div>
                <div class="cell">Message</div>
                <div class="cell">Severity</div>
            </div>
        """

        bugsResponse.issues[0].each { issue ->
            String issueComponent = issue.component
            String issueLine = (issue.line != null) ? issue.line : 'n/a'
            String issueMessage = issue.message
            String issueSeverity = issue.severity

            bugsElement += """
                <div class="row">
                    <div class="cell">${issueComponent}</div>
                    <div class="cell">${issueLine}</div>
                    <div class="cell">${issueMessage}</div>
                    <div class="cell">${issueSeverity}</div>
                </div>
            """
        }

        bugsElement += """
            </div>
        """
        return bugsElement
    }

    /**
     * This method is used to compose the code smells table.
     * @param codeSmellResponse The code smells response.
     * @return The code smells table.
     */
    def composeCodeSmellsElement(def codeSmellResponse) {
        def codeSmellsElement = """
            <div class="tableTitle">Code Smells</div>
            <div class="table" id="code-smells">
            <div class="row header green">
                <div class="cell">Component</div>
                <div class="cell">Line</div>
                <div class="cell">Message</div>
                <div class="cell">Severity</div>
            </div>
        """
        codeSmellResponse.issues[0].each { issue ->
            String issueComponent = issue.component
            String issueLine = (issue.line != null) ? issue.line : 'n/a'
            String issueMessage = issue.message
            String issueSeverity = issue.severity

            codeSmellsElement += """
                <div class="row">
                    <div class="cell">${issueComponent}</div>
                    <div class="cell">${issueLine}</div>
                    <div class="cell">${issueMessage}</div>
                    <div class="cell">${issueSeverity}</div>
                </div>
            """
        }
        codeSmellsElement += """
            </div>
        """
        return codeSmellsElement
    }

    /**
     * This method is used to compose the vulnerabilities table.
     * @param vulnerabilitiesResponse The vulnerabilities response.
     * @return The vulnerabilities table.
     */
    def composeVulnerabilitiesElement(def vulnerabilitiesResponse) {
        def vulnerabilitiesElement = """
            <div class="tableTitle">Vulnerabilities</div>
            <div class="table" id="Vulnerabilities">
            <div class="row header blue">
                <div class="cell">Component</div>
                <div class="cell">Line</div>
                <div class="cell">Message</div>
                <div class="cell">Severity</div>
            </div>
        """
        vulnerabilitiesResponse.issues[0].each { issue ->
            String issueComponent = issue.component
            String issueLine = (issue.line != null) ? issue.line : 'n/a'
            String issueMessage = issue.message
            String issueSeverity = issue.severity

            vulnerabilitiesElement += """ 
                <div class="row">
                    <div class="cell">${issueComponent}</div>
                    <div class="cell">${issueLine}</div>
                    <div class="cell">${issueMessage}</div>
                    <div class="cell">${issueSeverity}</div>
                </div>
            """
        }
        vulnerabilitiesElement += """
            </div>
        """
        return vulnerabilitiesElement
    }

    /**
     * This method is used to compose the security hotspots table.
     * @param securityHotspotsResponse The security hotspots response.
     * @return The security hotspots table.
     */
    def composeSecurityHotspotsElement(def securityHotspotsResponse) {
        def securityHotspotsElement = """
            <div class="tableTitle">Security Hotspots</div>
            <div class="table" id="securityHotspots">
            <div class="row header orange">
                <div class="cell">Component</div>
                <div class="cell">Line</div>
                <div class="cell">Message</div>
            </div>
        """

        securityHotspotsResponse.hotspots[0].each { hotspots ->
            String hotspotsComponent = hotspots.component
            String hotspotsLine = (hotspots.line != null) ? hotspots.line : 'n/a'
            String hotspotsMessage = hotspots.message

            securityHotspotsElement += """
                <div class="row">
                    <div class="cell">${hotspotsComponent}</div>
                    <div class="cell">${hotspotsLine}</div>
                    <div class="cell">${hotspotsMessage}</div>
                </div>
            """
        }
        securityHotspotsElement += """
            </div>
        """
        return securityHotspotsElement
    }

    /**
     * This method is used to compose the footer of the report.
     * @return The footer of the report.
     */
    def composeFooter() { 
        def footer = """
                    <footer>
                        <div class="footer-text">Report generated by <b class="purple-text">Assure SonarQube Utility Pipeline</b></div>
                    </footer>
                </div>
            </html>
                    """
        return footer
    }

    /**
     * This method is used to compose the header of the report.
     * @param componentName The name of the component.
     * @return The header of the report.
     */
    def composeHeader(def componentName) { 
        def styles = """.status,body{font-size:14px}.header-text,.status{font-weight:700;margin-bottom:20px}.statusTable,.table{box-shadow:0 1px 3px rgba(0,0,0,.2);display:table}body{font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;line-height:20px;font-weight:400;color:#3b3b3b;-webkit-font-smoothing:antialiased;background:#e6e3e3}.centeredText{text-align:center}.statusTable{width:20%}.wrapper{margin:0 auto;padding:40px;max-width:80%}.repoTitle,footer{text-align:center;padding:10px}.status{text-align:center;color:#fff}.header-text{font-size:24px;text-align:center;color:#333}.header-text-subtitle{font-size:16px;font-weight:700;text-align:center;color:#666;margin-bottom:20px}.repoTitle,.tableTitle{font-size:18px;font-weight:700}.table{margin:0 0 40px;width:100%}.repoTitle{color:#333}.tableTitle{text-align:left;color:#333;margin-bottom:10px}.row{display:table-row;background:#f6f6f6}.row:nth-of-type(odd){background:#e9e9e9}.row.header{font-weight:900;color:#fff}.row.red{background:#ea6153}.row.green{background:#27ae60}.row.blue{background:#2980b9}.row.purple{background:#9b59b6}.row.orange{background:#e67e22}.purple-text{color:#9b59b6}.green-text{color:#27ae60}.red-text{color:#ea6153;font-weight:700}.cell{padding:6px 12px;display:table-cell}@media screen and (max-width:580px){body{font-size:16px;line-height:22px}.table{display:block}.row{padding:14px 0 7px;display:block}.row.header{padding:0;height:6px}.row.header .cell{display:none}.row .cell{margin-bottom:10px}.row .cell:before{margin-bottom:3px;content:attr(data-title);min-width:98px;font-size:10px;line-height:10px;font-weight:700;text-transform:uppercase;color:#969696;display:block}.cell{padding:2px 16px;display:block}}footer{background-color:#f1f1f1;font-size:14px;color:#333}.footer-text{margin:0;font-size:14px}"""
        def header = """
            <!DOCTYPE html>
            <html lang="en" xml:lang="en">
            <head>
                <meta charset="UTF-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <title>SonarQube Report</title>
                <style>${styles}</style>
            </head>
            <div class="wrapper">
                <header>
                    <div class="header-text">SonarQube Report</div>
                    <div class="header-text-subtitle purple-text">${componentName}</div>
                </header>
        """
        return header
    }

    /**
     * This method is used to generate the SonarQube report.
     * @param securityHotspotsResponse The security hotspots response.
     * @param bugsResponse The bugs response.
     * @param overAllResponse The overall response.
     * @param projectResponse The project response.
     * @param codeSmellResponse The code smell response.
     * @param vulnerabilitiesResponse The vulnerabilities response.
     * @return The coverage data.
     */
    def sonarReport (def securityHotspotsResponse, def bugsResponse, def overAllResponse, def projectResponse, def codeSmellResponse, def vulnerabilitiesResponse) {
        def repoFinalCoverage

        if (securityHotspotsResponse.hotspots != null && securityHotspotsResponse.hotspots.size() > 0
            || bugsResponse.issues != null && bugsResponse.issues.size() > 0
            || codeSmellResponse.issues != null && codeSmellResponse.issues.size() > 0
            || vulnerabilitiesResponse.issues != null && vulnerabilitiesResponse.issues.size() > 0
            || overAllResponse.component != null && overAllResponse.component.measures.size() > 0
            || projectResponse.projectStatus != null && projectResponse.projectStatus.conditions != null && projectResponse.projectStatus.conditions.size() > 0) {

            def responseProjectStatus = projectResponse.projectStatus
            def projectStatus = (responseProjectStatus?.status != 'ERROR') ? 'PASSED' : 'FAILED'
            def componentName = overAllResponse.component.name
            def coverage = overAllResponse.component.measures.find { measure -> measure.metric == 'coverage' }?.value            

            def header = composeHeader(componentName)
            def statusTable = composeStatus(responseProjectStatus, projectStatus)
            def overAllTable = composeOverallStatus(overAllResponse, projectResponse, coverage)
            def bugsTable = composeBugsElement(bugsResponse)
            def codeSmellsTable = composeCodeSmellsElement(codeSmellResponse)
            def vulnerabilitiesTable = composeVulnerabilitiesElement(vulnerabilitiesResponse)
            def securityHotspotsTable = composeSecurityHotspotsElement(securityHotspotsResponse)
            def footer = composeFooter()

            def htmlReport = header + statusTable + overAllTable + bugsTable + codeSmellsTable + vulnerabilitiesTable + securityHotspotsTable + footer

            def sonarQubeReport = "sonar-report-${componentName}-${projectStatus}.html"
            def logsPath = "logs/"

            context.writeFile file: "${logsPath}${sonarQubeReport}", text: htmlReport
            repoFinalCoverage = coverage
        }
        return repoFinalCoverage
    }

    /**
     * This method is used to get the SonarQube issues.
     * @param path The path to the SonarQube API.
     * @param sonarHost The SonarQube host.
     * @param encodedToken The encoded token.
     * @param type The type of issue.
     * @return The list of issues.
     */
    def getSonarIssues(def path, def sonarHost, def encodedToken, def type) {
        try {
            def combinedIssues = []
            int issuesPerPage = 500
            int actualPage = 1
            int totalPages = 1
            def status = "OPEN"
            def sortField = "SEVERITY"
            for(actualPage; actualPage<=totalPages; actualPage++) {
                def apiPath = "${path}&s=${sortField}&statuses=${status}&ps=${issuesPerPage}&p=${actualPage}&asc=false"
                def issues = context.httpRequest url: sonarHost + apiPath, validResponseCodes: '200:599',  customHeaders: [[name: 'Authorization', value: "Basic ${encodedToken}", maskValue: true]]
                def issuesJson = context.readJSON text: issues.content
                int totalIssues

                switch(type) {
                    case "hotspots":
                        (issuesJson.paging != null) ? (totalIssues = issuesJson.paging.total) : (totalIssues = 0)
                    break;
                    case "vulnerabilities":
                        (issuesJson.total != null) ? (totalIssues = issuesJson.total) : (totalIssues = 0)
                    break;
                    case "bugs":
                        (issuesJson.total != null) ? (totalIssues = issuesJson.total) : (totalIssues = 0)
                    break;
                    case "codeSmell":
                        (issuesJson.total != null) ? (totalIssues = issuesJson.total) : (totalIssues = 0)
                    break;
                    default:
                        totalIssues = 0
                }

                double resultDiv = totalIssues.div(issuesPerPage)
                double pages = totalPages

                combinedIssues.add(issuesJson)

                if (resultDiv > pages) {
                    totalPages++
                }
            }
            return combinedIssues
        } catch (e) {
            context.error("-- ⚠️ -- SonarQube report 🛸 creation FAILED ❗❗ Something went wrong during the analysis")
        }
    }

    /**
     * This method is used to compose the SonarQube report.
     * @param sonarHost The SonarQube host.
     * @param currentName The name of the repository.
     * @param encodedToken The encoded token.
     * @return The coverage data.
     */
    def composeSonarReport(def sonarHost, def currentName, def encodedToken) {
        def repoCoverage
        def keyword = 'ASR-'
        currentName = keyword + currentName

        def overallPath = "/api/measures/component?additionalFields=period%2Cmetrics&component=${currentName}&metricKeys=alert_status%2Cquality_gate_details%2Cbugs%2Cnew_bugs%2Creliability_rating%2Cnew_reliability_rating%2Cvulnerabilities%2Cnew_vulnerabilities%2Csecurity_rating%2Cnew_security_rating%2Csecurity_hotspots%2Cnew_security_hotspots%2Csecurity_hotspots_reviewed%2Cnew_security_hotspots_reviewed%2Csecurity_review_rating%2Cnew_security_review_rating%2Ccode_smells%2Cnew_code_smells%2Csqale_rating%2Cnew_maintainability_rating%2Csqale_index%2Cnew_technical_debt%2Ccoverage%2Cnew_coverage%2Clines_to_cover%2Cnew_lines_to_cover%2Ctests%2Cduplicated_lines_density%2Cnew_duplicated_lines_density%2Cduplicated_blocks%2Cncloc%2Cncloc_language_distribution%2Cprojects%2Clines%2Cnew_lines"
        def overAllResponse = context.httpRequest url: sonarHost + overallPath, validResponseCodes: '200:599',  customHeaders: [[name: 'Authorization', value: "Basic ${encodedToken}", maskValue: true]]                                                            
        def overAllResponseJson = context.readJSON text: overAllResponse.content

        def projectPath = "/api/qualitygates/project_status?projectKey=${currentName}"
        def projectResponse = context.httpRequest url: sonarHost + projectPath, validResponseCodes: '200:599',  customHeaders: [[name: 'Authorization', value: "Basic ${encodedToken}", maskValue: true]]                                                            
        def projectResponseJson = context.readJSON text: projectResponse.content

        def bugsPath = "/api/issues/search?types=BUG&componentKeys=${currentName}"
        def bugsResponse = getSonarIssues(bugsPath, sonarHost, encodedToken, 'bugs')

        def codeSmellPath = "/api/issues/search?types=CODE_SMELL&componentKeys=${currentName}"
        def codeSmellResponse = getSonarIssues(codeSmellPath, sonarHost, encodedToken, 'codeSmell')

        def vulnerabilitiesResponsePath = "/api/issues/search?types=VULNERABILITY&componentKeys=${currentName}"
        def vulnerabilitiesResponse = getSonarIssues(vulnerabilitiesResponsePath, sonarHost, encodedToken, 'vulnerabilities')

        def securityHotspotsPath = "/api/hotspots/search?projectKey=${currentName}"
        def securityHotspotsResponse = getSonarIssues(securityHotspotsPath, sonarHost, encodedToken, 'hotspots')

        try {
            repoCoverage = sonarReport(securityHotspotsResponse, bugsResponse, overAllResponseJson, projectResponseJson, codeSmellResponse, vulnerabilitiesResponse)
        } catch (Exception e) {
            context.echo("-- ❌ -- 📂 No Sonar Report was generated due to an error. Please check the logs")
        }
        return repoCoverage
    }

    /**
     * This method is used to check the quality gate of the repository.
     * @param repositoryName The name of the repository.
     * @return True if the quality gate is passed, false otherwise.
     */
    def checkQualityGate(def repositoryName) {
        def currentName = repositoryName
        def keyword = 'ASR-'
        currentName = keyword + currentName

        def reportFile = "sonar-report-${currentName}-PASSED.html"
        def reportPath = "logs/${reportFile}"
        def exists = context.fileExists reportPath

        if (exists) {
            context.echo("📋 Quality gate PASSED for ${repositoryName} repository")
            return true
        } else {
            context.echo("📋 Quality gate FAILED for ${repositoryName} repository")
            return false
        }
    }

    /**
     * This method is used to get the SonarQube report.
     * @param repositoryName The name of the repository.
     * @return The coverage data.
     */
    def getSonarReport(def repositoryName) {
        def repoCoverage
        def currentName = repositoryName
        context.withCredentials([context.string(credentialsId:'ASSURE-SONAR-HOST', variable:'SONARHOST')]) {
            context.withCredentials([context.string(credentialsId:'ASSURE-SONAR-TOKEN', variable:'SONARTOKEN')]) {
                def sonarHost = context.env.SONARHOST
                def sonarToken = context.env.SONARTOKEN
                def encodedToken = context.sh (
                    script: "echo -n '${context.env.SONARTOKEN}:' | base64", 
                    returnStdout: true).trim()

                def path = '/api/users/identity_providers'
                def response = context.httpRequest url: sonarHost + path, validResponseCodes: '200:599'

                if (response.status != 200) {
                    context.error("-- ❌️ -- Something went wrong while trying to connect to SonarQube. Please review the logs.")
                } else {
                    context.echo("📋 Generating the SonarQube report...")
                    repoCoverage = composeSonarReport(sonarHost, currentName, encodedToken)
                    context.echo("📋 SonarQube report generated successfully.")
                }
            }
        }
        return repoCoverage
    }
}