data "template_file" "assure_aat_pca_config_lambda" {
   template = "${file("${path.module}/templates/assure_aat_pca_config_lambda_handler_policy.json")}"
   vars = {
      s3_bucket_arn = module.pca_bucket.bucket_arn
      fts_bucket_arn = "arn:aws:s3:::${data.aws_ssm_parameter.fts_bucket_name.value}"
      training_status_table_arn = module.training_status_table.table_arn
      reports_table_arn = module.reports_table.table_arn
   }
}
data "template_file" "assure_aat_pca_preprocessing_handler_lambda" {
   template = "${file("${path.module}/templates/assure_aat_pca_preprocessing_handler_lambda_policy.json")}"
   vars = {
      aws_region = var.aws_region
      aws_account = local.aws_target_account
      s3_bucket_arn = module.pca_bucket.bucket_arn
      training_status_table_arn = module.training_status_table.table_arn
      reports_table_arn = module.reports_table.table_arn
      sagemaker_region = var.sagemaker_image_aws_region
      sagemaker_account_number = var.aws_account_id
      Sagemaker_endpoint_name = "${local.environment_specific_resource_pre_name}-${var.sagemaker_endpoint_name}"
   }
}

data "template_file" "assure_aat_pca_config_event_handler_lambda" {
   template = "${file("${path.module}/templates/assure_aat_pca_config_event_lambda_policy.json")}"
   vars = {
      s3_bucket_arn = module.pca_bucket.bucket_arn
      training_status_table_arn = module.training_status_table.table_arn
      pca_preprocessing_lambda_arn = module.assure_aat_pca_preprocessing_handler_lambda.lambda_arn
      reports_table_arn = module.reports_table.table_arn
   }
}
