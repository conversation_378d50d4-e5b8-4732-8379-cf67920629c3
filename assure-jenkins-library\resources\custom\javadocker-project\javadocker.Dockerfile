FROM maven:0.0.0

ENV PIP_BREAK_SYSTEM_PACKAGES=1

RUN apt-get update \
  && apt-get install -y curl wget unzip bash gnupg docker.io

RUN wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64

RUN chmod a+x /usr/local/bin/yq

#Checkov
RUN apt-get update \
    && apt-get install -y python3-pip \
    && pip3 install --upgrade setuptools \
    && pip3 install -U checkov \
    && rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*