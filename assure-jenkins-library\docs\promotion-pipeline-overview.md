# Promotion Pipeline Overview

- [1. Promotion Pipeline steps description](#1-promotion-pipeline-steps-description)
  - [1.1. Stage 1: Set up and Configuration](#11-stage-1-set-up-and-configuration)
  - [1.2. Stage 2: Get Artifacts Data (AQL)](#12-stage-2-get-artifacts-data-aql)
  - [1.3. Stage 3: Manage Target Environments](#13-stage-3-manage-target-environments)
  - [1.4. Stage 4: Deploy and Test](#14-stage-4-deploy-and-test)
    - [1.4.1. DEV Stage](#141-dev-stage)
    - [1.4.2. TEST Stage](#142-test-stage)
    - [1.4.3. STAGING Stage](#143-staging-stage)
    - [1.4.4. PRODUCTION Stage](#144-production-stage)
- [1.5. Closure and Summary](#15-closure-and-summary)
- [1.6. Pipeline Notifications](#16-pipeline-notifications)
- [2. Promotion Pipeline execution](#promotion-pipeline-overview)

***

## 1. Promotion Pipeline steps description

This section describes the different steps performed by the Promotion Pipeline.

>
> Note: Discarding an artifact during the Promotion Pipeline process means that it is not processed, but no further
> action will be performed on it, so it will be taken again in the next iteration of the pipeline. It is important to
> track this circumstance to avoid having "garbage" in Artifactory or extra processing in the Promotion Pipeline. If this
> situation is repeated for many artifacts, it could mean that:
>
>   1. AQL query to select artifacts is wrong or not fully accurate
>   2. Artifactory path for some of the artifacts is not correct and their corresponding Push Pipeline should be
 reviewed
>

### 1.1. Stage 1: Set up and Configuration

All the customizable values are defined in a file config.yml (see customizable values in [Promotion Pipeline Customization Example
](samples/pipelinepromotion/conf.yml)). In this step, this file is loaded and its values printed for reference.

Additionally, other required elements and variables are initialized as they will be used across the pipeline (i.e
. the login required to consume the Environment Service API)

### 1.2. Stage 2: Get Artifacts Data (AQL)

> Please see the [DXC Assure Platform Lifecycle documentation](https://github.dxc.com/assure/assure-platform/blob/master/docs/processes/PLATFORM_LIFECYCLE.md) for
> details on the different parameters used to define a "to be deployed" element.

The objective of this stage is to gather a complete list of artifacts and components that should be ready to be
deployed to a new environment, tested and promoted or rejected based on the results. This list will be input used on the next stages.

Based on the custom values configured to compose the Artifactory query ([see AQL syntax](https://jfrog.com/help/r/jfrog-artifactory-documentation/artifactory-query-language)) retrieve list of components / artifacts on a "to be deployed"
status from Artifactory through its REST API.

The data retrieved from Artifactory is treated to get a single list of Artifacts that will be used on the next stages to calculate and perform the required actions. This data treatment includes **discarding any artifact that does not
fulfill all the requirements, i.e. missing property or invalid values**.

For each of the artifacts / components retrieved, the first step, apart from the former validation, is to "calculate"
to which deployment package it belongs. This information is require because most of the actions to be performed from this point
are based on the deployment package including the main operation that is the deployment.

The list of valid and discarded artifacts can be found in the pipeline, displaying the following items:
![Valid/Discarded artifacts](img/jenkins_valid_discarded_arts.png)

### 1.3. Stage 3: Manage Target Environments

Having already a list of artifacts, the next step is to decide where do they need to be deployed. This data will
depend on:

- **Status**: Again, based on [DXC Assure Platform Lifecycle documentation](https://github.dxc.com/assure/assure-platform),
each status is mapped to certain level of the lifecycle promotion, so it allows the mapping to specific environment.

For each of the artifacts, based on its status, the mapping to a specific target environment
key is done. This mapping is configured in the conf.yml in the form of property:

```yml
   status_stage_mapping:
     - status: "status-1"
       stage: "environment-1"
```

Any artifact that cannot be mapped will be discarded and not processed in this iteration.

During this process, the target environments have been identified. However, there are some scenarios where
a target environment should be skipped and this would mean that its artifacts must be discarded. i.e. the deployment package is not
available in the environment

![DP discarded](img/jenkins_dp_discarded.png)

The scenarios where a target environment should be skipped are:

- Environment is locked: Environment Service allows to lock an environment from being modified. This lock means that
no changes should be done on it and that includes deploying a new version of an artifact. The lock is indicated by a
property at Environment Service level: *"is_locked"*.

- Environment is already under deployment: it might occur that we need to deploy an artifact into an environment
where currently there is already a deployment or a test in progress. In order to avoid conflicts, on these cases, we
will consider the environment as busy and not deploy there. This condition is marked through a property at
Environment Service level: *"is_busy"*.

So this stage will perform the checks, by calling Environment Service to retrieve the relevant data of each specific  
target environment, to validate that the environment is really available. In case that environment is locked or busy,
all the artifacts that were to be deployed on it will be discarded.

If the environment is available, then we know that we are ready to start the deployment process and we will perform
the first actions

- **Block the environment**: this is done by setting the "is_busy" Environment Service parameter to True.

- **Block the artifacts**: Now that we know that the artifacts are going to be deployed, we must avoid that they are
picked by the next iteration of the deployment pipeline. This is done by updating one of their properties in
Artifactory: **"deploy-in-progress"**. This property is set to True for each of the artifacts in scope.

*Please notice that before this point, discarding an artifact, did not imply any change on Artifactory. As now one of
its properties has been modified, we need to ensure that this one is restored whatever happens to the artifact later.*

The outcome of this stage is a set of lists of Artifacts (Map), one for each target environment that has been
identified. This list will be showed in:

![Artifacts group by env](img/jenkins_arts_groupby_env.png)

### 1.4. Stage 4: Deploy and Test

This is a really complex stage as it contains the main logic of the promotion process and it implements different
types of scenarios depending on the actions to be performed.

For each target environment, an overall summary of the actions to be done in this stage are:

1. Deploy artifact
2. Test artifact
3. Update results:
    1. Step 3.1. Environment Service
    2. Step 3.2. Artifactory

However, we have defined 4 different types of scenarios and these default steps are different among them. The type of
scenario for each environment is customized in the config.yml.

#### 1.4.1. DEV Stage

This is thought for the Development stage where the objective is to deploy and test each artifact individually, so we
want to iterate through all the artifacts and deploy and test them before moving to the next one.

Steps:

1. Calculate (from configuration) what the success and failure target statuses for the artifacts are.
2. Iterate through all the artifacts for this target environment:
    1. Step 2.1. Perform artifact deployment by calling Environment Service refresh operation for the service that the artifact belongs to.
    2. Step 2.2. If deployment is successful, run the specific tests for this kind of environment
    3. Step 2.3. Update the test results for this deployment in Environment Service
    4. Step 2.4. Process test results (in Jenkins) for further reference
    5. Step 2.5. Update artifact status (success / failure) in Artifactory
    6. Step 2.6. Unblock artifact ("deploy-in-progress" to False) in Artifactory
3. After the last artifact iteration, unblock the environment in Environment Service (set "is_busy" to false)

#### 1.4.2. TEST Stage

This scenario is for the Test stages where the objective is still to deploy and test each artifact individually, but
with a slight difference: the tests run are not only for the deployed artifact, but all of the tests of the service
it belongs to.

Steps:

1. Calculate (from configuration) what the success and failure target statuses for the artifacts are.
2. Iterate through all the artifacts for this target environment:
    1. Step 2.1. Perform artifact deployment by calling Environment Service refresh operation for the service that the artifact belongs to.
    2. Step 2.2. If deployment is successful, run the specific tests for this kind of environment.
    3. Step 2.3. Update the test results for this deployment in Environment Service
    4. Step 2.4. Process test results (in Jenkins) for further reference
    5. Step 2.5. Update artifact status (success / failure) in Artifactory
    6. Step 2.6. Unblock artifact ("deploy-in-progress" to False) in Artifactory
3. After the last artifact iteration, unblock the environment in Environment Service (set "is_busy" to false)

#### 1.4.3. STAGING Stage

This would be the scenario where the two lifecycles (business and infrastructure) get together for the first time so
the objective is to test how every component interacts with the others and ensure that they can work together.

> Until this stage, we might have in scope of a single deployment more than one version of the same artifact and it
> did not cause any issue: we simply deployed one after the other and tested each of them independently. However, in
> this stage (and also in PRODUCTION Stage) we will be testing all the elements together so, in order to know whether
> a version works or not, we need to avoid deploying different versions of the same artifact. This means, that one of
> the steps will be a filter to just take the **older version** of the artifact and discard the rest that will be
> taken in later iterations of the Promotion Pipeline.

Steps:

1. Calculate (from configuration) what the success and failure target statuses for the artifacts are.
2. Filter list of artifacts to take single (older) version of the same artifact
    *Note: As target environment is blocked with this deployment, we do not unblock the discarded artifacts until the
     completion of this stage.
3. Deploy every artifact into the target environment
4. If deployment of every artifact is successful, run tests for all the services
5. If deployment of every artifact is successful, run End to End tests for all the services
6. Update test results in Environment Service for each deployment (if any deployment failed, mark all as failed)
7. Update all artifacts status (success / failure). All of them will have the same status.
8. Unblock all the artifacts, including those discarded on step 2.
9. Unblock the environment in Environment Service (set "is_busy" to false)

#### 1.4.4. PRODUCTION Stage

This would be higher environment in the lifecycle ecosystem and the intention is to have a Production like
environment with versions that are stable and working.

Steps are the same as for STAGING stage except in the tests.

Steps:

1. Calculate (from configuration) what the success and failure target statuses for the artifacts are.
2. Filter list of artifacts to take single (older) version of the same artifact
    *Note: As target environment is blocked with this deployment, we do not unblock the discarded artifacts until the
     completion of this stage.
3. Deploy every artifact into the target environment
4. If deployment of every artifact is successful, run End to End tests for all the services
5. If e2e test process was successfully completed, a new release using the Release Service will be created.
6. Update test results in Environment Service for each deployment (if any deployment failed, mark all as failed)
7. Update all artifacts status (success / failure). All of them will have the same status.
8. Unblock all the artifacts, including those discarded on step 2.
9. Unblock the environment in Environment Service (set "is_busy" to false)

### 1.5. Closure and Summary

This is a closing stage to manage results and perform any notification and set up based on the result.

> Given the possibility that the same artifact belongs to more than one service, it is potentially possible that it
> has to be deployed to more than one environment. This complicates part of the deployment and testing handling as it
> will not possible to mark an artifact for promotion until these two processes are completed for every environment
>. This makes that for those cases, the status update is not performed until reaching this stage, where all
> processing is completed and it can be validated what was the deploy and test result for an artifact in all
> environments.

Elements included in this stage:

- **Update status for "multi-environment" artifacts**: Given the possibility that the same artifact belongs to more
 than one service, it is potentially possible that it has to be deployed to more than one environment. This
 complicates part of the deployment and testing handling as it will not possible to mark an artifact for promotion
 until these two processes are completed for every environment. This makes that for those cases, the status update is
 not performed until reaching this stage, where all processing is completed and it can be validated what was the
  deploy and test result for an artifact in all environments.

- **Jenkins Pipeline Result**: Job result will be:
  - SUCCESS 🟢 --> Pipeline execution completed successfully. All the steps could be run without errors. It
     does not mean that all the retrieved artifacts have been deployed and tested successfully so they are promoted.
     It means that pipeline ran without errors but there might be artifacts discarded for any reason or target
     environments could be blocked.
  - UNSTABLE 🟡 --> Pipeline execution was completed but there were errors during the deployment stage. It
     is marked as warning and the errors in the deployment should be checked.
  - ERROR 🔴 --> Pipeline could not be completed. There was an unexpected error. Review is required and maybe
     the an artifact or environment could be on an undesired status.

### 1.6. Pipeline Notifications

This stage handles all the notification preparations regarding the status of the pipeline job. It creates and send two kind of notifications:

- **Email Notifications**
  - These notifications are sent to the email of the last committer/s and displays relevant information and links regarding the Promotion Pipeline status.

    **Email example:**

    ```text
        -- 🗃️ -- Deployment Package: example-deployment-package
        •	💾 Version: 1.0.0
        •	📜 Status: -- ✔️ -- Release service
        •	🏭 Release Service: Link to Release Service
        •	🚧 Pipeline: Jenkins Pipeline

        -- 🗳️ -- Artifact: example-artifact.1.0.0.json 
        •	👤 Committer: <EMAIL>
        •	💬 Commit: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    ```

- **MS Teams Notifications**
  - These notifications are sent to the default channel: [Promotion Pipeline Notifications](https://teams.microsoft.com/l/channel/19%3a629060b3621e46289f08efec4dbdf1f1%40thread.skype/Promotion%2520Pipeline%2520Notifications?groupId=2cf8b743-2890-45ae-99a4-257f55430548&tenantId=93f33571-550f-43cf-b09f-cd331338d086), or if it has been modified, to the specified channel. Please check the [How to Use documentation](how-to-use-promotion-pipeline.md) to learn how to change the specified channel.

## 2. Promotion Pipeline execution

In all executions of the promotion pipeline, you will see some general steps that are always the same explained in the previous section:
![Jenkins general steps](img/jenkins_general_steps.png)

and other steps that will change depending on the type of environment:
![Jenkins specific steps](img/jenkins_specific_steps.png)
