############################################################################################################
####################################### MODIFICATION AREA ###################################################
######################## Modify these values according to your project needs.  ##############################
### Rest of customizable values are listed in README.md section 2.1.3 Common custom configuration values  ###
############################## Only modify them is special behaviour is needed ##############################
#############################################################################################################

#### Name of current repository for validation. It has to match the name of the repository where this file is.
repositoryName: "name-of-current-repository-validation"

#### Artifactory
artifactPath: "path_to_upload_artifact"     #i.e: "assure-tools/solution-composer-service/solution-composer/ui/bundle"

##### Zip (zip)
targetZipName: "name-of-the-targetZipName"  #i.e: "solution-composer-ui"
zipSourceFolder: ""                         #i.e: "build"

##### Execute install (install)
scriptParams: "-r requirements.txt -t site-packages"
dependenciesFilePath: ""                    #folder of requirements.txt i.e: "code"

##### GitHub data #####
gitHubCredential: "assure-github" # for assure, assure-external or assure-delivery org the value is: "assure-github"

#############################################################################################################
################################### END OF MODIFICATION AREA ################################################
#############################################################################################################