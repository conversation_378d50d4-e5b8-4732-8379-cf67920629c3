############################################################################################################
####################################### MODIFICATION AREA ###################################################
######################## Modify these values according to your project needs.  ##############################
#############################################################################################################

#### Name of current repository for validation. It has to match the name of the repository where this file is.
repositoryName: "name-of-current-repository-validation"

### Name of the .yml file containing the list of scanneable repositories.
listFileName: 'list'

### List of the users that will receive the results of the Checkov scans.
emailList:
  - '<EMAIL>'
  - '<EMAIL>'

#############################################################################################################
################################### END OF MODIFICATION AREA ################################################
#############################################################################################################

##### GitHub data #####
gitHubCredential: "assure-github" # for assure, assure-external or assure-delivery org the value is: "assure-github"
gitEmail: "<EMAIL>"
gitUsername: "Jenkins User"