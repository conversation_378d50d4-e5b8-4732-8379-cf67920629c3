#!/bin/bash
##
## This script looks for all references to Terraform modules that are not already part
## of the folder tree in which the script is run, copies those remote modules into a
## folder in that tree, and amends the discovered references so that they refer to the
## local copies.
##
## Its existence is due to the justifiable prevention of the use of external modules
## in Terraform plans deployed by the PDXC Terraform API. To address that, we use
## this script to resolve such references before we submit the source git repository
## to the Terraform API for golden image creation.
##
## The script requires two tools that, while ubiquitous, are not normally part of base
## (b)ash script installations:
##
##   . terraform        - https://releases.hashicorp.com/terraform/
##   . jq (version 1.5) - https://stedolan.github.io/jq/download/
##
## These must be in the environment PATH of the script
##
## To run, change to the top level Terraform module folder and simply run the script
## with no arguments.
##
## The basic logic of the script is as follows:
##
##   . run "terraform get" to resolve all module references
##   . find all remote references from the .terraform/modules/modules.json 
##     file produced from "terraform get"
##   . copy all those remote modules from the .terraform/modules folder to
##     a folder named "external-modules/<module-id>/code"
##   . scan all files for references to those remote modules and change
##     those references so that the refer to the local copy
##
## NOTES: 
##   . On error, changes that were made before the error are NOT backed out.
##   . Module references are considered remote if they do not start with a "."
##   . Identical "source" values in the Terraform module block are resolved to the
##     same "external-modules" subfolder - meaning that any module "versioning"
##     not declared in the "source" property value is NOT taken into account.
##     For example, and in particular, the "version" property in the module block
##     is not used, meaning, in effect, that terraform repositories are not 
##     supported!
##

echo '########################################'
echo $0
env | sort
bash --version
echo '########################################'
echo

function get_referenced_external_modules {

#
# For every module source that comes from a remote repository...
#
while read -r ref; do

	#
	# Parse the jq output line
	#
	IFS=',' read -a parts <<< "$ref"
	src=${parts[0]}


	DIRsrc=$(echo ${src} | sed 's/git::http.:\/\///g' | sed 's/?ref=.*//' | tr "/" "_")

	target_dir="${DIRsrc}"

	#
	# Save the src->target mapping for later
	#
	if [ ! -z "${src}" ]; then
		SourceTargetMappings+=("${src},${target_dir}")
	fi

#
# Get all references from modules.json (generated by "terraform get") that do not start with a "."
#
# The sort eliminates duplicate sources. We do this because "terraform get" does not. Instead,
# if a module is referenced multiple times "terraform get" downloadeds it multiple times.
#
# NOTE: All other references are assumed to be remote to the top level module, even those
# that start with "/" (absolute local filesystem references). It does no harm to "reposition"
# these latter modules. On the other hande, by "avoiding" "." references we leave relative
# modules (particularly submodules) in place as we should - with the risk that some twit might
# be refering to a local file system module outside of the top level module tree using a
# relative path ... don't ... just don't!
#
done < <(cat .terraform/modules/modules.json | jq -r '.Modules[] | select(.Source|startswith(".")|not) | (.Source + "," + .Dir + "," + .Root) ' | sort -t "," -u -k 1,1)

}

function download_branch_referenced_external_modules {

echo '################# MAPPINGS ####################'
echo "${SourceTargetMappings[@]}"
echo '########################################'
echo

#
# Now go through the source mappings and add remote branch in the sources
#
for mapping in "${SourceTargetMappings[@]}"
do
	echo '################### MAPPING: ' $mapping
	
	BRANCH="${DIAAS_BRANCH}"

	#
	# Parse the src->target_dir mapping line
	#
	IFS=',' read -a parts <<< "$mapping"
	src=${parts[0]}
	target_dir=${parts[1]}

	DIRsrc=$(echo ${src} | tr "/" "_")

	target_temp_dir="${ExternalModulesTempFolder}/${target_dir}"
	target_dir="${ExternalModulesFolder}/${target_dir}"

	#
	# Make the module source reference suitable to be part of a regular expression
	#
	REsrc=$(echo ${src//\//\\/})

	#
	# Find every file that uses that module reference (including those in
	# $ExternalModulesFolder)
	#
	# NOTE: assumes the entire source reference is on a single line
	#
	found=0
	REF="false"

	gitRepo=`echo ${src} | sed 's/git:://' | sed 's/\.git.*/\.git/'`
	grepCommand=`echo ${src} | grep '?ref='`

	if [ $? -eq 0 ]; then
			refEntry=`echo ${src} | sed 's/.*?ref=//' | sed 's/\"//'`
			if [ "null$refEntry" != "null" ]; then
				REF="true"
				BRANCH=$refEntry
			fi
	fi
########################################################################################################################################
	echo '######## GITREPO #######' "$gitRepo" 'Branch: ' "$DIAAS_BRANCH"
	echo

	mkdir -p ${target_temp_dir}

	$GIT clone ${gitRepo} ${target_temp_dir}

	currpath=`pwd`
	subFolder=`echo ${src} | sed 's/.*\.git\/\///' | sed 's/?ref=.*//' | sed 's/\"//'`
	cd ${target_temp_dir}
	
	DIAAS_BRANCH_TYPE_PATTERN=`echo $BRANCH|sed 's/\/.*//'`
	
	if [[ $REF == "false" && $MODE != "fixedversion" && $DIAAS_BRANCH_TYPE_PATTERN == "release" ]]; then
		DIAAS_BRANCH_VERSION=`echo ${BRANCH}|sed 's/.*\///'`
		if [[ ${DIAAS_BRANCH_VERSION} =~ ^[0-9][0-9].[0-9][0-9]*.[0-9][0-9]*$ ]]; then
			DIAAS_BRANCH_VERSION_PATTERN=`echo ${DIAAS_BRANCH_VERSION}|awk -F\. '{print $1"."$2"."}'`
		else
			DIAAS_BRANCH_VERSION_PATTERN=${DIAAS_BRANCH_VERSION}
		fi
		echo '######## GITREPO #######' "$gitRepo" 'Searched branch pattern: ' "${DIAAS_BRANCH_TYPE_PATTERN}/${DIAAS_BRANCH_VERSION_PATTERN}"
		BRANCH=`$GIT branch --list --all|grep "remotes/origin/${DIAAS_BRANCH_TYPE_PATTERN}/${DIAAS_BRANCH_VERSION_PATTERN}"|sort|tail -n 1|sed 's/remotes\/origin\///'`
		echo '######## GITREPO ####### Branch found:' "$BRANCH"
		if [[ $BRANCH == "" && $MODE == "release" ]]; then
			echo "Release mode: specified ${BRANCH} from ${gitRepo} DOES NOT EXIST"
			exit 1;
		fi
	fi
	
	$GIT checkout $BRANCH
	
	if [ $? -ne 0 ]; then
		if [[ $REF == "true" ]]; then
			echo "Specific REF not found: ${BRANCH} from ${gitRepo} DOES NOT EXIST"
			exit 1;
		else if [[ $MODE == "fixedversion" ]]; then
			echo "Fixed version mode: specified branch ${BRANCH} from ${gitRepo} DOES NOT EXIST"
			exit 1;
		else if [[ $MODE == "release" ]]; then
			echo "Release mode: specified ${BRANCH} from ${gitRepo} DOES NOT EXIST"
			exit 1;
		else 
			echo "Failsafe mode: using MASTER branch as ${BRANCH} from ${gitRepo} DOES NOT EXIST"
		fi
		fi
		fi
	fi
	
	$GIT branch
	cd ${currpath}
########################################################################################################################################
	mkdir -p ${target_dir}
	mv ${target_temp_dir}/${subFolder}/* ${target_dir}

done

}

############################################################################################


function post_replacement {

echo '################# MAPPINGS ####################'
echo "${SourceTargetMappings[@]}"
echo '########################################'
echo

#
# Now go through the source->target mappings and replace remote references in the sources 
# with references to the local copies
#
for mapping in "${SourceTargetMappings[@]}"
do
	echo '################### MAPPING: ' $mapping

	#
	# Parse the src->target_dir mapping line
	#
	IFS=',' read -a parts <<< "$mapping"
	src=${parts[0]}
	target_dir=${parts[1]}
	
	target_dir="${ExternalModulesFolder}/${target_dir}"

	#
	# Make the module source reference suitable to be part of a regular expression
	#
	REsrc=$(echo ${src//\//\\/})
	
	#
	# Find every file that uses that module reference (including those in
	# $ExternalModulesFolder)
	#
	# NOTE: assumes the entire source reference is on a single line
	#
	found=0
	
	grepCommand="^\s*source\s*=\s*\"${src}\"" 
	echo '######## GREP #######' "$grepCommand"
	grep -lr "^\s*source\s*=\s*\"\"" .
	echo '########################################'
	echo

	for f in $(grep -lr "$grepCommand" .) 
	do
		found=1
		#
		# Create the correct number of parent folder references to the local
		# external module folder
		#
		dir=$(dirname $f)
		IFS='/' read -a parents <<< "$dir"
		unset parents[0]
		prefix=""
		#echo "Parents ${parents[@]}"
		for parent in "${parents[@]}"
		do
			#echo "Processing parent ${parent}"
			prefix="${prefix}../"
		done
		echo "Editing $f to reference ${prefix}${target_dir}"

		#
		# Edit the target file as needed
		#
		sed -i '
/^\s*source\s*=\s*"'"${REsrc}"'"/ {
i\
    source = "./'"${prefix}${target_dir}"'"
s/^/#/
}' $f;
		[ $? -eq 0 ] || { echo "Editing $f failed"; exit 1; }
	done
	if [ "$found" == "0" ]; then
		echo "WARNING: No referers found for remote source $src"
	fi
done
}

############################################################################################

if [ "null$1" == "null" ]; then
	DIAAS_BRANCH="master"
else
	DIAAS_BRANCH="$1"
fi

if [ "null$2" == "null" ]; then
	MODE="release"
else if [ "$2" == "release" ] || [ "$2" == "failsafe" ] || [ "$2" == "fixedversion" ]; then
	MODE="$2"
else
	echo "Incorrect MODE parameter, must be release, fixedversion or failsafe"
	echo "Usage: resolve.sh TF_MODULES_BRANCH MODE"
	exit 1;
fi
fi

echo "Using ${DIAAS_BRANCH} branch on ${MODE} mode."

GIT="/usr/bin/git"

if test -f "$GIT"; then
    echo "$GIT is present."
else
	echo "$GIT is NOT present."
	exit 1
fi

#
# Where we put local copies of remote modules
#
ExternalModulesFolder="external-modules"

#
# Where we put temp copies of remote modules
#
ExternalModulesTempFolder="external-modules-temp"

declare -a SourceTargetMappings

#
# Clean up
#
rm -rf "${ExternalModulesTempFolder}" "${ExternalModulesFolder}" ".terraform"

#
# Make terraform do all the module resolutions
#
terraform get
[ $? -eq 0 ] || { echo "Failed terraform get"; exit 1; }
echo $0

get_referenced_external_modules

#
# Remove the .terraform folder as we no longer need it 
#
rm -rf .terraform

download_branch_referenced_external_modules

post_replacement

#
# Clean up
#
rm -rf "${ExternalModulesTempFolder}" ".terraform"