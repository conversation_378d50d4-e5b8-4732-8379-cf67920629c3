# How to use the DXC Assure Promotion Pipeline

- [Prerequirements](#prerequirements)
- [How to start using the Promotion Pipeline](#how-to-start-using-the-promotion-pipeline)
- [Configuration parameters](#configuration-parameters)
  - [Repository name](#repository-name)
  - [Artifactory path patterns from where the artifacts must be retrieved](#artifactory-path-patterns-from-where-the-artifacts-must-be-retrieved)
    - [Examples and real scenarios](#examples-and-real-scenarios)
  - [Promotion cycle definition](#promotion-cycle-definition)
    - [Environment mapping](#environment-mapping)
    - [Define environment (stage) list](#define-environment-stage-list)
    - [Define what type of environment is each stage](#define-what-type-of-environment-is-each-stage)
    - [Success and Failure status change (Promotion)](#success-and-failure-status-change-promotion)
  - [Environment specific values](#environment-specific-values)
    - [Environment identification values](#environment-identification-values)
    - [Testing specific values](#testing-specific-values)
    - [Rollback specific value](#rollback-specific-value)
  - [Optional and Extension parameters](#optional-and-extension-parameters)  
    - [GitHub credential](#github-credential)
    - [Artifactory connection details](#artifactory-connection-details)
    - [e2e testing repositories](#e2e-testing-repositories)
    - [Microsoft Teams Notifications](#microsoft-teams-notifications)
    - [Disable Promotion Pipeline automatic Notifications](#disable-promotion-pipeline-automatic-notifications)
    - [Timeouts and retries](#timeouts-and-retries)
    - [Cypress docker image version](#cypress-docker-image-version)
    - [Auto-update Terraform version](#auto-update-terraform-version)    
- [Running examples](#running-examples)

***

## Prerequirements

Before you can use this pipeline you need to do the following steps:

- Use [DXC Assure generic pipelines](../README.md#2-push-pipeline-definition) or ensure that your generated artifacts are available in Artifactory and have the needed properties:

   | Property | Value | Description |
   | -----------------| --- | ----------- |
   | status    | ready | Based on the [DXC Assure Platform Lifecycle](https://github.dxc.com/assure/assure-platform/blob/master/docs/processes/PLATFORM_LIFECYCLE.md) |
   | type      | generic   | when standard artifact is created |
   |           | tf        | for deployment scripts (silver image descriptor) |
   |           | docker    | for docker images |
   | commit    | hash value | GitHub commit Id that resulted in generating this artifact |
   | git_url   | URL        | GitHub url of the repository that triggered this build process |

- Decide what your promotion cycle would be, this means decide which environments you will use, how many stages will be there and the type of testing to be done on each one: [Release process documentation](https://github.dxc.com/assure/assure-platform/blob/master/docs/processes/RELEASE.md)

  - Create the required environments as needed (e.g. if your cycle consists on DEV --> STAGING --> PROD, create those 3 environments where the different artifacts will be deployed during the promotion). Please make sure you have the required access to the AWS console for those environments. Depending on your cycle, different strategies might be used, e.g. [SDLC Environments example](https://github.dxc.com/assure/assure-platform/blob/master/docs/processes/RELEASE.md#4-software-delivery-lifecyle-sdlc-environments).
  - For every environment of the cycle, install all the components in scope
  - For enabling testing during the promotion cycle you need to:
    - Install this deployment package in your promotion environments: **assure-jenkins-testing-client-deploy**
    - In the default cognito pool of each environment, create a user and remember the credentials (user / password)

## How to start using the Promotion Pipeline

1. **Create a new repository for the pipeline**, e.g. *assure-platform-promotion-pipeline*.

   Recommendation:

   - if you are in "assure" GitHub organization use this template when creating the repo [Assure Promotion Pipeline Template](https://github.dxc.com/assure/assure-promotion-pipeline-template) as it already contains all the files that will be required for the pipeline to work.

      ![Create new repository from template](img/create_repo_from_template.png)

   - else, for any other organization, please just copy the files from that repository to yours.

2. Configure the `conf.yml` values as follows:

   Please see [below](#configuration-parameters) the whole list of parameters to be configured.

3. Configure the required credentials at pipeline level

   There are several values that are required for enabling the testing on the environments but that need to be treated as secrets. For this reason, instead of just adding them into the configuration file, it is required to make them available through Jenkins credentials. In this case, through a YAML file that contains all the values and that has to be upload as credential in Jenkins.

   These are the values to be added. As in the configuration ones, the environment / stage prefix is required:

   | Property  | Description  | Example |
   |-----------|--------------|---------|
   | user_pool | cognito user created previously in all environments (same for all) | user_pool: "promotion-user" |
   | password_pool | cognito password created previously in all environments (same for all) | password_pool: "StR0nGpa$$w0rd" |
   | STAGE_poolId | pool id generated by assure-jenkins-testing-client-deploy deployment package | environment-1_poolId: "us-east-1_aBcDeFjhI" |
   | STAGE_clientId | client id generated by assure-jenkins-testing-client-deploy deployment package| environment-1_clientId: "12345abcd" |
   | STAGE_apiKey | default api key | environment-1_apiKey: "aPiKeYvALuEdEvElOpMeNt" |

   **Credentials file template**
   You can use the following template as base for your file:

   Template: [promotion-config.yml Example](samples/pipelinepromotion/template-jenkins-cred.yml)

   Steps to create the new credentials of type file at project level are:

   a. Open a jenkins job, once the webpage is loaded, go to classic view:
   ![Jenkins classic view](img/jenkins_classic_view.png)

   b. In the name of the repository display the options and click on "Credentials"
   ![Jenkins access to credentials](img/jenkins_access_credentials.png)

   c. Inside Folder -> Global credentials click on "Add Credentials"
   ![Jenkins add credentials](img/jenkins_add_credentials.png)

   d. Select kind "Secret file", use "promotion_config" as ID and upload the YAML file which contains the properties previously mentioned.
   ![Jenkins save credentials](img/jenkins_save_cred.png)

4. Run pipeline

   The pipeline will be automatically run with a commit but in order to run it for the automatic promotion there are 2 alternatives:

   - Manual run: when the developer does a change or several that need to be promoted, it is possible to manually trigger the pipeline from Jenkins.
   - Schedule run: in the Jenkins file it is possible to add a schedule so it is run every specific number of hours.

   It is possible to have multiple branches of the pipeline to cover different scenarios, e.g. one for master and one for a hotfix that needs to be promoted.

   For execution details, please have a look at [Running examples section](#running-examples)

## Configuration parameters

### Repository name

This property will used to ensure that the configuration file (conf.yml) has been modified and to prevent deployment mistakes.

**Property**: *repositoryName*

### Artifactory path patterns from where the artifacts must be retrieved

The properties of this section depend on the artifactory organization. Please, check the following link to ensure the artifactory structure follow the guidelines
[Artifactory structure](https://github.dxc.com/assure/assure-platform/blob/master/docs/guidelines/AUTOMATION_DEVELOPMENT.md#5-artifactory-structure-and-guidelines)

**Properties**:

- *repository*: A string to specify in what artifactory repository or repositories the artifacts are, e.g. repository: "assure-generic"
- *path*: For each of the repositories on previous property (*repository*), include the string with the path where the artifacts are stored in. This is the base path to be used, so it is required to include the path until the first common folder that groups the artifacts or specify multiple paths. Example:

   > Example:
   >
   > Scenario: for *assure_generic* repository, we have the following paths: /assure-platform/event-service/service/..., /assure-platform/event-service/application/..., /assure-platform/core/... and assure-platform/ux-config
   >
   > Case 1: if we want everything for event-service we would have this structure:
   >
   >> ```yml
   >>    artifactory:
   >>      repositories:
   >>        - repository: "assure-generic"
   >>          type: "generic"
   >>          paths:
   >>            - path: "assure-platform/event-service"
   >> ```
   >
   > Case 2: if additionally, we want also the stuff under core, we would have:
   >
   >> ```yml
   >>    artifactory:
   >>      repositories:
   >>        - repository: "assure-generic"
   >>          type: "generic"
   >>          paths:        
   >>            - path: "assure-platform/event-service"
   >>            - path: "assure-platform/core"
   >> ```
   >
   > Case 3: if we want everything for platform, we would just have:
   >
   >> ```yml
   >>    artifactory:
   >>      repositories:
   >>        - repository: "assure-generic"
   >>          type: "generic"
   >>          paths:        
   >>            - path: "assure-platform"
   >> ```
   >
   > Case 4: if we want only the service part of event-service, we would have:
   >
   >> ```yml
   >>    artifactory:
   >>      repositories:
   >>        - repository: "assure-generic"
   >>          type: "generic"
   >>          paths:        
   >>            - path: "assure-platform/event-service/service"
   >> ```
   >

- Branch definition: which branches we want to include. Example, initially for Platform components we are only taking artifacts generated from "master". However, it is possible to want to select more than one branch or different branches depending on the artifactory paths. We can specify it:

  - *branches*: For each path listed on *path*, include the branches in scope. Example:

      ```yml
         artifactory:
         repositories:
           - repository: "assure-generic"
             type: "generic"
             paths:
               - path: "assure-platform/event-service/service"
                 branches:
                   - "master"
                   - "fix/OC-1234"
      ```

  If no branch property is defined, everything under the path will be taken.

  > **IMPORTANT**
  >
  > This whole logic is based on the assumption that all Artifactory paths for the artifacts are built using branch name as the last part of the path before the artifact name
  >

- *status*: Based on your promotion cycle, define what are the statuses to be retrieved (ready, staging, ga...). This will read the status property previously added by the PUSH pipeline and updated during the promotion by the PULL pipeline. Example:  

  ```yml
     artifactory:
       repositories:
         - repository: "assure-generic"
           type: "generic"
           paths:        
             - path: "assure-platform/event-service/service"
               branches:           
                 - "master"
                 - "fix/OC-1234"
               status:
                 - "ready"                             
                 - "general-error"        
                 - "staging"
                 - "ga"
  ```

- *type*: In case of having a docker repository, use this property to mark it:

  ```yml
     artifactory:
       repositories:
         - repository: "diaas-docker"
           type: "docker"
  ```

- *skipRelease*: This property will allow to skip the generation of a release (usefull when testing e2e cases before merge to main/master branch):

  ```yml
     skipRelease: true
  ```

#### Examples and real scenarios

1. Artifacts saved on diaas-generic, multiple paths inside assure-platform. my-lambda-service will retrieve artifacts from master branch and
my-application from development branch. Both of them will get artifacts which status is ready or stable.

   ```yml
      artifactory:
        repositories:
          - repository: "diaas-generic" # Repository name to get artifacts
            type: "generic"
            paths:        
              - path: "assure-platform/my-lambda-service" # Path to the service
                branches:            
                  - "master"
              - path: "assure-platform/my-application" # Path to the service
                branches:            
                  - "development"                               
                status: # artifact status(es)
                  - "ready"
                  - "stable"                  
   ```

2. Artifacts saved on diaas-generic and assure-generic.
   The artifacts stored in assure-generic have assure-product/calculation as path and only want feature/test branch.
   The artifacts stored in diaas-generic are inside assure-document path using generated artefacts from master branch.
   The status property of the artifacts has to be ready or in-error

   ```yml
      artifactory:
         repositories:
            - repository: "diaas-generic"            # Repository name to retrieve artifacts
               type: "generic"                       # Repository type (docker or generic)
               paths:        
               - path: "assure-document"             # Path to the service(s)
                 branches:            
                   - "master"                      # Branch(es) to deploy                             
               status:                               # artifact status(es)
                 - "ready"
                 - "in-error"    
            - repository: "assure-generic"           # Repository name to retrieve artifacts
               type: "generic"                       # Repository type (docker or generic)
               paths:        
               - path: "assure-product/calculation"  # Path to the service(s)
                  branches:            
                     - "feature/test"                # Branch(es) to deploy                             
               status:                               # artifact status(es)
                 - "ready"
                 - "in-error"           
         deployment_in_progress: "false"
   ```

### Promotion cycle definition

To define the promotion cycle we will have to define:

1. To which environment should the artifact be deployed depending on its status property
2. What is the next status to be set in case of a success or failure during deployment and testing
3. What type of environment is each one

#### Environment mapping

Define to which environment an artifact will be deployed based on its status:

**property**: *status_stage_mapping* : dictionary with the mapping between environment and status. **Please notice that these statuses need to be referenced previously on the artifactory status values.**

```yml
   status_stage_mapping:
     - status: "status-1"
       stage: "environment-1"
     - status: "status-2"
       stage: "environment-2"
```

Further details can be found on: [Artifact Lifecycle](https://github.dxc.com/assure/assure-platform/blob/master/docs/processes/PLATFORM_LIFECYCLE.md#5-artifact-lifecycle)

Examples:

1. Only one environment associated to ready status

   ```yml
      ###### Environment Status Mapping
      status_stage_mapping:
        - status: "ready"
          stage: "development"   
   ```

2. Two environments, one associated to ready status and the second environment associated to stable status

   ```yml
      ###### Environment Status Mapping
      status_stage_mapping:
        - status: "ready"
          stage: "development"
        - status: "stable"
          stage: "staging" 
   ```

> The environment names used in this property, will be used as reference in the rest of properties to be defined.

#### Define environment (stage) list

Give a name to every environment that will be part of the promotion cycle.

**property**: *name* - a string with the stage name

Example:

```yml
   stages:
     - name: "development"
     - name: "staging"
```

#### Define what type of environment is each stage

For each stage define a type. We have created the following predefined types that will cause that the deployment grouping logic and / or the testing perform is different.

- **development**:
  - *Deployment logic*: in this type of environment each artifact retrieved from Artifactory will be deployed and tested individually. This means that if the terraform and the deployable bundle have both been retrieved for the same component, each of them will be deployed and tested sequentially.
  - *Testing*: only the tests that are available under the /test folder of the repository and commit that generated the artifact will be executed, and only for the artifact being deployed.
- **test**:
  - *Deployment logic*: Same as "development" type - sequential deployment of each artifact.
  - *Testing*: once the artifact is successfully deployed, the pipeline will execute the deployed artifact tests, the tests of every other artifact that is deployed under the same deployment package, the ones for the deployment package and the same for those deployment packages that are dependents on the deployed one. In all cases, the tests performed are the ones from the /test folder of the repository and commit that is currently deployed on the environment.
  
  >
  > **Example:**
  >
  > - Deployment package 1 deploys 2 artifacts (artifact 1 and artifact 2)
  > - Deployment package 2 deploys 3 artifacts (artifact 3, artifact 4 and artifact 5) - this deployment package depends on Deployment package 1
  > - Deployment package 3 deploys 1 artifact (artifact 6) - this deployment package depends on deployment package 2
  >
  > **Case 1:**
  >>  If we deploy artifact 1, this will result on running the /test folder of all of the above:
  >>
  >> - artifact 1 tests as the deployed one
  >> - artifact 2 and deployment package 1 tests because they are part of the same deployment package
  >> - deployment package 2 and its artifacts because it depends on deployment package 1
  >> - deployment package 3 and its artifact because of the dependency on deployment package 2
  >
  > **Case 2:**
  >> If we deploy, artifact 3, we will run the /test folder of the deployment packages 2 and 3 and their artifacts but not for Deployment package 1
  >
  > **Case 3:**
  >> If we deploy the deployment package 3, we will only run the /test folder for that one and its artifact
  >

- **staging**:
  - *Deployment logic*: this will group the artifacts by deployment package. This means that if we have artifact 1 and the terraform that deploys it, there will be a single deployment using both new components (artifact and terraform).
  - *Testing*: The testing will be performed only once that all artifacts in scope on this pipeline execution are deployed.The tests to be done are the same as on *test* type plus the ones for the dependencies and the e2e tests. See [e2e Testing Repositories](#e2e-testing-repositories)

   >
   > **Example:**
   >
   > Using the same scenario as above, these would be the cases:
   >
   > **Case 1:**
   >> If we deploy artifact 1, this will result on running the /test folder of all of the above:
   >>
   >> - artifact 1 tests as the deployed one
   >> - artifact 2 and deployment package 1 tests because they are part of the same deployment package
   >> - deployment package 2 and its artifacts because it depends on deployment package 1
   >> - deployment package 3 and its artifact because of the dependency on deployment package 2
   >
   > **Case 2:**
   >> If we deploy, artifact 3, we will run the /test folder of:
   >>
   >> - the deployment packages 2 and 3 and their artifacts (self deployment package and dependent)
   >> - the deployment package 1 and its artifacts as they are a dependency for Deployment package 2
   >
   > **Case 3:**
   >> If we deploy the Deployment package 3, we will run the tests for all the Deployment Packages and artifacts as all are dependencies of the Deployment package 3
   >
   > Additionally, in all cases, after those tests, the e2e tests will be executed.
   >

- **prod**:
  - *Deployment logic*: Sames as "staging"
  - *Testing*: Only performs the e2e testing after all deployments are completed. See [e2e Testing Repositories](#e2e-testing-repositories)
  - **Additional steps**: The successful deployment and testing of an artifact in this type of environment will result on the creation of a new version / release of its Deployment Package on the **"Release Service"** and the request to execute the **"Upgrade pipeline"** that will deploy this new version in all the environments that fulfill the specific conditions.

#### Success and Failure status change (Promotion)

For each environment (stage), we need to define which status should be assign to an artifact when successfully deployed and tested in that environment or in case of failure.

**Property**: *status_success* -- status_success: "status-2"
**Property**: *status_failure* -- status_failure: "error-status-1"

Example:

```yml
stages:
  - name: "development"    
    status_success: "stable"
    status_error: "in-error"
```

Proposal for promotion status: [Artifact Lifecycle](https://github.dxc.com/assure/assure-platform/blob/master/docs/processes/PLATFORM_LIFECYCLE.md#5-artifact-lifecycle)

### Environment specific values

These properties have to be set up for each lifecycle environment:

#### Environment identification values

Specify the customer, account and the environment name (this is the ID that will normally be composed by the environment name + the account name) based on what it is defined on Environment Service.

**Properties:**

- customer_name --> customer name for each stage.

- account_name --> account name for each stage.

- resource_name --> for each stage include the required value.

Example:

```yml
stages:
  - name: "development"
    type: "development"
    status_success: "stable"
    status_error: "in-error"
    customer_name: "dxcassure"
    account_name: "Assure-DEVELOPMENT"
    resource_name: "dev-1234-Assure-DEVELOPMENT"
  - name: "staging"
    type: "staging"
    status_success: "staging"
    status_error: "general-error"
    customer_name: "other-customer-name"
    account_name: "Assure-STAGE"
    resource_name: "stg-1234-Assure-STAGE"

```

#### Testing specific values

In case that you are going to have testing in any of the stages / environments, you need to include the following properties of the environment to be able to execute them.

**Properties:**

- endpoint: "URL"
- arn: "arn role"
- region: "aws region"
- environment_type: "environment name"
- test_token_url: "URL to get token"

Example:

```yml
stages:
  - name: "development"
    type: "development"
    status_success: "stable"
    status_error: "in-error"
    customer_name: "dxcassure"
    account_name: "Assure-DEVELOPMENT"
    resource_name: "dev-1234-Assure-DEVELOPMENT"
    endpoint: "test-1234.hub-1.development.assure.dxc.com"
    arn: "arn:aws:iam::************:role/role"
    region: "us-east-1"
    environment_type: "test-1234"
    test_token_url: "https://www.test-1234.hub-1.development.assure.dxc.com/api/auth-management/realms"
```

#### Rollback specific value

In case that you want to enable the rollback when a deployment fails, you need to include the following property of the environment to be able to execute it. Adding the rollback property, if a deployment fails, the previous deployment will be executed.

**Properties:**

- rollback: "true"

Example:

```yml
stages:
  - name: "development"
    type: "development"
    status_success: "stable"
    status_error: "in-error"
    customer_name: "dxcassure"
    account_name: "Assure-DEVELOPMENT"
    resource_name: "dev-1234-Assure-DEVELOPMENT"
    rollback: "true"
```

### Optional and Extension parameters

There is an additional set of parameters that can be modified to overwrite the default values already configured at the base configuration:

#### GitHub credential

Different GitHub organizations might have different credentials defined. The default value is valid for many of the existing "assure" organizations, but in case that yours is not included, you might need to overwrite this value:

*gitHubCredential*

#### Artifactory connection details

By default our base configuration is already including read and write permissions over most of the Assure repositories, but it is possible that your artifacts are uploaded into a different or private repository. If you need to change the credential to be used by the pipeline you can set up this variable:

*artifactory_credential*

#### e2e testing repositories

Please have a look at the e2e testing documentation to learn how this type of testing must be implemented: [Assure e2e testing](https://github.dxc.com/assure/assure-e2e-testing)

You might define your own e2e testing repository instead of using the base one. You can do this by overwriting these values:

```yml
e2eTest:
 - repository: "repository-name"
   branch: "branch"
   organization: "github-organization"
```

> [IMPORTANT]
>
> If you required a different version of the docker images used in the e2e process, you will need to change your scripts specifying the versions or loading your own docker images.
>

#### Microsoft Teams Notifications

By default the pipeline sends notifications of the Pipeline job final result to: [Promotion Pipeline Notifications](https://teams.microsoft.com/l/channel/19%3a629060b3621e46289f08efec4dbdf1f1%40thread.skype/Promotion%2520Pipeline%2520Notifications?groupId=2cf8b743-2890-45ae-99a4-257f55430548&tenantId=93f33571-550f-43cf-b09f-cd331338d086) channel, defined within the [Assure Platform Automation](https://teams.microsoft.com/l/team/19%3aa53fd9c7180842e3a4dafed70f0ba99a%40thread.skype/conversations?groupId=2cf8b743-2890-45ae-99a4-257f55430548&tenantId=93f33571-550f-43cf-b09f-cd331338d086) team.
It is possible, and advisable, to create a channel within your own team to receive notifications specific to your pipeline. If you want to modify the credential to be used by the pipeline, create a webhook in you channel and add the following property in the conf.yml:

*msTeamsWebhook*

#### Disable Promotion Pipeline automatic Notifications

The pipeline sends two types of notifications:

- Email Notifications.

These notifications are sent to the email of the last committer/s and displays relevant information regarding the Promotion Pipeline status.

- Microsoft Teams notifications

These notifications are sent to the default channel: [Promotion Pipeline Notifications](https://teams.microsoft.com/l/channel/19%3a629060b3621e46289f08efec4dbdf1f1%40thread.skype/Promotion%2520Pipeline%2520Notifications?groupId=2cf8b743-2890-45ae-99a4-257f55430548&tenantId=93f33571-550f-43cf-b09f-cd331338d086), or if it has been modified, to the specified channel  through the corresponding property. It is possible to disable both notifications by adding the following property in the conf.yml file and setting it to false:

*promotionNotifications: false*

#### Timeouts and retries

The following properties are used to control de deployment operation, e.g. how long to wait for a deployment to be completed

- *maxTime*: Max minutes that a deploy operation of a service can take
- *sleepTime*: Seconds to wait between deployment completion checks
- *attemptCount*: Maximum number of attempts (refresh service)

#### Cypress docker image version

In case that you want to specify a cypress version to be used for the post deployment test, you can add the following property in the conf.yml:

**Property:**
*cypressVersion*

As default, if the property mentioned above is not found, the pipeline will load the 8.6.0 cypress version. The list of available versions can be found: [https://artifactory.platformdxc-mg.com/ui/repos/tree/General/diaas-docker%2Fpipelines%2Fcypress]

> If you cannot access to this repo (diaas-docker) on Artifactory, you can ask for permissions using the [Assure Self-Service Bot](https://github.dxc.com/assure/assure-self-service-bot)

Example:

```yml
cypressVersion: 9 
```

In the example 9 is the major version of cypress that the pipeline will take. Apart of the mayor version (8, 9, 10), you could use "latest" if you want to be updated with the latest cypress version that is stored in artifactory.

#### Auto-update Terraform version

The following property will be used to update the terraform version automatically during the deployment process. Note that if the property is configured, all deployments will start executing plan action and then apply.

**Property:**
*auto_update_tf_version*

As default, if the property mentioned above is not found, the pipeline will no update the terraform version automatically.

Example:

```yml
auto_update_tf_version: true 
```


## Running examples

Every stage and multiple pipeline executions explained: [Promotion Pipeline Overview](promotion-pipeline-overview.md)