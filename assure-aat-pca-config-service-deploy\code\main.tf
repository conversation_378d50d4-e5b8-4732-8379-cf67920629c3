# * Lambda For PCA Handler
module assure_aat_pca_config_service_lambda {
    source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-create-simple-lambda/code"
	# * Platfrom Vars
	lambda_runtime      	= "python3.9"
    lambda_handler      	= "lambda_function.lambda_handler"
    client_name             = local.client_short_name
    environment_name        = local.environment_name
	aws_account             = local.aws_target_account
	aws_region              = var.aws_region
	resource_tags           = local.resource_tags
	
	# * Required
	# ? Lambda Function Name
	service_name            = var.service_name
	function_name           = var.assure_aat_pca_config_service_function_name
	function_full_name      = "${local.environment_name}-${var.assure_aat_pca_config_service_function_name}-handler"
	# ? Lambda Bundles Info
	artifact_branch_name    = var.artifact_branch_name
	bundle_version          = var.bundle_version_assure_aat_pca_config_service_lambda
	bundle_path             = "${var.bundle_path_assure_aat_pca_config_service_lambda}/${var.bundle_branch_assure_aat_pca_config_service_lambda}"
	bundle_name             = var.bundle_name_assure_aat_pca_config_service_lambda
	bundle_type             = var.bundle_type_assure_aat_pca_config_service_lambda

	lambda_memory_size  = var.config_service_lambda_memory_size
	ephemereal_storage_size = var.config_service_ephemereal_storage_size
    lambda_timeout      = var.config_service_lambda_timeout

	# * Options
    allow_apigw_invocation  = "false"
	register_lambda         = "true"
	lambda_use_shared_sg    = "true"

	add_lambda_policy		= "true"
	lambda_policy           = data.template_file.assure_aat_pca_config_lambda.rendered

	# * Env
	lambda_environment        = {
     	bucket_name = module.pca_bucket.bucket_name
		fts_bucket_name = data.aws_ssm_parameter.fts_bucket_name.value
     	s3_folder_input_docs_path = var.s3_folder_input_docs_path
     	s3_folder_output_docs_path = var.s3_folder_output_docs_path
		s3_folder_reports_path = var.s3_folder_reports_path
		s3_folder_template_docs_path = var.s3_folder_template_docs_path
		dynamodb_table = module.training_status_table.table_name
		dynamodb_report_table = module.reports_table.table_name
    }
}

# * Lambda For PCA Event Handler
module assure_aat_pca_config_event_service_lambda {
    source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-create-simple-lambda/code"
	# * Platfrom Vars
	lambda_runtime      	= "python3.9"
    lambda_handler      	= "lambda_function.lambda_handler"
    client_name             = local.client_short_name
    environment_name        = local.environment_name
	aws_account             = local.aws_target_account
	aws_region              = var.aws_region
	resource_tags           = local.resource_tags
	
	# * Required
	# ? Lambda Function Name
	service_name            = var.service_name
	function_name           = var.assure_aat_pca_config_event_handler_function_name
	function_full_name      = "${local.environment_name}-${var.assure_aat_pca_config_event_handler_function_name}-handler"
	# ? Lambda Bundles Info
	artifact_branch_name    = var.artifact_branch_name
	bundle_version          = var.bundle_version_assure_aat_pca_config_event_service_lambda
	bundle_path             = "${var.bundle_path_assure_aat_pca_config_event_service_lambda}/${var.bundle_branch_assure_aat_pca_config_event_service_lambda}"
	bundle_name             = var.bundle_name_assure_aat_pca_config_event_service_lambda
	bundle_type             = var.bundle_type_assure_aat_pca_config_event_service_lambda

	lambda_memory_size  = var.lambda_memory_size
	ephemereal_storage_size = var.ephemereal_storage_size
    lambda_timeout      = var.lambda_timeout

	# * Options
    allow_apigw_invocation  = "false"
	register_lambda         = "true"
	lambda_use_shared_sg    = "true"

	add_lambda_policy		= "true"
	lambda_policy           = data.template_file.assure_aat_pca_config_event_handler_lambda.rendered

	# * Env
	lambda_environment        = {
		dynamodb_table               = module.training_status_table.table_name
		dynamodb_report_table		 = module.reports_table.table_name
		S3_bucket_name 				 = module.pca_bucket.bucket_name
		pca_preprocessing_lambda_arn = module.assure_aat_pca_preprocessing_handler_lambda.lambda_arn
    }
}

resource "aws_lambda_permission" "s3_event_trigger" {
	statement_id = "AllowExecutionFromS3Bucket"
	action = "lambda:InvokeFunction"
	function_name = "${module.assure_aat_pca_config_event_service_lambda.lambda_name}"
	principal = "s3.amazonaws.com"
	source_arn = "arn:aws:s3:::${module.pca_bucket.bucket_name}"
	source_account = local.aws_target_account

	depends_on = [ 
		module.pca_bucket,
		module.assure_aat_pca_config_event_service_lambda
	]
}

resource "aws_s3_bucket_notification" "aws-lambda-trigger" {

	depends_on = [
        aws_lambda_permission.s3_event_trigger
    ]

	bucket = module.pca_bucket.bucket_name
	lambda_function {
		lambda_function_arn = "${module.assure_aat_pca_config_event_service_lambda.lambda_arn}"
		events              = ["s3:ObjectCreated:Put","s3:ObjectCreated:CompleteMultipartUpload","s3:ObjectCreated:Copy","s3:ObjectRemoved:Delete","s3:ObjectRemoved:DeleteMarkerCreated"]
		filter_prefix 		= "pca/input-data/"
	}
}

# * API For PCA
module assure_aat_pca_config_service_api {
    source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//assure-standard-api/code"  

	# ? Platfrom Variable
    client_name             = local.client_short_name
    environment_name        = local.environment_name
	artifact_branch_name    = var.artifact_branch_name
	aws_account             = local.aws_target_account
	aws_region              = var.aws_region

	# * Required Variable
	service_name            = var.service_name
	service_url    			= "lambda:${module.assure_aat_pca_config_service_lambda.lambda_name}"
    api_name                = var.api_name
	api_base_path			= var.api_base_path

	# Tags
	resource_tags           = local.resource_tags
}

# * Lambda For PCA Sagemaker Handler

module assure_aat_pca_preprocessing_handler_lambda {
    source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-create-simple-lambda/code"
	# * Platfrom Vars
    lambda_runtime      	= "python3.9"
    lambda_handler      	= "lambda_function.lambda_handler"
    client_name             = local.client_short_name
    environment_name        = local.environment_name
	aws_account             = local.aws_target_account
	aws_region              = var.aws_region
	resource_tags           = local.resource_tags
	
	# * Required
	# ? Lambda Function Name
	service_name            = var.service_name
	function_name           = var.assure_aat_pca_preprocessing_handler_lambda_function_name
	function_full_name      = "${local.environment_name}-${var.assure_aat_pca_preprocessing_handler_lambda_function_name}-handler"
	# ? Lambda Bundles Info
	artifact_branch_name    = var.artifact_branch_name
	bundle_version          = var.bundle_version_assure_aat_pca_preprocessing_handler_lambda
	bundle_path             = "${var.bundle_path_assure_aat_pca_preprocessing_handler_lambda}/${var.bundle_branch_assure_aat_pca_preprocessing_handler_lambda}"
	bundle_name             = var.bundle_name_assure_aat_pca_preprocessing_handler_lambda
	bundle_type             = var.bundle_type_assure_aat_pca_preprocessing_handler_lambda

	lambda_memory_size  = var.lambda_memory_size
	ephemereal_storage_size = var.ephemereal_storage_size
    lambda_timeout      = var.lambda_timeout

	# * Options
    allow_apigw_invocation  = "false"
	register_lambda         = "true"
	lambda_use_shared_sg    = "true"

	add_lambda_policy		= "true"
	lambda_policy           = data.template_file.assure_aat_pca_preprocessing_handler_lambda.rendered

	# * Env
	lambda_environment        = {
		dynamodb_table = module.training_status_table.table_name
		dynamodb_report_table = module.reports_table.table_name
		S3_bucket_name = module.pca_bucket.bucket_name
		# Sagemaker_endpoint_name = "pca-deployed-model-endpoint-v12"
		Sagemaker_endpoint_name =  "${local.environment_specific_resource_pre_name}-${var.sagemaker_endpoint_name}"
    }
}
