/* groovylint-disable ThrowException */
package org.assure.util

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.exception.ContextNotSetException
import org.pdxc.util.ValuesUtils
import org.pdxc.notification.TeamsTemplateNotification
import groovy.json.JsonOutput

/**
* Teams Utility class
*/
class MsTeamsUtils {

    private MsTeamsUtils() { }

    static def buildMsTeamsObject (def artifact, def blueOceanUrl, def logUrl, boolean overallStatus) {
        def msTeamsbody = [committer: artifact.committer, 
                        commit: artifact.commit,
                        environment: artifact.environment, 
                        artifact: artifact.name,
                        deployresult: artifact.deployresult,
                        testresult: artifact.testresult,
                        overallStatus: overallStatus,
                        git_url: artifact.git_url,
                        blueOceanUrl: blueOceanUrl,
                        logUrl: logUrl,
                        deploymentPackage: artifact.deploymentPackage]
        return msTeamsbody
    }

    static def createMessage (def msTeamsbody, def releasesJson, def extraParam = '') {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def jsonTemplateFile
        def templateData

        if (extraParam != '') {
            (templateData, jsonTemplateFile) = composeRsFailureData(msTeamsbody)
        }

        if (msTeamsbody.overallStatus != true) {
            if (msTeamsbody.environment.equals('development') || msTeamsbody.environment.equals('test')) {
                (templateData, jsonTemplateFile) = composeDevOrTestErrorData(msTeamsbody)

            } else if (msTeamsbody.environment.equals('staging') || msTeamsbody.environment.equals('prod')) {
                (templateData, jsonTemplateFile) = composeStagingOrProdErrorData(msTeamsbody)
            }
        } else if (msTeamsbody.overallStatus != false && msTeamsbody.environment == 'prod') {
            (templateData, jsonTemplateFile) = composeSuccessData(msTeamsbody, releasesJson)
        }

        def templateJSON = context.readJSON text: jsonTemplateFile
        def template = JsonOutput.toJson(templateJSON)

        return [template, templateData]
    }

    static def composeDevOrTestErrorData (def msTeamsbody) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def jsonTemplateFile = context.libraryResource "custom/promotion-project/notification-templates/dev-test-error_notification.json"

        def templateData = [:]
        templateData.put('deploymentPackage', "${msTeamsbody.deploymentPackage}")
        templateData.put('status', (msTeamsbody.deployresult != true) ? 'Error during deployment' : 'Error during testing' )
        templateData.put('artifact', "${msTeamsbody.artifact}")
        templateData.put('committer', "${msTeamsbody.committer}")
        templateData.put('commit', "${msTeamsbody.commit}")
        templateData.put('repositoryURL', "${msTeamsbody.git_url}/commit/${msTeamsbody.commit}")
        templateData.put('pipelineUrl', "${msTeamsbody.blueOceanUrl}")
        return [templateData, jsonTemplateFile]
    }

    static def composeStagingOrProdErrorData (def msTeamsbody) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def jsonTemplateFile = context.libraryResource "custom/promotion-project/notification-templates/stage-prod-error_notification.json"

        def templateData = [:]
        templateData.put('deploymentPackage', "${msTeamsbody.deploymentPackage}")
        templateData.put('status', (msTeamsbody.deployresult != true) ? 'Error during deployment' : 'Error during testing' )
        templateData.put('artifact', "${msTeamsbody.artifact}")
        templateData.put('committer', "${msTeamsbody.committer}")
        templateData.put('commit', "${msTeamsbody.commit}")
        templateData.put('logUrl', "${msTeamsbody.logUrl}")
        templateData.put('repositoryURL', "${msTeamsbody.git_url}/commit/${msTeamsbody.commit}")
        templateData.put('pipelineUrl', "${msTeamsbody.blueOceanUrl}")
        return [templateData, jsonTemplateFile]
    }
    
    static def composeSuccessData (def msTeamsbody, def releasesJson) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def releaseVersion
        def jsonTemplateFile = context.libraryResource "custom/promotion-project/notification-templates/success_notification.json"

        releasesJson.each {releaseArt ->
            def dpName = releaseArt.find { property -> property.key == 'deployment_package_name' }.value
            def dpVersion = releaseArt.find { property -> property.key == 'version' }.value
            if (dpName == msTeamsbody.deploymentPackage) {
                releaseVersion = dpVersion
            }                                      
        }

        def templateData = [:]
        templateData.put('deploymentPackage', "${msTeamsbody.deploymentPackage}")
        templateData.put('version', "${releaseVersion}")
        templateData.put('status', "Release Service Created")
        templateData.put('artifact', "${msTeamsbody.artifact}")
        templateData.put('committer', "${msTeamsbody.committer}")
        templateData.put('commit', "${msTeamsbody.commit}")
        templateData.put('repositoryURL', "${msTeamsbody.git_url}/commit/${msTeamsbody.commit}")
        templateData.put('releaseServiceURL', "https://csam.assure.dxc.com/release-service/index.html#/releases/${msTeamsbody.deploymentPackage}/versions/${releaseVersion}")
        templateData.put('pipelineUrl', "${msTeamsbody.blueOceanUrl}")
        return [templateData, jsonTemplateFile]
    }

    static def composeRsFailureData(def msTeamsbody) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def jsonTemplateFile = context.libraryResource "custom/promotion-project/notification-templates/rs-error_notification.json"

        def templateData = [:]
        templateData.put('deploymentPackage', "${msTeamsbody.deploymentPackage}")
        templateData.put('version', "${msTeamsbody.version}")
        templateData.put('status', "Release Service Creation Error")
        templateData.put('artifact', "${msTeamsbody.artifact}")
        templateData.put('committer', "${msTeamsbody.committer}")
        templateData.put('commit', "${msTeamsbody.commit}")
        templateData.put('repositoryURL', "${msTeamsbody.git_url}/commit/${msTeamsbody.commit}")
        templateData.put('releaseServiceURL', "https://csam.assure.dxc.com/release-service/index.html#/releases/${msTeamsbody.deploymentPackage}/versions/${msTeamsbody.version}")
        templateData.put('pipelineUrl', "${msTeamsbody.blueOceanUrl}")

        return [templateData, jsonTemplateFile]
    }

    static void sendMsTeamsNotifications (def msTeamsbody, def releasesJson, def configuration) {
        TeamsTemplateNotification teamsNotification = new TeamsTemplateNotification()

        def template
        def templateData

        def extraParam = ''

        if (msTeamsbody.version) extraParam = msTeamsbody.version

        // Create message
        (template, templateData) = createMessage(msTeamsbody, releasesJson, extraParam)
        // Create template message
        teamsNotification.setMessageFromTemplate(template, templateData, false)
        // Sets Teams Channel info
        teamsNotification.setChannel(configuration.msTeamsWebhook) // Missing real test channel
        // Send notification
        teamsNotification.send()
    }
}