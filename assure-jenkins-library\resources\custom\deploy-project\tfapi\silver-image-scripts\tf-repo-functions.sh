#
# Parse out the parts of a TF source reference
#
# MUST be called "locally" (not as a subprocess)
#
function parse_tf_git_source_reference {
	local tf_git_source_reference=$1
	if [[ "$tf_git_source_reference" == "/"* ]]; then
		tf_source_ref_type="local"
	else 
		tf_source_ref_type="remote"
	fi

	#
	# Strip off "git::"
	#
	local reference_no_git=${tf_git_source_reference##git::}

	#
	# Strip off the path and ref stuff
	#
	local tf_source_url=${reference_no_git%//*}

	#
	# Isolate the path
	#
	local tf_source_path=${reference_no_git##$tf_source_url//}

	#
	# Get any reference on the path (e.g. embedded branch name)
	# Then dump the reference from the path
	#
	local tf_source_reference=${tf_source_path##*?ref=}
	[[ "$tf_source_reference" == "$tf_source_path" ]] && tf_source_reference=""
	tf_source_path=${tf_source_path%%?ref=*}

	#
	# Expose as env vars
	#
	_TF_SOURCE_REFERENCE_TYPE="$tf_source_ref_type"
	_TF_SOURCE_REPO_URL="$tf_source_url"
	_TF_SOURCE_REPO_REF="$tf_source_reference"
	_TF_SOURCE_PATH="$tf_source_path"
}

function fetch_repo {

	local repo_url="$1"
	local target_dir="$2"
	local repo_type

	if [[ "$repo_url" == "/"* ]]; then
		#
		# This is an absolute local repo path, we just copy it to the target
		#
		mkdir -p "$target_dir" || { echo "unable to create directory $target" >&2; return 1; }
		cp -r "$repo_url"/* "$target_dir"
		repo_type="local"
	else 
		#
		# git clone
		#
		git clone -q "$repo_url" "$target_dir" || { echo "Failed to GIT clone $repo_url into directory $target_dir" >&2; return 1; }
		repo_type="remote"
	fi
	_REPO_TYPE="$repo_type"
}

#
# Checkout a branch of a repo
#
# Must be called locally (i.e. not as a subprocess)
#
function checkout_branch {
	local target_dir="$1"
	local branch_name="${2:-master}"
	local my_cwd=$(pwd)
	local master_branch

	cd "$target_dir" || { echo "Failed to cd to $target_dir" >&2; return 1; }
	if [[ "$branch_name" == "master" ]]; then
		#
		# Politically correct fix since new repos MAY be called "main" or, indeed, something else!
		#
		# This MAY not be needed - we MAY be able to simply change branch_name to HEAD
		#
		# But I prefer to be verbose about this
		#
		master_branch=$(git branch -r --list 'origin/HEAD' | grep "origin/HEAD" | head -n 1 | sed 's%.*/%%') || { echo "Failed to establish name of origin/HEAD branch" >&2; return 1; }
		[[ "$master_branch" ]] || { echo "Failed to establish name of origin/HEAD branch - empty" >&2; return 1; }
		branch_name=$master_branch
		echo "Remote default branch name is: $master_branch" >&2
	fi
	git fetch -q origin "$branch_name" && git checkout -q FETCH_HEAD
	[[ $? -eq 0 ]] || { echo "Failed to checkout branch $branch_name of repo in $target_dir" >&2; return 1; }
	_GIT_REFERENCE=$(git rev-list --max-count=1 HEAD)

	#
	# add a marker
	#
	touch "BRANCH-NAME-IS-${branch_name////_}"

	cd "$my_cwd"
	return $RC
}

#
# Resolve a Git branch version
#
# MUST be called "locally" (not as a subprocess)
#
function resolve_branch_name {
	local target_dir="$1"
	local branch_name="$2"
	local use_version_discovery=${3:-"false"}
	local resolved_branch

	local my_cwd=$(pwd)
	cd "$target_dir" || return 1

	# Possible migration in the future to SHA256 (https://github.com/git/git/blob/master/Documentation/technical/hash-function-transition.txt)
	# sha256=$(echo ${branch_name} | grep -P "\b[0-9a-f]{64,64}\b")
	# sha1=$(echo ${branch_name} | grep -P "\b[0-9a-f]{7,7}\b|\b[0-9a-f]{40,40}\b")
	# This part is meant to deal with source references to specific GIT commits
	# ex:
	# module access {
	# 	source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-allow-rds-access/code?ref=e0ddcf9ecf0214593f0669e4893db2903c670705"
	# ...
  
	sha_tag=$(echo ${branch_name} | grep -P "\b[0-9a-f]{7,7}\b|\b[0-9a-f]{40,40}\b|\b[0-9a-f]{64,64}\b")
	RC=$?
	if [[ $RC -eq 0 ]]; then
		git fetch -q origin ${sha_tag} && resolved_branch=${branch_name} || echo "WARNING: Unable to resolve $branch_name as a valid SHA TAG" >&2
	else
		if [[ "$use_version_discovery" != "true" ]]; then
			resolved_branch=$(git remote show origin | grep "^\\s*$branch_name " | awk '{print $1}' | sort -u)
		else
			#
			# Lop off the last version indicator
			#
			local version_stem=${branch_name%.*}
			resolved_branch=$(git remote show origin | grep "^\\s*${version_stem//\//\\/}" | awk '{print $1}' | awk 'BEGIN{FS = "/"} {split($2,a,"."); for(i in a) printf("%03d",a[i]); print " " $0}' | sort -rn | awk '{print $2}' | head -1)
		fi
	fi

	cd "$my_cwd"
	[[ "$resolved_branch" ]] || echo "WARNING: Unable to resolve $branch_name into an existing branch; version discovery = $use_version_discovery" >&2
	[[ "$resolved_branch" ]] && echo "Branch $branch_name resolved to branch $resolved_branch; version discovery = $use_version_discovery" >&2

	_GIT_BRANCH_NAME="$resolved_branch"
}

#
# Establish branch based on various criteria
#
# Must be called locally (i.e. not as a subprocess)
#
function establish_branch_name {
	local target_dir="$1"
	local branch_name="${2:-master}"
	local branch_mode="${3:-fixedversion}"

	[[ "$branch_mode" != "fixedversion" && $branch_name =~ ^release*\/[0-9][0-9]*.[0-9][0-9]*.[0-9][0-9]*$ ]] && use_version_discovery="true"

	resolve_branch_name "$target_dir" "$branch_name" "$use_version_discovery"
	RC=$?
	[[ $RC -ne 0 ]] && { echo "Error attempting to find branch $branch_name of repo in $target_dir" >&2; return 1; }
	if [[ -z "$_GIT_BRANCH_NAME" ]]; then
		echo "Failed to find branch $branch_name of repo in $target_dir" >&2
		if [[ "$branch_mode" == "failsafe" ]]; then
			echo "Branch mode is 'failsafe'; reverting to 'master' branch" >&2
			_GIT_BRANCH_NAME=master
		else
			exit 1;
		fi
	fi
}

function cleanup_git_repo {
	( cd "$1" && rm -rf .git .github )
}
