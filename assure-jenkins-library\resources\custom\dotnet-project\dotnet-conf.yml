##### Notification (post)
sendMail: "false"
emailFrom: "<EMAIL>"
emailTo: ""
attachmentFileEmail: ""
#teamsSecretHookId: "TEAMS_PUSH_WEBHOOK"

##### Artifactory
#artifactoryURL: "" // Not required as taking PDXC default URL
artifactoryCredentials: "diaas-rw"
artifactRepository: "diaas-generic"
artifactLocalPath: ""
artifactProperties:
  - prop: "status"
    value: "ready"
  - prop: "type"
    value: "generic"

#dotNet
version: "0.0.0.0"

##### Zip (zip)
zipInclude: ""
zipScript: ""

##### Checkov
checkov_conf_file: "checkovConfiguration.yml"
passphrase_id: "PASSPHRASE_ID"
checkovQualityGate: false

##### SonarQube
sonarSources: "."
sonarExclusions: "**/bin/**/*,**/obj/**/*, **/*.js, **/*.css, **/*.cshtml.cs, pipelinesTempFiles/*"
sonarQualityGate: false
sonarCoverage: "sonar.cs.opencover.reportsPaths"
sonarCoveragePath: "coverage.xml"

###### Environment service configuration
env_service_conf_file: "envServData.yml"

###### Release Notes Auto-Generation
releaseNotes: false

###### .Net version
dotnetVersion: 7