package org.assure.pushpipeline

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.Utils
import org.pdxc.util.ValuesUtils
import org.pdxc.util.DefaultConfiguration
import org.pdxc.notification.MailNotification
import groovy.json.JsonOutput

/**
 * Utility class used to define a set of functions that can be reused across the different Assure pipelines but that
 * are too specific to be moved to a Global Jenkins Shared Library.
 */

class CheckovPipelineUtils {

    private CheckovPipelineUtils() {}

    def context = JenkinsContext.getContext()

    void setCheckovDockerAgent(String dockerPath, String dockerTargetName) {
        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        def dockerFile
        if (exists) {
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: "conf.yml"
            def pipelineType = config.pipelineType
            def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)
            dockerFile = context.libraryResource "custom/${projectType}-project/${dockerTargetName}"   
        }
        context.writeFile file: dockerTargetName, text: dockerFile
    }

    def pipelineInfoSteps(String pipelineName) {
        def conf = context.libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
        context.writeFile file: "${pipelineName}-conf.yml", text: conf
        def defaultConfigYml = context.readYaml file: "${pipelineName}-conf.yml"
        def repoData = context.readYaml file: 'conf.yml'

        def configData = defaultConfigYml + repoData

        context.println 'Loaded configuration values: \n\n' + JsonOutput.prettyPrint(JsonOutput.toJson(configData))
        context.writeYaml file: 'conf.yml', data: configData, overwrite: true

        return configData
    }

    def getRepositories(def listName) {
        def repositories
        listName = ValuesUtils.removeStartEndChars(listName, '/', true, true)
        def listPath = "${listName}.yml"
        def exists = context.fileExists listPath
        if (!exists) {
            context.error("-- ❌️ -- No list file was selected. Please check your configuration")
        } else {
            context.echo("📋 Preparing the repository list...")
            def repoData = context.readYaml file: listPath
            repositories = ValuesUtils.getVariableArrayList(repoData, 'repositories', 'update')
            return repositories
        }
    }

    def cleanRepositoriesList(def repositories) {
        def cleanedRepositories = []
        def failedRepositories = []

        context.echo("📋 Checking the repository list...")

        repositories.each { repo ->
            def modifiedRepo = [:]
            def existingRepo
            if (repo.repoName != null && repo.repoName != '') {
                def scanOrganizationName = (repo.organization != null && repo.organization != '') ? repo.organization : 'assure'
                def scanBranchName = (repo.branch != null && repo.branch != '') ? repo.branch : 'master'

                modifiedRepo.put('repoName', repo.repoName)
                modifiedRepo.put('organization', scanOrganizationName)
                modifiedRepo.put('branch', scanBranchName)

                cleanedRepositories.add(modifiedRepo)    
            } else {
                failedRepositories.add(repo)  
            }
        }
        return [cleanedRepositories, failedRepositories]
    }

    void cloneRepository(def configData, def repository) {
        def branch = repository.branch 
        def organization = repository.organization
        def repoName = repository.repoName
        context.withCredentials([context.usernamePassword(credentialsId: configData.gitHubCredential, passwordVariable:'GITHUB_PASSWORD', usernameVariable:'GITHUB_USER')]) {
            context.sh(script: """
            git clone -b ${branch} https://${context.env.GITHUB_USER}:${context.env.GITHUB_PASSWORD}@${DefaultConfiguration.PDXC_GITHUB_HOST}/${organization}/${repoName}.git ${repoName}                                    
        """, label: 'Cloning the repository...')
        }
    }

    void scanRepository(def configData, def repository, def dockerName, def uuidCheckov, def currentTime) {
        def branch = repository.branch 
        def organization = repository.organization
        def repoName = repository.repoName

        checkovScan(repoName, uuidCheckov, dockerName, currentTime) 
    }

    void checkovScan (String repoName, def uuid, def dockerName, def currentTime) {

        try {        
            def checkovScript = context.libraryResource(resource: "custom/checkov-project/checkov-scan.sh", encoding: "Base64")
            context.writeFile(file: "pipelinesTempFiles/checkov.sh", text: checkovScript, encoding: "Base64")

            def skipPolicies = context.libraryResource "custom/checkov-project/skipPolicies.txt"
            
            def outputScript = context.sh (
                        script: """ 
                                set +x
                                bash +x pipelinesTempFiles/checkov.sh ${repoName} ${uuid} ${dockerName} ${skipPolicies}
                                set -x
                                """,
                        returnStdout: true,
                        encoding: 'UTF-8',
                        label: 'Executing Checkov scan').trim()

            outputScript = outputScript.replaceAll("(?m)^(\\s)*(<|;).*(\r\n|\r|\n)?", "")
            outputScript = outputScript.replaceAll("(?m)^(\\s)*(---|;).*(\r\n|\r|\n)?", "");
            context.echo "-- ℹ️ -- Checkov scan results \n ${outputScript}"
            outputScript = outputScript.replaceAll("[^\\x00-\\x7F]","");
            def checkovFileName = "checkov-scan-results-${repoName}_${uuid}.log" 
            context.writeFile(file: "${checkovFileName}", text: "${outputScript}")
            context.archiveArtifacts artifacts: "${checkovFileName}"

            def checkovLogName = "checkov-scan-results-${repoName}_${currentTime}.log" 
            context.writeFile(file: "./logs/${checkovLogName}", text: "${outputScript}")

            // Remove script
            context.sh(script: """ 
                rm -rf pipelinesTempFiles/checkov.sh
                rm -rf pipelinesTempFiles/passphrase.txt
                rm -rf pipelinesTempFiles/checkovConfiguration.yml_${uuid}
                rm -rf pipelinesTempFiles/checkovConfiguration.yml_${uuid}.gpg
            """, label: "Remove checkov secrets")

        }
        catch (Exception e) {
            context.echo("-- ⚠️ -- Something went wrong during Checkov Scan Analysis. ${e.getMessage()}")
        }       
    }

    def checkovErrorCheck (def repoName, def uuidCheckov) {
        def checkovTestsStatus = []
        try {
            List testValues = context.readFile("checkov-scan-results-${repoName}_${uuidCheckov}.log").split('scan results:')

            testValues.each { value -> 
                if (value.contains("By bridgecrew.io")) {
                    checkovTestsStatus.add('skip')
                } else if (value.contains("Failed checks: 0")) {
                    checkovTestsStatus.add('ok')
                } else if (value.contains("Failed checks:")){
                    checkovTestsStatus.add('fail')
                } else {
                    checkovTestsStatus.add('ok')
                }
            }

            if (checkovTestsStatus.contains('fail')) {
                return 1
            } else {
                return 0
            }

        } catch (Exception e) {
            context.echo("-- ⚠️ -- Something went wrong during the checkov log analysis. ${e.getMessage()}")
        }
    }

    void sendEmailNotifications (List<String> emailRecipients, def successScans, def failedScans, def currentTime) {
        context.echo("📧 Sending the generated logs...")
        // Builds the mail and sends the notification
        if (emailRecipients != null && emailRecipients.size() > 0) {
            MailNotification mail = new MailNotification()

            String emailTo = emailRecipients.join(',')
            def emailFrom = "<EMAIL>"
            def subject = "-- 🤖 -- Checkov scans from the Checkov Utility Pipeline"
            def emailBody = composeNotificationEmail( successScans, failedScans)
            def reportFile = "Checkov_logs-${currentTime}.zip"

            mail.configureMail(emailTo, emailFrom, subject, emailBody, reportFile)
            mail.send()   
        } else {
            context.echo("-- ⚠️ -- No email recipients were selected. Check your config file.")
        }
    }

    void compressLogs(def currentTime) {
        context.echo("📁 Compressing the generated logs...")

        def logFileName = "Checkov_logs-${currentTime}.zip"
        context.zip(glob: "", zipFile: "${logFileName}" , dir: "./logs/")
        context.archiveArtifacts artifacts: "${logFileName}"
    }

    def composeNotificationEmail(def successScans, def failedScans) {
        def header = '''
                    <!DOCTYPE html><html lang="en"><head>
                    <meta charset="UTF-8" /><meta http-equiv="X-UA-Compatible" content="IE=edge" /><meta name="viewport" content="width=device-width, initial-scale=1.0" /><title>Checkov Scan Results</title></head><style>
                    #emailTable, #emailFooter {border: 0;height: 100%;width: 100%;border-spacing: 0;padding: 0;}
                    #emailContainer {border-collapse: collapse;font-family: Tahoma, Geneva, sans-serif;}
                    #emailContainer td {padding: 15px;}
                    #emailContainer thead th {background-color: #54585d;color: #ffffff;font-weight: bold;font-size: 12px;border: 1px solid #54585d; text-align: center; padding: 15px;}
                    #emailContainer tbody td {color: #636363;border: 1px solid #dddfe1;}
                    #emailContainer tbody tr {background-color: #f9fafb; border-bottom: 1px solid grey; padding: 0; text-align: center;}
                    #emailContainer tbody tr:nth-child(odd) {background-color: #ffffff;}
                    </style><body><table id="emailTable">
                    <th><h2>Checkov Utility Pipeline Scans Results</h2></th>
                    <tr><td align="center" valign="top">
                    <p>Below you can see the scans of the selected repositories executed in the Checkov Utility Pipeline.</p>
                    <table id="emailContainer"><thead><tr>
                    <th>🗃️ Scanned repositories</th>
                    <th>📜 Status</th>
                    </tr></thead><tbody>
                     '''
        def footer = '''
                    </tbody></table></td></tr></table></td><br />
                    <table border="0" cellpadding="0" cellspacing="0" width="100%" id="emailFooter"><tr>
                    <td align="center" valign="top">
                    <i> This is a message generated automatically by the Checkov Utility Pipeline,
                    please do not reply to this email.</i><br /><br />
                    <i> If you need assistance or clarification on any of the topics covered in this
                    email, please use the established channels to contact the <b>Automation team</b>.
                    </i></td></tr></table></body></html>
                     '''

        def body = ''
        if (successScans != null && successScans.size() > 0) {
            successScans.each { scan ->
                body += """
                    <tr>
                    <td>${scan}</td>
                    <td style='color:green'>SUCCESS</td>
                    </tr>
                """
            }
        }
        if (failedScans != null && failedScans.size() > 0) {
            failedScans.each { scan ->
                body += """
                    <tr>
                    <td>${scan}</td>
                    <td style='color:red'>FAILURE</td>
                    </tr>
                """
            }
        }
        
        def message = header + body + footer
        return message
    }

}