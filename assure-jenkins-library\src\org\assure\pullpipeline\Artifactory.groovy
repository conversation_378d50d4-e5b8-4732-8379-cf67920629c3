package org.assure.pullpipeline

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.util.ValuesUtils
import org.pdxc.util.DefaultConfiguration

/**
* Wrapper class for Artifactory API.
*/
class Artifactory implements Serializable {

    ArtifactoryApi api;
    /* groovylint-disable-next-line FieldTypeRequired, NoDef */
    def context

    Artifactory(String url, String credential) {
        context = JenkinsContext.getContext()
        api = new ArtifactoryApi(url, credential)
    }

    /**
     * Compose AQL query and run it against the configured Artifactory instance to retrieve the list of artifacts to
     * be treated by the Pull pipeline.
     * @param query Configuration data.
     * @return List of artifacts to be processed.
     */
    List performAQLQuery(String query) {
        try {            
            // return api.executeAQL(query)

            // TEMP - increase timeout
            def url = 'https://artifactory.platformdxc-mg.com/artifactory' + '/api/search/aql'
            List validStatuses = [200]
            def response
            
            try {
                int maxRetries = 3
                int retryCount = 0 
                def statusResponse = 0 
                def httpResponse                             
                
                while (retryCount < maxRetries && statusResponse != 200) {
                    httpResponse = context.httpRequest url: url,
                                acceptType: 'APPLICATION_JSON',
                                authentication: 'diaas-rw',
                                contentType: 'TEXT_PLAIN',
                                customHeaders: [],
                                httpMode: 'POST',
                                requestBody: query,
                                timeout: 180,
                                validResponseCodes: '200:599'

                    statusResponse = httpResponse.status                                       
                } 
                
                
                // REMOVE
                context.echo "HTTP response status: ${httpResponse.status}"
                context.echo "HTTP response content: ${httpResponse.content}"
                if (validStatuses.indexOf(httpResponse.status) == -1) {                    
                    context.error('Error during aql request')
                }

                if (httpResponse.content != null) {
                    try {
                        response = context.readJSON text: httpResponse.content
                    } catch (Exception e) {
                        context.echo 'Response could not be parsed as JSON, creating custom response with content'
                        response = ['response': httpResponse.content]
                    }
                } else {
                    response = ['status': httpResponse.status]
                }
                
            } catch (Exception e) {                
                context.error("Failed to send data to ${url} via POST. Params: ${query}. Error: ${e}")
                throw e
            }
            
            def returnList = null
            if (response != null && response.results != null) {
                returnList = response.results
            }
            return returnList
        } catch (IllegalArgumentException) {
            context.error("-- ❌ -- Empty artifactory query. Error: ${error.message}")
        } catch (Exception error) {
            context.error("-- ❌ -- Error getting list of deployable Artifacts. Error: ${error.message}")
        }
    }

    String composeAQL(Map config) {
        String query = ''
        List repositories = ValuesUtils.getVariableArrayList(config, 'artifactory_repos')
        String splitter = ','
        String splitterRepos = ''
        repositories.each { repo ->
            query += splitterRepos
            splitterRepos = splitter
            query += "{ \"\$and\": [ {\"repo\":\"${repo}\"},"

            String repoType = ValuesUtils.getVariable(config, "artifactory_${repo}_repotype")
            query += ('docker' == repoType) ? '{\"name\" : \"manifest.json\"},' : ''

            String splitterOr = ''
            List paths = ValuesUtils.getVariableArrayList(config, "artifactory_${repo}_paths")
            String splitterPaths = ''
            String splitterBranches = ''
            if (!paths?.isEmpty()) {
                query += splitterOr
                splitterOr = splitter
                query += '{\"\$or\": ['
                paths.each { path ->
                    String pathBranch = path.replaceAll ("/", "-")
                    List branches = ValuesUtils.getVariableArrayList(config, "artifactory_${repo}_${pathBranch}_branches")
                    if (!branches?.isEmpty()) {
                        branches.each { branch ->
                            query += splitterBranches
                            splitterBranches = splitter
                            query += "{\"\$and\": [{\"path\":{\"\$match\":\"${path}/*\"}},"
                            query += "{\"path\":{\"\$match\":\"*${branch}*\"}}]}"
                        }
                    } else {
                        query += splitterPaths
                        splitterPaths = splitter
                        query += "{\"path\":{\"\$match\":\"${path}/*\"}}"
                    }
                }
                query += ']}'
            } else {
                List branches = ValuesUtils.getVariableArrayList(config, "artifactory_${repo}_branches")
                if (!branches?.isEmpty()) {
                    query += splitterPaths
                    splitterPaths = splitter
                    query += '{\"\$or\": ['
                    branches.each { branch ->
                        query += splitterBranches
                        splitterBranches = splitter
                        query += "{\"path\":{\"\$match\":\"*/${branch}\"}}"
                    }
                    query += ']}'
                }
            }

            List statuses = ValuesUtils.getVariableArrayList(config, "artifactory_${repo}_status")
            if (!statuses?.isEmpty()) {
                query += ', {\"\$or\": ['
                String splitterStatus = ''
                statuses.each { status ->
                    query += splitterStatus
                    splitterStatus = splitter
                    query += "{\"@status\":\"${status}\"}"
                }
                query += ']}'
            }
            String nodeploy = ValuesUtils.getVariable(config, 'artifactory_nodeployprop')
            if ('true' == nodeploy) {
                query  += ', {\"@deploy-in-progress\":{\"\$nmatch\":\"true\"}}'
            }
            query += ']}'
        }
        if ('' == query) {
         context.error('❌  No repository was selected. At least one has to be defined.')
        }
        String result = 'items.find ({ \"\$or\": ['
        result += query
        result += ']}).include(\"@deployment-packages\", \"@status\", \"@deploy-in-progress\", '
        result += '\"@type\", \"@git_url\", \"@commit\", \"@committer\")'

        return result
    }

    /**
    * Orchestrate the AQL generation and invokation.
    * @param configuration Values to be used.
    * @return List of artifacts found.
    */
    List manageAQLQuery(Map configuration) {
        return performAQLQuery(composeAQL(configuration))
    }

    /**
     * For the given list of artifacts, invoke Artifactory API to set the "deploy-in-progress" flag to True / False
     * so they are blocked from new deployments / made available for new deployment. This will mark whether an
     * artifact can be taken again by the PULL pipeline in a later iteration.
     *
     * It returns the list of artifacts that could be blocked / unblocked.
     *
     * @param artifacts List of artifacts to be updated.
     * @param block True to block and False to unblock
     * @return List of artifacts where operation was successfully applied.
     */
    List blockArtifacts(List<Map> artifacts, boolean block) {
        def artifactsBlocked = artifacts.collect { artifact ->
            try {                
                blockArtifact(artifact, block)
                return artifact
            } catch (Exception error) {
                context.echo "Unable to block artifact ${artifact.name}. Error: ${error}"
            }
        }
        if (artifactsBlocked == null || artifactsBlocked.size() == 0) {
            // Utils.throwExceptionInstance("Exception", "-- ❌ -- No artifact could be blocked.")
            context.echo "-- ❌ -- No artifact could be blocked."
        }
        return artifactsBlocked
    }

    /**
     * For the given artifact, invoke Artifactory API to set the "deploy-in-progress" flag to True / False
     * so it is blocked from new deployments / made available for new deployment. This will mark whether an
     * artifact can be taken again by the PULL pipeline in a later iteration.
     *
     * @param artifact Artifacts to be updated.
     * @param block True to block and False to unblock
     */
    void blockArtifact(Map artifact, boolean block) {
        api.updateArtifactProperties(
                artifact.repo as String,
                artifact.path as String,
                artifact.name as String,
                [[prop: "deploy-in-progress", value: "${block}"]] as ArrayList<LinkedHashMap>
        )
    }

    /**
     * Update status property for a list of artifacts.
     * @param artifacts List of artifacts.
     * @param status New status.
     */
    void updateArtifactsStatus(List<Map> artifacts, String status) {
        def properties = [[prop: "status", value: status]]
        updateAllArtifactsProperties(artifacts, properties as ArrayList<LinkedHashMap>)
    }

    /**
     * Set up new status and unblock artifacts.
     * @param artifacts List of artifacts.
     * @param status New status to be set.
     */
    void deployArtifactsCompleted(List<Map> artifacts, String status) {
        def properties = [[prop: "deploy-in-progress", value: "false"], [prop: "status", value: status]]
        updateAllArtifactsProperties(artifacts, properties as ArrayList<LinkedHashMap>)
    }

    /**
     * Iterate through the complete list of artifacts updating the given properties.
     * @param artifacts List of artifacts.
     * @param properties Properties to be set up.
     */
    void updateAllArtifactsProperties(List<Map> artifacts, ArrayList<LinkedHashMap> properties) {
        artifacts.each { Map artifact ->
            api.updateArtifactProperties(
                    artifact.repo as String,
                    artifact.path as String,
                    artifact.name as String,
                    properties
            )
        }
    }

}