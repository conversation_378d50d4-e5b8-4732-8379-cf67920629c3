# Standard library imports
import json
import os
import sys
from urllib.parse import urlencode

# Add custom site-packages directory for AWS Lambda Layers
sys.path.insert(1, 'site-packages/')

# AWS Lambda Powertools for structured logging
from aws_lambda_powertools import Logger  # import layer AWSLambdaPowertoolsPythonV3-python311-x86_64

# Local modules for DynamoDB management and code ingestion
import dynamoDB_manager
import ingestion

# Initialize logger for observability and debugging
logger = Logger(service="Code-Analyzer")

# Initialize the DynamoDB table manager using schema file
db_manager = dynamoDB_manager.DynamoDBTableManager("project_table_schema.yaml")

# Define the composite key fields required for identifying a configuration uniquely
key_fields = ["client_name", "application_instance", "application_type", "application_version"]


def _parse_event(event):
    """
    Parse the Lambda event to extract body and query parameters.

    Args:
        event (dict): The event object from API Gateway.

    Returns:
        dict: Combined dictionary of body and query parameters.
    """
    body = event.get("body")
    if body:
        try:
            body = json.loads(body) if isinstance(body, str) else body
        except json.JSONDecodeError:
            body = {}
    else:
        body = {}

    query = event.get('queryStringParameters') or {}
    return {**body, **query}


def _extract_key_params(data):
    """
    Extract the composite key values from the input data.

    Args:
        data (dict): Input data from request.

    Returns:
        dict or None: Dictionary with key fields or None if any key is missing.
    """
    if not all(data.get(field) for field in key_fields):
        return None
    return {field: data[field] for field in key_fields}


def _response(status_code, body=None, headers=None):
    """
    Construct an HTTP response compatible with API Gateway.

    Args:
        status_code (int): HTTP status code to return.
        body (dict or str, optional): Response body content.
        headers (dict, optional): Additional headers if any.

    Returns:
        dict: Formatted HTTP response object.
    """
    headers = headers or {"Content-Type": "application/json"}
    return {"statusCode": status_code,
            "headers": headers,
            "body": json.dumps(body) if body is not None else ""}


def validate_post_put_input(data):
    """
    Validate the request payload for POST and PUT operations.

    Args:
        data (dict): Payload from the client request.

    Returns:
        list: List of error messages if validation fails.
    """
    errors = []

    # Default 'created_by' if not provided
    if not isinstance(data.get("created_by"), dict):
        data["created_by"] = "UNKNOWN_USER"

    # Validate top-level ingestion object
    ingestion_data = data.get("ingestion")
    if not isinstance(ingestion_data, dict):
        return ["1. Missing or invalid 'ingestion' object. Should be a dictionary."]

    # Validate required key fields
    for field in key_fields:
        if not isinstance(data.get(field), str) or not data[field].strip():
            errors.append(f"'{field}' is required and must be a non-empty string.")

    # Validate 'ingested_code_s3_uri'
    ingested_code_s3_uri = data.get("ingested_code_s3_uri")
    if not isinstance(ingested_code_s3_uri, str) or not ingested_code_s3_uri.strip():
        errors.append("'ingested_code_s3_uri' is required and must be a non-empty string.")

    # Validate 'code_path' inside ingestion block
    code_path = ingestion_data.get("code_path")
    if not isinstance(code_path, str) or not code_path.strip():
        errors.append("'code_path' is required and must be a non-empty string.")

    # Validate 'apply_filters' and nested 'filters' if applicable
    apply_filters = ingestion_data.get("apply_filters")
    if not isinstance(apply_filters, bool):
        errors.append("'apply_filters' must be a boolean.")
    else:
        # If apply_filters is True, validate 'filters'
        if apply_filters:
            filters = ingestion_data.get("filters")
            if not isinstance(filters, dict) or not filters:
                errors.append("'filters' must have atleast one filter when 'apply_filters' is True.")
            else:
                # Check for at least one non-empty filter, e.g., 'files'
                valid_filters = any(isinstance(v, list) and len(v) > 0 for v in filters.values())
                if not valid_filters:
                    errors.append("At least one non-empty filter is required in 'filters' "
                                  "when 'apply_filters' is True.")

    # Format the errors with numbering
    return [f"{i + 1}. {e}" for i, e in enumerate(errors)]


def validate_get_delete_input(data):
    """
    Validate input for GET and DELETE requests.

    Args:
        data (dict): Input data.

    Returns:
        list: List of validation error messages.
    """
    errors = []
    for field in key_fields:
        if not isinstance(data.get(field), str) or not data[field].strip():
            errors.append(f"'{field}' is required and must be a non-empty string.")
    return [f"{i + 1}. {e}" for i, e in enumerate(errors)]


def get_environment_variable(name, default=None):
    """
    Retrieve a required environment variable with optional default.

    Args:
        name (str): Environment variable name.
        default (str, optional): Default value if variable is not set.

    Returns:
        str: Environment variable value.

    Raises:
        ValueError: If variable is not found and no default is given.
    """
    var_value = os.environ.get(name, default)
    if not var_value:
        raise ValueError(f"Missing required environment variable: {name}")
    return var_value


@logger.inject_lambda_context(log_event=True, clear_state=True)
def lambda_handler(event, context):
    """
    Main Lambda entry point for handling RESTful CRUD operations via API Gateway.

    Handles GET, POST, PUT, DELETE methods on configuration data.

    Args:
        event (dict): Event from API Gateway.
        context (LambdaContext): AWS Lambda context.

    Returns:
        dict: API Gateway-compliant response object.
    """
    try:
        http_method = event["httpMethod"]
        data = _parse_event(event)
        key_config = {}

        # GET and DELETE require key fields to identify resource
        if http_method in ("GET", "DELETE"):
            errors = validate_get_delete_input(data)
            if errors:
                return _response(400, {"validation_errors": errors})

            key_config = _extract_key_params(data)
            if not key_config:
                return _response(400, {"error": f"Missing required parameters: {key_fields}"})

        # Retrieve necessary environment variables
        s3_bucket_name = get_environment_variable("S3_BUCKET_NAME")
        application_configuration_file = get_environment_variable("APPLICATION_CONFIGURATION_FILE")

        # --- Handle GET ---
        if http_method == "GET":
            result = db_manager.get_config(**key_config)
            return _response(result["statusCode"], json.loads(result["body"]))

        # --- Handle POST ---
        elif http_method == "POST":
            if "ingested_code_s3_uri" not in data:
                data["ingested_code_s3_uri"] = "TO_BE_INGESTED"

            errors = validate_post_put_input(data)
            if errors:
                return _response(400, {"validation_errors": errors})

            key_config = _extract_key_params(data)
            if db_manager.config_exists(**key_config):
                return _response(409, {"error": "Configuration already exists."})

            # Execute ingestion process and persist configuration
            data["ingested_code_s3_uri"] = ingestion.execute(data, s3_bucket_name, application_configuration_file)
            result = db_manager.create_config(**data)
            body = json.loads(result["body"])

            # Construct Location header for newly created resource
            query_params = {field: data[field] for field in key_fields}
            location = f"/config?{urlencode(query_params)}"

            return _response(201, body, headers={"Content-Type": "application/json",
                                                 "Location": location,
                                                 "Content-Location": location})

        # --- Handle PUT ---
        elif http_method == "PUT":
            errors = validate_post_put_input(data)
            if errors:
                return _response(400, {"validation_errors": errors})

            key_config = _extract_key_params(data)
            if not db_manager.config_exists(**key_config):
                return _response(404, {"error": "Configuration not found."})

            data["ingested_code_s3_uri"] = ingestion.execute(data, s3_bucket_name, application_configuration_file)
            result = db_manager.update_config(**data)
            return _response(result["statusCode"], json.loads(result["body"]))

        # --- Handle DELETE ---
        elif http_method == "DELETE":
            if not db_manager.config_exists(**key_config):
                return _response(404, {"error": "Configuration not found."})

            result = db_manager.delete_config(**key_config)
            status = result.get("statusCode", 500)
            body = result.get("body")

            if status == 200:
                return _response(204)  # No content, success

            try:
                parsed_body = json.loads(body) if isinstance(body, str) and body else body
            except Exception:
                parsed_body = {"error": "Failed to parse error body", "raw": str(body)}

            return _response(status, parsed_body)

        # --- Handle Unsupported Methods ---
        else:
            return _response(405, {"error": "Method Not Allowed"})

    except Exception as e:
        # Global exception catch to prevent Lambda failure and provide detailed logs
        logger.exception("Unhandled exception occurred")
        return _response(500, {"error": "Internal server error", "details": str(e)})
