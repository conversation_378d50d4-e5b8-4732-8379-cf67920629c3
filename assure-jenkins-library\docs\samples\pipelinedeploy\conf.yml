############################################################################################################
####################################### MODIFICATION AREA ###################################################
######################## Modify these values according to your project needs.  ##############################
### Rest of customizable values are listed in README.md section 2.1.3 Common custom configuration values  ###
############################## Only modify them is special behaviour is needed ##############################
#############################################################################################################

#### Name of current repository for validation. It has to match the name of the repository where this file is.
repositoryName: "name-of-current-repository-validation"

pipelineType: "TERRAFORM_DEPLOY_PIPELINE_12" #only required if you are using tf12 or newer versions

#### Artifactory
artifactPath: "assure-platform/artifactpath"  #i.e: "assure-platform/data-pipeline/merge-etl-output-lambda"
artifactoryFileName: "name-of-descriptor"     #i.e: "case-management-lambda"

# Do we want to disable promotion of the generated artifact (silver image) in our DXC Assure Platform lifecycle
# (prevent the generation of the descriptor in Artifactory to be retrieved from Jenkins Pull pipeline): true / false
lifecycleDisabled: "false"

#############################################################################################################
################################### END OF MODIFICATION AREA ################################################
#############################################################################################################

##### Name of the organization where the Siver Image has to be created.
silverImageOrganization: "assure-terraform-images" # "dxc-assure-tfapi" for legacy TF11

##### GitHub data #####
gitHubCredential: "assure-github" # for assure, assure-external or assure-delivery. Legacy value was "pdxc-jenkins"
gitEmail: "<EMAIL>"
gitUsername: "Jenkins User"
