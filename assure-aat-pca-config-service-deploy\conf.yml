# Name of current repository for validation. It has to match the name of the repository where this file is.
repositoryName: "assure-aat-pca-config-service-deploy"

#### Upgrading Terraform version
terraform:
  version: 1.4  # 0.13 -> 1.4

# Pipeline to be executed. It has to match the constants defined in the Assure Library (pipelineRunner.groovy).
# It does not need to be modify for standard -deploy projects (value='TERRAFORM_DEPLOY_PIPELINE')
pipelineType: "TERRAFORM_DEPLOY_PIPELINE_12"

# Do we want to disable promotion of the generated artifact (silver image) in our DXC Assure Platform lifecycle
# (prevent the generation of the descriptor in Artifactory to be retrieved from Jenkins Pull pipeline): true / false
lifecycleDisabled: "false"

##### Name of the organization where the Siver Image has to be created.
silverImageOrganization: "assure-terraform-images"

##### GitHub data #####
gitHubCredential: "assure-github" # for assure, assure-external or assure-delivery org the value is: "assure-github"
gitEmail: "<EMAIL>"
gitUsername: "Jenkins User"

#### Artifactory
artifactoryFileName: "assure-aat-pca-config-service"
artifactRepository: "assure-generic"
artifactoryPath: "assure-platform/assure-aat-pca-config-service/lambda/deploy"

modulesReleaseVersion: "release/25.1.0"

#############################################################################################################
################################### END OF MODIFICATION AREA ################################################
#############################################################################################################
