# ---------------------------------------------------------------------------------------------------------------------
# Global Variables
# These variables that contain information about the deployment
# ---------------------------------------------------------------------------------------------------------------------

// Required
variable "client_name" {
  description = "Name of the client we are deploying to"
  type        = string
}

variable "client_short_name" {
  description = "Short name (no spaces or special characters) of the client we are deploying to"
  type        = string
}
variable "environment_name" {
  description = "Name of the environment we are deploying to"
  type        = string
}

variable "environment_id" {
  description = "Id of the environment amongst many environments with the same environment_name, to which are deploying to"
  type        = string
}

variable "artifact_branch_name" {
  description = "The name of the branch from which to obtain artifacts"
  type        = string
  default     = "master"
}

#
# PDXC TAG REQUIREMENTS
#
# All tagable resources need these
#
variable "BusinessServiceCI" {
  description = "ServiceNow Business Service CI name"
  type        = string
  default     = ""
}
variable "creator" {
  description = "Name of the person who is deploying the terraform script"
  type        = string
}

variable "pdxc_application_id" {
  description = "PDXC application ID. TAG NAME: application-id"
  type        = string
}

variable "pdxc_environment" {
  description = "PDXC environment ID. TAG NAME: environment"
  type        = string
}

variable "pdxc_ooss_compliance" {
  description = "PDXC application OOSS compliance. TAG NAME: ooss-compliance"
  type        = string
}

variable "pdxc_owner" {
  description = "PDXC application owner. TAG NAME: owner"
  type        = string
}

variable "pdxc_approval_group" {
  description = "PDXC approval group. TAG NAME: approval-group"
  type        = string
}

variable "pdxc_support_group" {
  description = "PDXC support group. TAG NAME: support-group"
  type        = string
}

variable "configuration_company" {
  description = "DIaaS Configuration Company. TAG NAME: configuration-company"
  type        = string
}

variable "configuration_department" {
  description = "DIaaS Configuration Department. TAG NAME: configuration-department"
  type        = string
}

variable "configuration_domain" {
  description = "DIaaS Configuration Domain. TAG NAME: configuration-domain"
  type        = string
}

locals {
	environment_name = "${var.environment_id == "" ? lower(var.environment_name) : "${lower(var.environment_name)}-${var.environment_id}"}"
	client_short_name = "${var.client_short_name == "" ? lower(var.client_name) : lower(var.client_short_name)}"
}

#
# The common tag map
#
# Use this to merge with other tags:
#
#    tags = "${merge(map("Name", self.name), var.resource_tags)}"
#
locals {
    resource_tags = {
	    "client"            = "${var.client_name}"
	    "client-id"         = "${local.client_short_name}"
	    "creator"           = "${var.creator}"
		"branch-name"       = "${var.artifact_branch_name}"
	    "environment"       = "${local.environment_name}"
	    "environment-name"  = "${var.pdxc_environment}"
	    "environment-id"    = "${var.environment_id == "" ? "undefined" : var.environment_id}"
	    "ooss-compliance"   = "${var.pdxc_ooss_compliance}"
	    "owner"             = "${var.pdxc_owner}"
	    "application-id"    = "${var.pdxc_application_id}"   

	    "approval-group"    = "${var.pdxc_approval_group}"    
	    "support-group"     = "${var.pdxc_support_group}"    
        "configuration-company" = "${var.configuration_company}"
        "configuration-department" = "${var.configuration_department}"
        "configuration-domain" = "${var.configuration_domain}"

	"business-service-ci" = "${var.BusinessServiceCI == "" ? var.local_BusinessServiceCI : var.BusinessServiceCI}",
	"ServiceNowAttributes" = "u_level_of_service:${var.client_name} u_used_for:${local.environment_name}"
	"DiscoveryBlackList"="False"
	}
}