# * S3 Bucket

module "pca_bucket" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-create-s3-bucket/code"

  platform_vars = {
    client_name         = "${local.client_short_name}"
    environment_name    = "${local.environment_name}"
    aws_account         = "${local.aws_target_account}"
    aws_region          = "${var.aws_region}"
    resource_tag_keys   = "${join(",", keys(local.resource_tags))}"
    resource_tag_values = "${join(",", values(local.resource_tags))}"
  }

bucket_name = var.service_name
enable_versioning = "true"  
 # ? CORS rule enable
 enable_cors = "true"
 cors_rule = {
    # allowed_origins = ["*"]
    # allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE"]
  }
}

# Upload tar file

resource "aws_s3_object" "upload_prompts_template" {
  for_each = fileset("${path.module}/${local.pca_filepath}", "**")
  bucket = module.pca_bucket.bucket_id
  key    = "pca/${each.key}"
  source = "${path.module}/${local.pca_filepath}/${each.value}"
  etag   = filemd5("${path.module}/${local.pca_filepath}/${each.value}")
lifecycle {
    ignore_changes = [source, etag]
  }
}


resource "aws_s3_object" "empty_directory_structure" {
  for_each     = toset(local.pca_empty_directories)
  bucket       = module.pca_bucket.bucket_id
  key          = "pca/${each.value}/"
  content_type = "application/x-directory"
}