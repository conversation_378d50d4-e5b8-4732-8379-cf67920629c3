FROM node:0.0.0-alpine

ENV INSTALL_CHECKOV=true
ENV PIP_BREAK_SYSTEM_PACKAGES=1

RUN apk add --update git bash openssh curl gnupg docker \
    && npm i -g npm@0 \
    && rm -rf /var/cache/apk/*

RUN apk add --no-cache python3 py3-pip \
    && pip3 install -U pip \
    && pip3 install --no-cache-dir awscli \
    && rm -rf /var/cache/apk/*

#Checkov
RUN if [[ "$INSTALL_CHECKOV" == "true" ]]; then \
    pip3 install -U setuptools \
    && apk add --allow-untrusted yq --repository=https://dl-cdn.alpinelinux.org/alpine/latest-stable/community >/dev/null 2>&1 \
    && pip3 install -U checkov \
    && rm -rf /var/cache/apk/*; fi
