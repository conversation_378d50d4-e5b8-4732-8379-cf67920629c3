#!/usr/bin/env groovy

/**
 * Definition of the all the pipeline stages to be executed for a Assure Platform  project.
 */

import org.pdxc.rest.GitApi
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.util.DefaultConfiguration
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.assure.util.WarningUtils
import org.assure.pushpipeline.PushPipelineUtils
import static groovy.json.JsonOutput.prettyPrint
import static groovy.json.JsonOutput.toJson

/**
 * Executor method to run the pipeline stages.
 * @param configData Map with the custom values to be used during the execution.
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'dotnet.Dockerfile') {
    String pipelineName = 'dotnet'
    //Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the artifact generated
    String artifactName
    // Calculated new version
    String newVersion
    // Name of the dockerFile
    String dockerName
    // Current repository name
    def currentRepoName
    // Current repository name
    def repoName
    // QA vars
    def sonarStatusCode = 0
    def checkovStatusCode = 0

    PushPipelineUtils pushUtils
    WarningUtils warningUtils

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 1, unit: 'HOURS')
        }
        stages {
            stage ('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        warningUtils = new WarningUtils()
                        dockerName = "${pipelineName}.Dockerfile"
                        pushUtils.setDockerAgentDotNet(dockerPath, dockerName)
                    }
                }
            }
            stage ('Artifact & Deploy') {
                agent {
                    dockerfile {
                        args '-u root:root'
                        filename "${pipelineName}.Dockerfile"
                        reuseNode true
                        registryCredentialsId 'assure-docker'
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('Pipeline info') {
                        steps {
                            script {
                                def conf = libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
                                writeFile file: "${pipelineName}-conf.yml", text: conf
                                def defaultConfigYml = readYaml file: "${pipelineName}-conf.yml"
                                def repoData = readYaml file: 'conf.yml'

                                configData = defaultConfigYml + repoData
                                configData = pushUtils.writeConfigData(configData, 'generic')
                                println 'Loaded configuration values: \n\n' + prettyPrint(toJson(configData))

                                writeYaml file: 'conf.yml', data: configData, overwrite: true

                                pushUtils.executePostStageFunction(stagesMap, 'info')
                            }
                        }
                    }
                    stage('Validate pipeline') {
                        steps {
                            script {
                                currentRepoName = new GitApi().getCurrentRepositoryName()
                                if (currentRepoName != configData.repositoryName) {
                                    error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                                }
                                echo "Configured repository name matches current repository: ${currentRepoName}"

                                pushUtils.executePostStageFunction(stagesMap, 'validate')
                            }
                        }
                    }
                    stage('Set up') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'setup') } }
                        steps {
                            script {
                                // Configure Git global data
                                def gitHubCredential = ValuesUtils.getVariable(configData, 'gitHubCredential', 'setup')
                                def mail = ValuesUtils.getVariable(configData, 'gitEmail', 'setup')
                                def user = ValuesUtils.getVariable(configData, 'gitUsername', 'setup')
                                def url = ValuesUtils.getVariable(configData, 'gitHubUrl', 'setup')
                                functiongroup_git.setup(gitHubCredential, mail, user, url)

                                // Calculate and set new version to be built
                                echo 'Calculate and set new version:'
                                def currentVersion = ValuesUtils.getVariable(configData, 'version', 'setup')
                                def path = ValuesUtils.getVariable(configData, 'csProjectPath', 'setup')

                                newVersion = currentVersion.substring(0, currentVersion.lastIndexOf('.')) + ".${env.BUILD_NUMBER}"
                                sh "sed -i 's#<AssemblyVersion>${currentVersion}</AssemblyVersion>#<AssemblyVersion>${newVersion}</AssemblyVersion>#' ${path}"

                                echo "Current Version: ${currentVersion} --- New Version: ${newVersion}"
                                configData.put('newVersion', "${newVersion}")
                                writeYaml file: 'conf.yml', data: configData, overwrite: true
                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Test') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'test') } }
                        steps {
                            script {
                                echo 'Test'
                                pushUtils.executePostStageFunction(stagesMap, 'test')
                            }
                        }
                    }
                    stage('Restore') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'restore') } }
                        steps {
                            script {
                                def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'restore')
                                def commandName = ValuesUtils.getVariable(configData, 'commandName', 'restore')
                                def commandParams = ValuesUtils.getVariable(configData, 'commandParams', 'restore')
                                def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'restore')
                                if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL

                                pushUtils.restore(cred, commandName, commandParams, artifactoryURL)

                                pushUtils.executePostStageFunction(stagesMap, 'restore')
                            }
                        }
                    }
                    stage('Build') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'build') } }
                        steps {
                            script {
                                def commandName = ValuesUtils.getVariable(configData, 'commandName', 'build')
                                def commandParams = ValuesUtils.getVariable(configData, 'commandParams', 'build')
                                pushUtils.executeCommand(commandName, commandParams)

                                pushUtils.executePostStageFunction(stagesMap, 'build')
                            }
                        }
                    }
                    stage('Pack') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'pack') } }
                        steps {
                            script {
                                def commandName = ValuesUtils.getVariable(configData, 'commandName', 'pack')
                                def commandParams = ValuesUtils.getVariable(configData, 'commandParams', 'pack')

                                pushUtils.executeCommand(commandName, commandParams)
                                pushUtils.executePostStageFunction(stagesMap, 'pack')
                            }
                        }
                    }
                    stage('Zipping artifact') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'zip') } }
                        steps {
                            script {
                                def zipScript = ValuesUtils.getVariable(configData, 'zipScript', 'zip')
                                def zipInclude = ValuesUtils.getVariable(configData, 'zipInclude', 'zip')
                                if (zipInclude == null) zipInclude = ''
                                def sourceFolder = ValuesUtils.getVariable(configData, 'zipSourceFolder', 'zip')
                                artifactName = ValuesUtils.getVariable(configData, 'targetZipName', 'zip') + ".${newVersion}"
                                //Delete if file already exist
                                sh script: "rm -rf ${artifactName}.zip", label: 'Delete old version of zip file'
                                if (zipScript != "" && zipScript != null) {
                                    sh script: "${zipScript} ${artifactName}.zip ${sourceFolder} ${zipInclude}", label: 'Zip file using custom script'
                                } else {
                                    zip glob: "${zipInclude}", zipFile: "${artifactName}.zip", dir: "${sourceFolder}"
                                }
                                pushUtils.executePostStageFunction(stagesMap, 'zip')
                            }
                        }
                    }                    
                    stage('Code quality') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'codequality') } }
                        parallel{
                            stage('SonarQube scan') {
                                when { expression { pushUtils.notSkipStage(stagesMap, 'sonar') } }
                                    steps {
                                        script {
                                            if (configData.dotnetVersion != 6) { //! Remove after Adapt SonarQube to dotnet 6
                                                repoName = configData.repositoryName
                                                def sonarExclusions= ValuesUtils.getVariable(configData, 'sonarExclusions', 'sonar')
                                                def csProjectTestPath = ValuesUtils.getVariable(configData, 'csProjectTestPath', 'sonar')
                                                def testPath = csProjectTestPath.substring(0, csProjectTestPath.lastIndexOf ('/'))
                                                // context.echo "-- ❌ -- Sonar disabled due to Network connectivity has been interrupted to Plano D1 resources."
                                                catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                    withCredentials([string(credentialsId:'ASSURE-SONAR-HOST', variable:'SONARHOST')]) {
                                                        withCredentials([string(credentialsId:'ASSURE-SONAR-TOKEN', variable:'SONARTOKEN')]) {
                                                            ///d:.sonar.projectDescription="${sonarDescription}"
                                                            def sonarDescription = "assure"

                                                            sonarStatusCode = sh script: """
                                                                apt-get update && apt-get install -y default-jdk
                                                                PATH="$PATH:/root/.dotnet/tools"
                                                                dotnet tool install --global dotnet-sonarscanner
                                                                cd src
                                                                dotnet sonarscanner begin /k:"${repoName}" /d:sonar.host.url=$SONARHOST /d:sonar.login=$SONARTOKEN /d:sonar.language="cs" /d:sonar.exclusions="${sonarExclusions}, ${dockerName}" /d:sonar.cs.opencover.reportsPaths="lcov.opencover.xml"
                                                                dotnet add ${testPath} package coverlet.msbuild --version 2.9.0
                                                                dotnet test ${csProjectTestPath} /p:CollectCoverage=true /p:IncludeTestAssembly=true /p:CoverletOutputFormat=\\"opencover,lcov\\" /p:CoverletOutput=../lcov
                                                                dotnet sonarscanner end /d:sonar.login=$SONARTOKEN
                                                            """,
                                                            label: 'Sonar-scanner analysis', 
                                                            returnStatus: true

                                                            def currentName = repoName
                                                            currentName = currentName.replaceAll('\\.', '')

                                                            def sonarQualityGate = ValuesUtils.getVariable(configData, 'sonarQualityGate', 'sonar')

                                                            def encodedToken = sh (
                                                                script: "echo -n '${SONARTOKEN}:' | base64", 
                                                                returnStdout: true).trim()

                                                            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                                pushUtils.composeSonarReport(sonarHost, currentName, encodedToken)
                                                            }

                                                            if(sonarStatusCode != 0 && sonarQualityGate){
                                                                currentBuild.result = 'ABORTED'
                                                                error ("-- ❌ -- Pipeline ABORTED ❗❗ SonarQube Quality Gate Status: FAILED (sonarQualityGate is ${sonarQualityGate})")
                                                                return
                                                            }
                                                            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                                if(sonarStatusCode != 0){
                                                                    error("-- ⚠️ -- SonarQube Scan 🛸 quality gate status FAILED ❗❗ Please check 👀 Log for details")
                                                                    return
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                pushUtils.executePostStageFunction(stagesMap, 'codequality')
                                            } else { //! Remove after Adapt SonarQube to dotnet 6
                                                catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                    error(" -- ⚠️ -- SonarQube Scan Skipped 🛸 SonarQube won't run using Dotnet 6.")
                                                    return
                                                }
                                            }
                                        }                                     
                                    }
                            }
                            stage('Checkov Scan') {
                                    when { expression { pushUtils.notSkipStage(stagesMap, 'checkov') } }
                                    steps {
                                        script {
                                            def checkovQualityGate = ValuesUtils.getVariable(configData, 'checkovQualityGate', 'checkov')
                                            def (checkovData, uuidCheckov) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.checkov_conf_file, "checkov/checkovConfiguration.yml.gpg")
                                            def scriptPath = "checkov/checkov.sh"

                                            pushUtils.checkovScan('generic', currentRepoName, currentRepoName, scriptPath, uuidCheckov, dockerName)

                                            def checkName = 'Vulnerabilities'
                                            withChecks("${checkName}") {
                                                junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: "**/results*.xml")
                                            }
                                            pushUtils.markGitHubCheckNeutral(configData, checkName)

                                            checkovStatusCode = pushUtils.checkovErrorCheck(currentRepoName, uuidCheckov)
                                            
                                            if(checkovStatusCode != 0 && checkovQualityGate){
                                                currentBuild.result = 'ABORTED'
                                                error ("-- ❌ -- Pipeline ABORTED ❗❗ Checkov Scan 🛸 status: FAIL (checkovQualityGate is ${checkovQualityGate})")
                                                return
                                            }
                                            
                                            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                if(checkovStatusCode != 0){
                                                    error("-- ⚠️ -- Checkov Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                                                    return
                                                }
                                            }
                                            pushUtils.executePostStageFunction(stagesMap, 'checkov')
                                        }
                                    }
                                }
                        }
                        post {
                            success {
                                script {
                                    pushUtils.executePostStageFunction(stagesMap, 'codequality')
                                }
                            }
                        }
                    }
                    stage('Upload artifact') {                        
                        when { expression { return ((pushUtils.notSkipStage(stagesMap, 'upload')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                        steps {
                            script {
                                def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
                                def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'upload')
                                if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                                def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'upload')
                                def localPath = ValuesUtils.getVariable(configData, 'artifactLocalPath', 'upload')
                                def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
                                functiongroup_artifactory.uploadGenericArtifact(cred, repo, artifactPath,
                                        artifactName + '.zip', localPath, artifactoryURL)

                                pushUtils.executePostStageFunction(stagesMap, 'upload')
                            }
                        }
                    }
                    stage('Update artifact properties') {                        
                        when { expression { return ((pushUtils.notSkipStage(stagesMap, 'update')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                        steps {
                            script {
                                def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'update')
                                if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                                def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'update')
                                ArtifactoryApi artfApi = new ArtifactoryApi(artifactoryURL, cred)

                                def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'update')
                                def props = ValuesUtils.getVariableArrayList(configData, 'artifactProperties', 'update')
                                def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'update')
                                
                                // Add 'qa' property based on vars
                                def notSkipQa     = pushUtils.notSkipStage(stagesMap, 'codequality')
                                def notSkipCheckov  = pushUtils.notSkipStage(stagesMap, 'checkov')
                                def notSkipSonar  = pushUtils.notSkipStage(stagesMap, 'sonar')

                                if(!notSkipQa || !notSkipCheckov  || !notSkipSonar){
                                    configData.artifactProperties.add([prop: 'qa', value: 'skip'])  
                                }
                                else if (checkovStatusCode  != 0 || sonarStatusCode != 0 ){
                                        configData.artifactProperties.add([prop: 'qa', value: 'fail'])
                                }
                                else 
                                        configData.artifactProperties.add([prop: 'qa', value: 'pass'])

                                artfApi.updateArtifactProperties(repo, artifactPath, artifactName + '.zip', props)

                                pushUtils.executePostStageFunction(stagesMap, 'update')
                            }
                        }
                    }
                    stage('Generate Release Notes') { 
                        // This stage generates release notes in github using the github api
                        when {
                            expression { return (pushUtils.notSkipStage(stagesMap, 'releaseNotes') && (BRANCH_NAME == 'master') && (configData.releaseNotes == true)) }
                        }
                        steps {
                            script {
                                    pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)

                                    pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                            }
                        }
                    }
                    stage('Branch protection') {
                        /* This stage updates the branch protection rule for BRANCH releases */
                        when{
                            expression { return ((pushUtils.notSkipStage(stagesMap, 'protection')) && (BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/)) }
                        }
                        steps {
                            script {
                                def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                                pushUtils.branchProtect(currentRepoName, gitHubCred)
                            }
                        }
                    }
                }
            }
        }
        post {
            always {
                script {
                    postActions(configData)
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}