package org.assure.envservice

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.rest.RestApi
// isAvailable
import org.pdxc.util.ValuesUtils
import org.pdxc.util.Utils

import org.assure.util.AssureUtils

import groovy.json.JsonOutput

import java.text.SimpleDateFormat


/**
* Class for Environment Service API.
*/
class EnvServiceApi extends RestApi {

    RestApi api
    /* groovylint-disable-next-line FieldTypeRequired, NoDef */
    def context
    def token = null    
    def apiKey = null
    def tokenUrl = null

    EnvServiceApi(String url, String credential, String tokenUrl, String apiKey) {
        super(url, credential)
        context = JenkinsContext.getContext()        
        this.tokenUrl = tokenUrl
        this.apiKey = apiKey        
    }

    void setToken() {
        this.token = getToken()
    }

    /**
     * Invoke authorizer to get token to be used with Environment Service.
     * @return Valid token.
     */      
    String getToken() {        
        return post(tokenUrl, null, 'POST', [], [200], 'APPLICATION_FORM', 'NOT_SET').access_token
    }

    /**
     * Compose the headers required by the Environment Service API.
     * @return List with the headers.
     */
    private List getHeader() {
        return [[name: 'x-api-key', value: "${apiKey}", maskValue: true], [name: 'Authorization', value: "Bearer ${token}", maskValue: true]]
    }

    /**
     * Wrapper for HTTP GET operations that will retry after getting a new token in case that the first attempt
     * returns an Unauthorized response. This is used to avoid the implementation of this retry on every single method.
     * @param url Endpoint of Environment Service to be invoked.
     * @return Operation response.
     */
    Map getWithCustomAuth(String url) {
        def response = get(url, [200, 401], getHeader())
        if (response?.message == 'Unauthorized' || response?.messages?.message?.contains('Unauthorized')) {
            token = getToken()
            response = get(url, [200], getHeader())
        }

        return response
    }

    Map getWithCustomAuthNotFound(String url) {
        def response = get(url, [200, 404], getHeader())
        if (response?.message == 'Unauthorized' || response?.messages?.message?.contains('Unauthorized')) {
            token = getToken()
            response = get(url, [200, 404], getHeader())
        }

        return response
    }

    /**
     * Wrapper for HTTP POST operations that will retry after getting a new token in case that the first attempt
     * returns an Unauthorized response. This is used to avoid the implementation of this retry on every single method.
     * @param url Endpoint of Environment Service to be invoked.
     * @return Operation response.
     */
    Map postWithCustomAuth(String url, String details, String method, String contentType = 'APPLICATION_JSON') {
        def response = post(url, details, method, getHeader(), [200, 202, 204, 401], contentType)
        if (response?.message == 'Unauthorized' || response?.messages?.message?.contains('Unauthorized')) {
            token = getToken()
            response = post(url, details, method, getHeader(), [200, 202, 204], contentType)
        }
        return response
    }

    /**
     * Check whether environment is currently available for a new deployment. It checks whether any of the blocking
     * flags is set to True.
     * @param environment Target environment to be checked.
     * @param data Configuration data.
     * @param response Contains environment data is already available
     * @return True when environment is not blocked for deployment.
     */
    // MOVE TO PULL PIPELINES UTILS???
    boolean isAvailable(String environment, LinkedHashMap data, def response) {        

        def resourceName = ValuesUtils.getVariable(data, 'environment_resource_name', environment)

        def available = null 
        available = (response.is_locked == false)
        // response._links?.item.findAll { env ->
        //                                 if (env.summary.resource_name == resourceName) {
        //                                     available = (env.summary.is_locked == false)// && env.summary.is_busy == false)
        //                                 }
        //                            }
        if (available == null) {
            Utils.throwExceptionInstance('Exception', "environment not found")
        }
        return available       
    }

    /**
     * Update the Environment resource in the given path by setting the "is_busy" flag to the given value.
     * @param environmentPath Full path to environment, excluding the base URL.
     * @param busy Value to be set to "is_busy" (true / false).
     * @return Response from the API.
     */
    Map setEnvironmentStatus(String environmentPath, boolean busy) {
        return postWithCustomAuth(apiURL + '/' + environmentPath, "{\"is_busy\":${busy}}", 'PATCH')
    }

    /**
     * Utility method to compose the whole URL path with customer and account values. Also, if flag is True, also
     * appends the path to a specific environment.
     * @param data Loaded configuration data from where customer and account values have to be retrieved.
     * @param prefix Prefix of the step if applicable. It is expected to be Environment key used if this is different.
     * @param environment Flag to include or not the environment as part of the path.
     * @return Path to the required element.
     */
    private String composeURLPath(LinkedHashMap data, String prefix, boolean environment) {
        def url = 'customers/' + ValuesUtils.getVariable(data, 'customerName', prefix) + '/accounts/' + ValuesUtils.getVariable(data, 'accountName', prefix)
        return ((environment) ? url + '/environments/' + ValuesUtils.getVariable(data, 'environment_resource_name', prefix) : url)
    }

    /**
     * Invoke the deploy operation of Environment Service for a deployment package.
     * @param environment Target environment.
     * @param deploymentPackage Deployment Package to be deployed.
     * @param artifacts List of artifacts of the deployment package that we need to deploy.
     * @param data Configuration data.
     * @return Operation result.
     */
    Map deploy(String environment, String deploymentPackage, List<Map> artifacts, LinkedHashMap data) {        
        def sleepTime = ValuesUtils.getVariable(data, 'sleepTime')
        def maxTime = ValuesUtils.getVariable(data, 'maxTime')
        def attemptCount = ValuesUtils.getVariable(data, 'attemptCount')
        
        def lastDeployResp = getLastDeployData(environment, deploymentPackage, data)
        def envDataResp = getEnvironmentData(environment, deploymentPackage, data)
        def body = composeDeployBody(environment, lastDeployResp, envDataResp, deploymentPackage, artifacts, data)
        
        def autoUpdateTF = ValuesUtils.getVariable(data, 'auto_update_tf_version')
        if (autoUpdateTF == true) {
            def planResult = planDeploymentPackage(deploymentPackage, environment, body, data, sleepTime, maxTime, attemptCount)
            if (planResult.success == false) return planResult
        }

        context.echo "-- ⭐ -- BODY: ${body}"

        return refreshDeploymentPackage(deploymentPackage, environment, body, data, sleepTime, maxTime, attemptCount)
    }

    Map getLastDeployData(String environment, String deploymentPackage, LinkedHashMap data) {
        def customer = ValuesUtils.getVariable(data, 'customerName', environment)
        def account = ValuesUtils.getVariable(data, 'accountName', environment)
        def env = ValuesUtils.getVariable(data, 'environment_resource_name', environment)
        String urlLastDeploy = "deployment-packages/${deploymentPackage}/deployments/${deploymentPackage}-${env}"
        
        def path = composeURLPath(data, environment, true)

        return getWithCustomAuth(data.environment_service_url + '/' + path + '/' + urlLastDeploy)        
    }

    Map getEnvironmentData(String environment, String deploymentPackage, LinkedHashMap data) {
        def customer = ValuesUtils.getVariable(data, 'customerName', environment)
        def account = ValuesUtils.getVariable(data, 'accountName', environment)
        def env = ValuesUtils.getVariable(data, 'environment_resource_name', environment)
        String urlEnvData = "deployment-packages/${deploymentPackage}"

        def path = composeURLPath(data, environment, true)

        return getWithCustomAuth(data.environment_service_url + '/' + path + '/' + urlEnvData)              
    }

    String composeDeployBody(String environment, Map lastDeployResp, Map envDataResp, String deploymentPackage, List<Map> artifacts, LinkedHashMap data) {
        def customer = ValuesUtils.getVariable(data, 'customerName', environment)
        def account = ValuesUtils.getVariable(data, 'accountName', environment)
        def env = ValuesUtils.getVariable(data, 'environment_resource_name', environment)
        
        Map previousDeployInfo = lastDeployResp.deploy_info
        def previousArtifactsInfo = (lastDeployResp.artifacts != null) ? lastDeployResp.artifacts : []

        def deployInfo = [:]
        def newArtifactList = []

        artifacts.each { artifact ->
            if (artifact.type == 'tf') {                
                deployInfo = composeDeployInfoParams(artifact)                
            }
            else {
                boolean hasBundleBranch = false
                def uniqueName = getUniqueName(artifact)
                
                artifact.put("artefacts", envDataResp.artefacts)

                if (lastDeployResp.artifacts != null && lastDeployResp.artifacts.size() > 0) {                                       
                    lastDeployResp.artifacts.each { art ->                       
                        if (art.uniquename == uniqueName) {                                                     
                            hasBundleBranch = art.tf_properties.any{ it.key.contains('bundle_branch') || it.key.contains('image_branch')}                           
                        }
                        if (art.uniquename == null) {                            
                            if (getUniqueName(art) == uniqueName) {                                                      
                                hasBundleBranch = art.tf_properties.any{ it.key.contains('bundle_branch') || it.key.contains('image_branch')}                            
                            }
                        }
                    }                    
                }
                else {
                    envDataResp.services.each { item ->
                        item.artefacts.each { art ->                                                        
                            if (art.name == uniqueName) {
                                hasBundleBranch = art.tf_properties.any{ it.key.contains('bundle_branch') || it.key.contains('image_branch') }                                 
                            }
                        }
                    }
                }

                newArtifactList.add(composeArtifactParams(artifact,hasBundleBranch))                
            }
        }

                       
        def previousArtifactsFiltered = previousArtifactsInfo.findAll {art ->             
            def uniqueName = getUniqueName(art)            
            return !newArtifactList.any {artifact -> artifact.uniquename == uniqueName}
        }       

        newArtifactList = newArtifactList + previousArtifactsFiltered

        def artifactsMap = [:]
        artifactsMap.put("artifacts", newArtifactList)
        def preparedArtifactJson = JsonOutput.toJson(artifactsMap)        
        preparedArtifactJson = ValuesUtils.removeStartEndChars(preparedArtifactJson, "{", true, false)
        preparedArtifactJson = ValuesUtils.removeStartEndChars(preparedArtifactJson, "}", false, true)        

        if (deployInfo == [:] && previousDeployInfo) {            
            def deplInfo = [:]
            if (previousDeployInfo.git_url) {
                deplInfo.git_url = previousDeployInfo.git_url
            }            
            if (previousDeployInfo?.tag && previousDeployInfo.tag != null && previousDeployInfo.tag != "null") {                
                deplInfo.tag = previousDeployInfo.tag                
            }
            deplInfo = deplInfo.findAll{ (it.value != "null" && it.value != null) }
            deployInfo = deplInfo            
        }   
        
        def dependsOnData = envDataResp.dependsOn
        dependsOnData = (dependsOnData != null) ? dependsOnData : []

        def dependsOnDataMap = [:]
        dependsOnDataMap.put("dependsOn", dependsOnData)
        def preparedDependsOnData = JsonOutput.toJson(dependsOnDataMap)
        preparedDependsOnData = ValuesUtils.removeStartEndChars(preparedDependsOnData, "{", true, false)
        preparedDependsOnData = ValuesUtils.removeStartEndChars(preparedDependsOnData, "}", false, true)
        
        def ssmConditions = envDataResp.ssm_conditions
        ssmConditions = (ssmConditions != null) ? ssmConditions : []

        def ssmConditionsMap = [:]
        ssmConditionsMap.put("ssm_conditions", ssmConditions)
        def preparedssmConditionsJson = JsonOutput.toJson(ssmConditionsMap)
        preparedssmConditionsJson = ValuesUtils.removeStartEndChars(preparedssmConditionsJson, "{", true, false)
        preparedssmConditionsJson = ValuesUtils.removeStartEndChars(preparedssmConditionsJson, "}", false, true)
        
        def tag = ""
        if (deployInfo.tag != "null" && deployInfo.tag != null && deployInfo?.tag) tag = ", \"tag\": \"${deployInfo.tag}\""
        
        def body = """{
                "customer": \"${customer}\", 
                "account": \"${account}\",
                "environment": \"${env}\",
                "username": \"assure-pipeline\",
                "terraform_action": "apply",
                "deploymentPackages": [{
                    "name": \"${deploymentPackage}\",
                    "deploy_info": {
                        "git_url": \"${deployInfo.git_url}\"
                        ${tag}                       
                    },
                    ${preparedArtifactJson},
                    ${preparedDependsOnData},
                    ${preparedssmConditionsJson},
                    "version": \"\",                                     
                    "skip_on_destroy": ${(envDataResp.skip_on_destroy == true ) ? true : false},
                    "parameters": ${JsonOutput.toJson(lastDeployResp.parameters)} 
                }]
            }"""            

        return body
    }

    /**
     * Compose the TF parameters for Deploy Units.
     * @param artifact Artifact data.
     * @return Parameters to be sent.
     */
    Map composeDeployInfoParams(Map artifact) {
        // TEMP!!!!!!!!!!!!!!!!!!!
        def tag = artifact.tag.replace('+', '%2B')

        def param = [:]
        param.put('git_url', artifact.deploy_git_url)
        param.put('tag', tag)
        
        return param
    }

    /**
     * Compose the parameters that are passed through to TF scripts.
     * @param artifact Artifact to be deployed.
     * @return Parameters to be sent.
     */
    Map composeArtifactParams(Map artifact, boolean hasBundleBranch) {        
        def param = [:]
        param.put('uniquename', getUniqueName(artifact))
        // REQUIRED FOR TESTING
        param.put('name', artifact.name)
        param.put('git_url', artifact.git_url)
        param.put('commit', artifact.commit)
        param.put('servicename', artifact.service_name)

        def variableValue
        artifact.artefacts.find { property -> 
                                    if (artifact.uniquename == property.name) {
                                        variableValue = property.variable
                                    }
        }  


        def tfProperties = [:]

        if (artifact.type == 'docker') {
            tfProperties.put('image_path_' + variableValue, '/' + artifact.repo + '/' + artifact.pathnobranch)
            def branchImageKey = (hasBundleBranch == true) ? "image_branch_${variableValue}" : 'artifact_branch_name'
            tfProperties.put("${branchImageKey}", artifact.branch)
            def singleName = artifact.uniquename.substring(artifact.uniquename.lastIndexOf("/")+1, artifact.uniquename.size())            
            tfProperties.put('image_name_' + variableValue, singleName)
            tfProperties.put('image_tag_' + variableValue, artifact.version)
        } else {
            tfProperties.put('bundle_version_' + variableValue, artifact.version)
            tfProperties.put('bundle_path_' + variableValue, '/' + artifact.repo + '/' + artifact.pathnobranch)
            def branchKey = (hasBundleBranch == true) ? "bundle_branch_${variableValue}" : 'artifact_branch_name'            
            tfProperties.put("${branchKey}", artifact.branch)
            tfProperties.put('bundle_name_' + variableValue, artifact.uniquename)
            tfProperties.put('bundle_type_' + variableValue, artifact.extension)
        }
        
        param.put('tf_properties', tfProperties)
        return param
    }

    String getUniqueName(def artifact) {
        String uniqueName = artifact.uniquename
        
        try {
            if (uniqueName == null) {
                def regexUN = '(bundle_name_[a-zA-Z0-9-_]*)'
                def artifactUniqueName = AssureUtils.getFromRegEx(artifact.tf_properties,regexUN)                
                uniqueName = artifact.tf_properties."${artifactUniqueName}"                          
                if (artifactUniqueName == null) {                    
                    regexUN = '(image_name_[a-zA-Z-_]*)'
                    def nameVariable = AssureUtils.getFromRegEx(artifact.tf_properties,regexUN) 
                    regexUN = '(image_path_[a-zA-Z-_]*)'
                    def pathVariable = AssureUtils.getFromRegEx(artifact.tf_properties,regexUN)
                    def path = artifact.tf_properties."${pathVariable}" 
                    path = path.replace('/feature', '')                    
                    uniqueName = path + '/' + artifact.tf_properties."${nameVariable}"                   
                }
            }
        } catch (Exception e) {
            context.echo "-- ❌ -- Unable to extract uniquename"            
        }

        return uniqueName
    }

    Map planDeploymentPackage(String deploymentPackage, String environment, def body, LinkedHashMap data, int sleepTime, int maxTime, int attemptCount) {
        
        context.echo "-- 🚀 -- Plan deployment package: ${deploymentPackage}"
        body = body.replace('"terraform_action": "apply",', '"terraform_action": "plan",')
        context.echo "-- ⭐ -- PLAN BODY: ${body}"

        def deployResult = [:]

        def urlRefresh = composeURLPath(data, environment, true)
        
        def path = '/deployment-packages/' + deploymentPackage + ':refresh'        
 
        def response = postWithCustomAuth(data.environment_service_url + '/' + urlRefresh + path, body, 'POST', 'APPLICATION_JSON')

        def statusURL = response.status
        deployResult.tf_log = response.tf_log_url                
        deployResult.location = ValuesUtils.removeStartEndChars(statusURL, '/status', false, true)
        def responseStatus
        def lastLog = ''
        deployResult.log_url = response.tf_log_url

        context.timeout(time: maxTime, unit: 'MINUTES') {
            def counter = 0
            while (lastLog.contains('###END_STAGE_OK') == false && lastLog.contains('###END_STAGE_KO') == false && counter < attemptCount ) {
                context.sh script: "sleep ${sleepTime}", label: "-- ⏳ -- Deployment in progress... Installation NOT completed"
                responseStatus = getWithCustomAuth(deployResult.tf_log)
                lastLog = responseStatus.response                
                counter++
            }
        }

        if (lastLog.contains('###END_STAGE_OK') == true) {
            context.echo "-- 🎉 -- Plan of deployment package ${deploymentPackage} completed"
            deployResult.success = true
        } else {
            context.echo "-- 💥 -- Plan of deployment package ${deploymentPackage} failed"
            deployResult.success = false
        }
        def sdf = new SimpleDateFormat('HH-mm-ss.SS').format(new Date())
        context.writeFile file: "deploylogs/log-${environment}-${deploymentPackage}-${sdf}-deploy.log", text: "${lastLog}"
        context.sh script: "cat deploylogs/log-${environment}-${deploymentPackage}-${sdf}-deploy.log", label: 'Created log'
        
        return deployResult        
    }

    Map refreshDeploymentPackage(String deploymentPackage, String environment, def body, LinkedHashMap data, int sleepTime, int maxTime, int attemptCount) {

        // context.error "-- ❌ -- Pipeline disabled"
        
        context.echo "-- 🚀 -- Deploy deployment package: ${deploymentPackage}"
        def deployResult = [:]

        def urlRefresh = composeURLPath(data, environment, true)
        
        def path = '/deployment-packages/' + deploymentPackage + ':refresh'        
 
        def response = postWithCustomAuth(data.environment_service_url + '/' + urlRefresh + path, body, 'POST', 'APPLICATION_JSON')

        def statusURL = response.status
        // TEMPORAL FIX
        statusURL = statusURL.replace('environment-service/environment-service', 'environment-service')
        deployResult.location = ValuesUtils.removeStartEndChars(statusURL, '/status', false, true)
        def responseStatus
        def deployStatus = 'deployment-in-progress'
        def lastLog = ''
        deployResult.log_url = response.tf_log_url

        context.timeout(time: maxTime, unit: 'MINUTES') {
            def counter = 0
            while (deployStatus != 'installed' && deployStatus != 'installation-failed' && deployStatus != 'no-deployment-log' && counter < attemptCount ) {
                context.sh script: "sleep ${sleepTime}", label: "-- ⏳ -- Deployment in progress... Installation NOT completed"
                responseStatus = getWithCustomAuth(statusURL)
                lastLog = responseStatus.content
                deployStatus = responseStatus.status
                counter++
            }
        }

        if (deployStatus == 'installed') {
            context.echo "-- 🎉 -- Deployment of deployment package ${deploymentPackage} completed: Installed"
            deployResult.success = true
        } else {
            context.echo "-- 💥 -- Deployment of deployment package ${deploymentPackage} failed: ${deployStatus}"
            deployResult.success = false
        }
        def sdf = new SimpleDateFormat('HH-mm-ss.SS').format(new Date())
        context.writeFile file: "deploylogs/log-${environment}-${deploymentPackage}-${sdf}-deploy.log", text: "${lastLog}"
        context.sh script: "cat deploylogs/log-${environment}-${deploymentPackage}-${sdf}-deploy.log", label: 'Created log'

        return deployResult        
    }
    
    // OPS 
    Map refreshService(String deploymentPackage, String environment, def body, LinkedHashMap data, int sleepTime, int maxTime, int attemptCount) {

        // context.error "-- ❌ -- Pipeline disabled"
        
        context.echo "-- 🚀 -- Deploy deployment package: ${deploymentPackage}"
        def deployResult = [:]

        def path = '/services/' + deploymentPackage + ':refresh'        
 
        def response = postWithCustomAuth(data.environment_service_url + path, body, 'POST', 'APPLICATION_JSON')

        def statusURL = response.status
        deployResult.location = ValuesUtils.removeStartEndChars(statusURL, '/status', false, true)
        def responseStatus
        def deployStatus = 'deployment-in-progress'
        def lastLog = ''

        context.timeout(time: maxTime, unit: 'MINUTES') {
            def counter = 0
            while (deployStatus != 'installed' && deployStatus != 'installation-failed' && deployStatus != 'no-deployment-log' && counter < attemptCount ) {
                context.sh script: "sleep ${sleepTime}", label: "-- ⏳ -- Deployment in progress... Installation NOT completed"
                responseStatus = getWithCustomAuth(statusURL)
                lastLog = responseStatus.content
                deployStatus = responseStatus.status
                counter++
            }
        }

        if (deployStatus == 'installed') {
            context.echo "-- 🎉 -- Deployment of deployment package ${deploymentPackage} completed: Installed"
            deployResult.success = true
        } else {
            context.echo "-- 💥 -- Deployment of deployment package ${deploymentPackage} failed: ${deployStatus}"
            deployResult.success = false
        }
        def sdf = new SimpleDateFormat('HH-mm-ss.SS').format(new Date())
        context.writeFile file: "deploylogs/log-${environment}-${deploymentPackage}-${sdf}-deploy.log", text: "${lastLog}"
        context.sh script: "cat deploylogs/log-${environment}-${deploymentPackage}-${sdf}-deploy.log", label: 'Created log'

        return deployResult        
    }

    List getDeployedArtefactsData(String url, String environment, String deploymentPackage, LinkedHashMap data) {
        String path = composeURLPath(data, environment, true) +
                "/deployments/${deploymentPackage}-${ValuesUtils.getVariable(data, 'environment_resource_name', environment)}"
        List artifacts = []
        def response = getAcceptingNotFound(url + "/" + path)
        if ( "Not found" != response.message) {
            artifacts = (response.artifacts) ? response.artifacts : []
        }
        return artifacts
    }

    Map getAcceptingNotFound(String url) {
        def response = get(url, [200, 401, 404], getHeader())
        if (response?.message == 'Unauthorized' || response?.messages?.message?.contains('Unauthorized')) {
            token = getToken()
            response = get(url, [200], getHeader())
        }

        return response
    }

    Map getDeploymentPackageDependencies(String url, String deploymentPackageName) {        
        def response = getWithCustomAuth(url + '/deployment-packages/' + deploymentPackageName)
        def dependsOn = response?.dependsOn
        def dependencies = []
        if (dependsOn) {
            dependsOn.each { deploymentPackage ->            
                dependencies.add(getWithCustomAuth(url + '/deployment-packages/' + deploymentPackage.service_name))
            }
        }
        return dependencies
    }

    /**
     * Update the testing status for a deployment.
     * @param url Url of the deployment resource.
     * @param testResult Result of the test to be set.
     * @return Operation result.
     */
    Map setTestResults(String url, String testResult) {
        return postWithCustomAuth(url, "{\"testing\":\"${testResult}\"}", 'PATCH')
    }

    def rollBack(String serviceName, LinkedHashMap configuration, String environment) {

        def customer = ValuesUtils.getVariable(configuration, 'customerName', environment)
        def account = ValuesUtils.getVariable(configuration, 'accountName', environment)
        def env = ValuesUtils.getVariable(configuration, 'environment_resource_name', environment)

        def sleepTime = ValuesUtils.getVariable(configuration, 'sleepTime')
        def maxTime = ValuesUtils.getVariable(configuration, 'maxTime')
        def attemptCount = ValuesUtils.getVariable(configuration, 'attemptCount')
               
        // String urlLastDeploy = "deployment-packages/${serviceName}/deployments/${serviceName}-${env}?trail=true"
        // def path = composeURLPath(configuration, environment, true)
        // def previousDeployments = getWithCustomAuth(configuration.environment_service_url + '/' + path + '/' + urlLastDeploy)
        // def lastDeployInfo = previousDeployments.trail[1]

        String urlLastDeploy = "/customers/${customer}/accounts/${account}/environments/${env}/deployment-packages/${serviceName}/deployments"
        def deploymentsHistory = getWithCustomAuth(configuration.environment_service_url + urlLastDeploy)
        def previousArtifactsInfo = getWithCustomAuth(configuration.environment_service_url + urlLastDeploy + '/' + deploymentsHistory._links.item[1].summary.resource_name)
        def lastDeployInfo = previousArtifactsInfo               
        

        def dependsOnData = (lastDeployInfo.dependsOn != null) ? lastDeployInfo.dependsOn : [] 
        def dependsOnDataMap = [:]
        dependsOnDataMap.put("dependsOn", dependsOnData)
        def preparedDependsOnData = JsonOutput.toJson(dependsOnDataMap)
        preparedDependsOnData = ValuesUtils.removeStartEndChars(preparedDependsOnData, "{", true, false)
        preparedDependsOnData = ValuesUtils.removeStartEndChars(preparedDependsOnData, "}", false, true)

        def ssmConditions = (lastDeployInfo.ssm_conditions != null) ? lastDeployInfo.ssm_conditions : []
        def ssmConditionsMap = [:]
        ssmConditionsMap.put("ssm_conditions", ssmConditions)
        def preparedssmConditionsJson = JsonOutput.toJson(ssmConditionsMap)
        preparedssmConditionsJson = ValuesUtils.removeStartEndChars(preparedssmConditionsJson, "{", true, false)
        preparedssmConditionsJson = ValuesUtils.removeStartEndChars(preparedssmConditionsJson, "}", false, true)

//         def params = []
//         if (lastDeployInfo.parameters != null) {            
//             lastDeployInfo.parameters.each { prop -> 
//                     def paramsMap = [:]
//                     paramsMap.put("${prop.key}", "${prop.value}")
//                     def preparedParams = JsonOutput.toJson(paramsMap)
//                     params.add(preparedParams)
//             }
//         }        

        def tag = ""
        if (lastDeployInfo.deploy_info.tag != "null" && lastDeployInfo.deploy_info.tag != null && lastDeployInfo.deploy_info.tag) tag = ", \"tag\": \"${lastDeployInfo.deploy_info.tag}\""

        def body = """{
                "customer": \"${customer}\", 
                "account": \"${account}\",
                "environment": \"${env}\",
                "username": \"assure-pipeline\",
                "terraform_action": "apply",
                "deploymentPackages": [{
                    "name": \"${serviceName}\",
                    "deploy_info": {
                        "git_url": \"${lastDeployInfo.deploy_info.git_url}\"
                        ${tag}                       
                    },
                    "artifacts": ${lastDeployInfo.artifacts.toString()},
                    ${preparedDependsOnData},
                    "version": \"\",
                    ${preparedssmConditionsJson},
                    "skip_on_destroy": ${lastDeployInfo.skip_on_destroy == true},
                    "parameters": ${JsonOutput.toJson(lastDeployInfo.parameters)}
                }]
            }"""

        context.echo "-- ⭐ -- BODY: ${body}"
        
        return refreshDeploymentPackage(serviceName, environment, body, configuration, sleepTime, maxTime, attemptCount)
    }
    
}