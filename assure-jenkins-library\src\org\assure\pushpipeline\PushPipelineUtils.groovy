package org.assure.pushpipeline

import org.pdxc.rest.GitApi
import org.pdxc.rest.RestApi
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.util.ValuesUtils
import org.pdxc.util.Utils
import org.pdxc.util.FileUtils
import org.pdxc.util.DefaultConfiguration
import com.cloudbees.groovy.cps.NonCPS
import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import org.pdxc.jenkins.JenkinsContext
import org.assure.pullpipeline.PullPipelineUtils

/**
 * Utility class used to define a set of functions that can be reused across the different Assure pipelines but that
 * are too specific to be moved to a Global Jenkins Shared Library.
 */

class PushPipelineUtils {

    private PushPipelineUtils() {}

    def context = JenkinsContext.getContext()

    /**
     * Combine the 2 potential inputs for a pipeline: stages coming from the caller and from Assure specifics.
     * @param map1 Functions coming from the caller.
     * @param map2 Functions defined in Assure Library.
     * @return Combined functions.
     */
    def mergeStagesMaps(LinkedHashMap map1, LinkedHashMap map2) {
        LinkedHashMap finalMap = [:]
        if (map1 != null && map2 != null) {
            map1.each { item ->
                if (map2.get(item.key) != null) {
                    def func1 = ((Map) item.value).get('func')
                    def func2 = ((Map) map2.get(item.key)).get('func')
                    def func = {
                        ((func2 != null) ? func2 : { })()
                        ((func1 != null) ? func1 : { })()
                    }
                    def skip2 = ((Map) map2.get(item.key)).get('skip')
                    def skip = (skip2 != null && skip2 == true) ? skip2 : ((Map) item.value).get('skip')
                    finalMap.put(item.key, ['skip': skip, 'func': func])
                } else {
                    finalMap.put(item.key, item.value)
                }
            }
            map2.each { item ->
                if (map1.get(item.key) == null) {
                    //Not added yet, include this stage (change order of assignments and override)
                    finalMap.put(item.key, item.value)
                }
            }
        } else {
            finalMap = (map1 == null) ? map2 : ((map2 == null) ? map1 : [:])
        }

        return finalMap
    }

    def writeConfigData(LinkedHashMap configData, String type) {
                
        def path = ValuesUtils.getVariableArrayList(configData, 'artifactPath')
        path += '/' + "${context.env.BRANCH_NAME}"

        def committer = context.sh (script: "git --no-pager show -s --format='%ae'", 
                                    label: "-- 👤 -- Getting committer", 
                                    returnStdout: true).trim()
        configData.artifactProperties.add([prop: 'committer', value: committer])
        configData.artifactProperties.add([prop: 'commit', value: "${context.GIT_COMMIT}"])
        configData.artifactProperties.add([prop: 'git_url', value: "${context.GIT_URL}".endsWith('.git') ? "${context.GIT_URL}".substring(0,"${context.GIT_URL}".lastIndexOf('.git')) : "${context.GIT_URL}"])

        def statusProp = false
        def typeProp = false

        configData.artifactProperties.findAll { property -> 
                                                if (property.prop == 'status') {
                                                    statusProp = true
                                                }
                                                if (property.prop == 'type') {
                                                    typeProp= true
                                                }
                                            }

        if (typeProp == false) configData.artifactProperties.add([prop: 'type', value: "${type}"])
        if (statusProp == false) configData.artifactProperties.add([prop: 'status', value: "ready"])

        configData.put('artifactPath', "${path}")

        return configData
    }

    @NonCPS
    def getArtefactData(def dataFile) {
        def strFindName = 'default="'

        def bundleNameJson = [:]
        def bodyJson = ''

        def imagePathMap = [:]
        def imageNameMap = [:]

        dataFile.eachLine { line ->
            if ((line ==~ /(variable+\s+"bundle_name_+.*)/) || (line ==~ /(variable+\s+"image_path_+.*)/) || (line ==~ /(variable+\s+"image_name_+.*)/)) {
                // Extract default value (it will be the artefact name identifier)
                def pos = dataFile.indexOf(line)
                def posFin = dataFile.indexOf('}', pos)
                def sub = (dataFile.substring(pos, posFin + 1)).replaceAll("\\s", '')
                def posSub = sub.lastIndexOf(strFindName)
                def posSubFin = sub.indexOf('"', posSub + strFindName.size())
                def bundleName = sub.substring(posSub + strFindName.size(), posSubFin)

                def strFindVariable
                if (line ==~ /(variable+\s+"bundle_name_+.*)/) {
                    strFindVariable = 'variable"bundle_name_'
                } else if (line ==~ /(variable+\s+"image_path_+.*)/) {
                    strFindVariable = 'variable"image_path_'
                } else {
                    strFindVariable = 'variable"image_name_'
                }

                // Extract variable details
                def variableName = line
                variableName = variableName.replaceAll("\\s", '')
                posSub = variableName.lastIndexOf(strFindVariable)
                posSubFin = variableName.indexOf('"', posSub + strFindVariable.size())
                variableName = variableName.substring(posSub + strFindVariable.size(), posSubFin)

                if (strFindVariable == 'variable"bundle_name_') {
                    bundleNameJson.put('name', bundleName)
                    bundleNameJson.put('variable', variableName)
                    bodyJson += JsonOutput.toJson(bundleNameJson) + ','
                } else if (strFindVariable == 'variable"image_path_') {
                    imagePathMap.put(variableName, bundleName)
                } else {
                    imageNameMap.put(variableName, bundleName)
                }
            }
        }

        if (imageNameMap.size() > 0) {
            imageNameMap.each { variable, name ->
                def path = imagePathMap.get(variable)
                if (path != null) {
                    bundleNameJson.put('name', path + '/' + name)
                    bundleNameJson.put('variable', variable)
                    bodyJson += JsonOutput.toJson(bundleNameJson) + ','
                }
            }
        }

        return (bodyJson == '') ? '' : bodyJson[0..-2]
    }

    @NonCPS
    def getAllArtefactData(def dataFile) {

        def bodyJson = ''
        def tfFilesText = dataFile.replaceAll("\\s", '')

        def lastIndex = 0
        def variablesBundles = tfFilesText.findAll(~/bundle_name_/) { match ->     
                    lastIndex = tfFilesText.indexOf(match, lastIndex+1)
                    def variablesBundlesSubStr = tfFilesText.substring(lastIndex + match.size(), tfFilesText.indexOf('"', lastIndex + match.size()))            
                    return variablesBundlesSubStr }
        
        lastIndex = 0        
        def variablesImageDocker = tfFilesText.findAll(~/"image_name_/) { match ->     
                    lastIndex = tfFilesText.indexOf(match, lastIndex+1)
                    def variablesImageDockerSubStr = tfFilesText.substring(lastIndex + match.size(), tfFilesText.indexOf('"', lastIndex + match.size()))            
                    return variablesImageDockerSubStr }
        
        // TODO Create a function
        if (variablesBundles.size() > 0) {
            def artData = ["bundle_name_", "bundle_path_", "bundle_version_", "bundle_type_", "bundle_branch_"]
            def strFindName = 'default="'

            variablesBundles.each { eachVar ->
                def textVariables = [:]
                def posIniName = tfFilesText.indexOf("bundle_name_${eachVar}")
                def posDefaultName = tfFilesText.indexOf("${strFindName}", posIniName)
                def nameValue = tfFilesText.substring(posDefaultName + strFindName.size(), tfFilesText.indexOf('"', posDefaultName + strFindName.size()))
                textVariables.put("name", nameValue)
                textVariables.put("variable", "${eachVar}")
                def tfProperties = [:]
                artData.each { type ->
                    def posIni = tfFilesText.indexOf("${type}${eachVar}")
                    if (posIni != -1) {                        
                        def posDefault = tfFilesText.indexOf("${strFindName}", posIni)                        
                        def bundleValue = tfFilesText.substring(posDefault + strFindName.size(), tfFilesText.indexOf('"', posDefault + strFindName.size()))
                        tfProperties.put("${type}${eachVar}", bundleValue)
                    }
                    if (posIni == -1 && type == "bundle_branch_") {
                        def posIniBranch = tfFilesText.indexOf("artifact_branch_name")
                        def posDefaultBranch = tfFilesText.indexOf("${strFindName}", posIniBranch)
                        def branchValue = tfFilesText.substring(posDefaultBranch + strFindName.size(), tfFilesText.indexOf('"', posDefaultBranch + strFindName.size()))
                        tfProperties.put("artifact_branch_name", branchValue)
                    }
                }
                bodyJson += JsonOutput.toJson(textVariables)[0..-2] + ', "tf_properties":' + JsonOutput.toJson(tfProperties) +'},'
            }            
        }
        
        if (variablesImageDocker.size() > 0) {
            def artImageDockerData = ["image_name_", "image_path_", "image_tag_", "image_branch_"]
            def strFindName = 'default="'

            variablesImageDocker.each { eachVar ->
                def textVariables = [:]
                def posIniName = tfFilesText.indexOf("\"image_name_${eachVar}")
                def posDefaultName = tfFilesText.indexOf("${strFindName}", posIniName)
                def nameValue = tfFilesText.substring(posDefaultName + strFindName.size(), tfFilesText.indexOf('"', posDefaultName + strFindName.size()))
                // textVariables.put("name", nameValue)
                textVariables.put("variable", "${eachVar}")
                def tfProperties = [:]
                artImageDockerData.each { type ->
                    def posIni = tfFilesText.indexOf("\"${type}${eachVar}")
                    if (posIni != -1) {                   
                        def posDefault = tfFilesText.indexOf("${strFindName}", posIni)                        
                        def bundleValue = tfFilesText.substring(posDefault + strFindName.size(), tfFilesText.indexOf('"', posDefault + strFindName.size()))
                        tfProperties.put("${type}${eachVar}", bundleValue)
                        if (type == "image_path_"){
                            textVariables.put("name", bundleValue + '/' + nameValue)
                        }
                    }
                    if (posIni == -1 && type == "image_branch_") {
                        def posIniBranch = tfFilesText.indexOf("\"artifact_branch_name")
                        def posDefaultBranch = tfFilesText.indexOf("${strFindName}", posIniBranch)
                        def branchValue = tfFilesText.substring(posDefaultBranch + strFindName.size(), tfFilesText.indexOf('"', posDefaultBranch + strFindName.size()))
                        tfProperties.put("artifact_branch_name", branchValue)
                    }
                }
                bodyJson += JsonOutput.toJson(textVariables)[0..-2] + ', "tf_properties":' + JsonOutput.toJson(tfProperties) +'},'
            }            
        }

        return (bodyJson == '') ? '' : bodyJson[0..-2]        
    }

    // UTILS DOTNET
    def executeCommand(String commandName = 'build', String params = '') {
        if (commandName == null || commandName == '') {
            Utils.throwExceptionInstance('IllegalArgumentException', 'Missing required input parameters')
        }

        context.sh script: "dotnet ${commandName} ${params}", label: 'Execute dotnet command'
    }

    def restore(String credentials, String commandName = 'restore', String params = '', String url = '') {
        if (credentials == null || commandName == null || commandName == '') {
            Utils.throwExceptionInstance('IllegalArgumentException', 'Missing required input parameters')
        }

        url = (url == null || url == '' || url == DefaultConfiguration.PDXC_ARTIFACTORY_URL) ? DefaultConfiguration.PDXC_ARTIFACTORY_URL + '/api/nuget/diaas-nuget' : url

        context.withCredentials([context.usernamePassword(credentialsId:'diaas-rw', passwordVariable:'ARTIF_PASSWORD', usernameVariable:'ARTIF_USER')]) {
            context.sh script: """
                    nuget sources add -name artifactory -source ${url} -username ${context.env.ARTIF_USER} -password ${context.env.ARTIF_PASSWORD} -StorePasswordInClearText
                    nuget setapikey ${context.env.ARTIF_USER}:${context.env.ARTIF_PASSWORD} -source artifactory
                """, label: 'Execute dotnet command'

            executeCommand(commandName, params)
        }
    }

    /**
    * Executes the function that is provided in the specific key (stage) of the functions map parameter.
    * This function is used to allow consumers of the generic pipelines to execute an extra function at the end of one
    * of the generic stages that are included by default on each pipeline. This allows adding extra stages or just
    * executing one extra function dynamically.
    *
    * Functions are accessed in this way: 'functions[stage]["func"]()'
    *
    * @param functions Map containing the functions to be executed.
    * @param stage Name of the key to be retrieved.
    */
    void executePostStageFunction(Map functions, String stage) {
        if (functions != null && functions[stage] != null && functions[stage]['func'] != null && functions[stage]['func'] != '') {
            functions[stage]['func']()
        }
    }

    /**
    * Returns true or false depending on the values found in the provided map for the given key (stage parameter).
    * This function is used on the generic pipelines to decide whether one of the generic stages has to be executed or
    * just skipped. This allows the consumers of the generic pipelines to decide whether a stage is required when
    * running a pipeline.
    *
    * Check is done on: functions[stage]["skip"] and looking for true value.
    *
    * @param functions Map containing the skip conditions.
    * @param stage Name of the key to be retrieved.
    * @return True when "skip" is false.
    */
    boolean notSkipStage(Map functions, String stage) {
        return (functions[stage] == null || functions[stage]['skip'] == null || functions[stage]['skip'] != true)
    }

    /**
    * Generic function to delete the workspace contents.
    */
    void deleteWorkspace() {
        sh 'cd /'
        deleteDir()
    }

    /**
    * Set up a specific docker agent for the run of the generic pipeline. By default, if the file searched does not
    * exist, it will try to load the dockerFile from the /resources folder.
    * @param dockerPath Relative path (inside the workspace) to the dockerfile to be used (including the name)
    * @param dockerTargetName Name of the target file to be created.
    * @param stagesMap the stages map, in case conditional code uses it (optional)
    */
    void setDockerAgentTF(String dockerPath, String dockerTargetName, LinkedHashMap stagesMap = null) {
        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        def dockerFile
        if (exists) {
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: "conf.yml"
            def pipelineType = config.pipelineType
            if ("TERRAFORM_DEPLOY_PIPELINE_12" != pipelineType) {
                def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)
                //dockerFile = context.readFile("/custom/${projectType}-project/${dockerTargetName}")
                dockerFile = context.libraryResource "custom/${projectType}-project/${dockerTargetName}"
            } else {
                def tfVersion = config.terraform?.version
                //dockerFile = context.readFile("/custom/deploy-project/deploy-12.Dockerfile")
                dockerFile = context.libraryResource "custom/deploy-project/deploy-12.Dockerfile"
                if (tfVersion != null) {
                    dockerFile = dockerFile.replace("ENV TERRAFORM_VERSION=0.12", "ENV TERRAFORM_VERSION=${tfVersion}")
                }
                if (stagesMap != null && !notSkipStage(stagesMap, 'checkov')) {
                    dockerFile = dockerFile.replace("ENV INSTALL_CHECKOV=true", "ENV INSTALL_CHECKOV=false")
                }

                /*
                 * We need various scripts installed in the docker image
                 */
                def gpp_base64 = context.libraryResource "custom/deploy-project/tfapi/silver-image-scripts/gpp.base64"
                def install_tf = context.libraryResource "custom/deploy-project/tfapi/silver-image-scripts/install-tf.sh"
                def preprocessor_runner = context.libraryResource "custom/deploy-project/tfapi/silver-image-scripts/run-preprocessor.sh"
                def stage_modules = context.libraryResource "custom/deploy-project/tfapi/silver-image-scripts/stage-modules.sh"
                def tf_profile = context.libraryResource "custom/deploy-project/tfapi/silver-image-scripts/tf-profile.sh"
                def tf_repo_functions = context.libraryResource "custom/deploy-project/tfapi/silver-image-scripts/tf-repo-functions.sh"

                context.writeFile file: "./silver-image-scripts/gpp.base64", text: gpp_base64
                context.writeFile file: "./silver-image-scripts/install-tf.sh", text: install_tf
                context.writeFile file: "./silver-image-scripts/run-preprocessor.sh", text: preprocessor_runner
                context.writeFile file: "./silver-image-scripts/stage-modules.sh", text: stage_modules
                context.writeFile file: "./silver-image-scripts/tf-profile.sh", text: tf_profile
                context.writeFile file: "./silver-image-scripts/tf-repo-functions.sh", text: tf_repo_functions
            }
        }
        context.writeFile file: dockerTargetName, text: dockerFile
    }

    void setDockerAgent(String dockerPath, String dockerTargetName) {
        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        def dockerFile
        if (exists) {
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: "conf.yml"
            def pipelineType = config.pipelineType
            if ("TERRAFORM_DEPLOY_PIPELINE_12" != pipelineType) {
                def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)
                //dockerFile = context.readFile("/custom/${projectType}-project/${dockerTargetName}")                
                dockerFile = context.libraryResource "custom/${projectType}-project/${dockerTargetName}"                
            } else {
                //dockerFile = context.readFile("/custom/deploy-project/deploy-12.Dockerfile")
                dockerFile = context.libraryResource "custom/deploy-project/deploy-12.Dockerfile"
            }
        }
        context.writeFile file: dockerTargetName, text: dockerFile
    }

    /**
    * Determines the Docker registry used by the image specified in the Dockerfile.
    * If a custom registry domain is found in the first FROM instruction (via regex),
    * that domain is returned as a URL; otherwise, null is returned to indicate DockerHub.
    * @param configData Configuration data for the pipeline.
    * @param dockerTargetName Name of the Dockerfile to be scanned.
    * @param pipelineName Name of the pipeline.
    * @return A list containing the Docker registry URL and credentials.
    */
    List<String> getDockerRegistryUrlAndCreds(Map configData, String dockerTargetName, String pipelineName) {
        String dockerRegistry = ValuesUtils.getVariable(configData, 'dockerRegistry')
        if (dockerRegistry == null || dockerRegistry.trim() == "") {
            context.echo("Deriving Docker Registry from ${dockerTargetName}")
            String reg = context.sh(script: """
                cat ${dockerTargetName} | grep 'FROM\\s*[^/]*/' | head -n 1 | sed -e 's/FROM *//' -e 's|/.*||'
                """,
                returnStdout: true
            ).trim()

            context.echo("Scan of FROM in ${dockerTargetName} returned '${reg}'")
            if (reg?.contains('.')) {
                dockerRegistry = reg.trim()
            }
        }
        String creds = getDockerRegistryCreds(configData, dockerRegistry, pipelineName)
        String url = dockerRegistry == null ? null : "https://${dockerRegistry}/"
        context.echo("Docker registry url is '${url}'")
        return [url, creds]
    }

    private def getDockerRegistryCreds(def configData, String dockerRegistry, String pipelineName) {
        String dockerRegistryCreds = ValuesUtils.getVariable(configData, 'dockerRegistryCredential')
        if (dockerRegistryCreds == null || dockerRegistryCreds.trim() == "") {
            if (dockerRegistry == 'docker.dxc.com') {
                dockerRegistryCreds = 'diaas-rw'
            } else if (dockerRegistry == null) {
                if (pipelineName == 'docker' || pipelineName == 'custom' || pipelineName == 'javadocker' || pipelineName == 'javazip') {
                    dockerRegistryCreds = 'assure-docker'
                } else if (pipelineName == 'lambda' || pipelineName == 'python' || pipelineName == 'multiRust' || pipelineName == 'rust') {
                    dockerRegistryCreds = 'assure-docker-lambda'

                } else if (pipelineName == 'ui' || pipelineName == 'npm' || pipelineName == 'data' || pipelineName == 'dotnet') {
                    dockerRegistryCreds = 'assure-docker-ui'
                } else {
                    dockerRegistryCreds = 'assure-docker-ui'
                }
            }
        }
        return dockerRegistryCreds
    }

    void setDockerAgentDocker(String dockerPath, String dockerTargetName) {
        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        def dockerFile
        if (exists) {
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: "conf.yml"
            def pipelineType = config.pipelineType
            if ("TERRAFORM_DEPLOY_PIPELINE_12" != pipelineType) {
                def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)
                def versionImageResource = context.libraryResource "custom/${projectType}-project/${projectType}-docker.yml" 
                 
                context.writeFile file: "${projectType}-docker.yml", text: versionImageResource
                def versionImageYml = context.readYaml file: "${projectType}-docker.yml"
                def mayorVersion = config.nodeVersion
                def dockerBaseVersion = versionImageYml."node-${mayorVersion}"

                if (dockerBaseVersion == null) {
                    context.error("-- ⚠️ -- Node Version ${mayorVersion} not supported. Please check docs.")
                }

                //NPM version
                def npmVersion = config.npmVersion
                def dockerNpmVersion = versionImageYml."npm-${npmVersion}"

                dockerFile = context.libraryResource "custom/${projectType}-project/${dockerTargetName}"

                // Check if npm 10 is usable in the pipeline
                def nodeVersion = config.nodeVersion
                if (npmVersion == 10 && nodeVersion <= 16) {
                    context.error("-- ⚠️ -- NPM 10 is incompatible with Node.js version: ${nodeVersion}. Please check your configuration file.")
                }

                if (npmVersion >= 8) {
                    dockerFile = dockerFile.replace("&& npm config set unsafe-perm true",'&& echo "Removed unsafe-perm while using npm version 9"')
                }

                dockerFile = dockerFile.replace("node:0.0.0-alpine", "node:${dockerBaseVersion}-alpine")
                context.echo "-- 💁 -- You are currently using Node Version: ${dockerBaseVersion}"

                dockerFile = dockerFile.replace("npm@0", "npm@${dockerNpmVersion}")
                context.echo "-- 💁 -- You are currently using NPM Version: ${dockerNpmVersion}"              
            } else {
                dockerFile = context.libraryResource "custom/deploy-project/deploy-12.Dockerfile"
            }
        }
        context.writeFile file: dockerTargetName, text: dockerFile
    }

    void setDockerAgentDotNet(String dockerPath, String dockerTargetName) {
        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        def dockerFile
        if (exists) {
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: "conf.yml"
            def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)    
            def dotnetVersion = config.dotnetVersion
            context.echo "-- 💁 -- You are currently using .NET Version: ${dotnetVersion}"

            if (dotnetVersion == 7) {
                dockerFile = context.libraryResource "custom/${projectType}-project/${dockerTargetName}"
            } else {
                dockerFile = context.libraryResource "custom/${projectType}-project/dotnet-${dotnetVersion}.Dockerfile"
            }
        }
        context.writeFile file: dockerTargetName, text: dockerFile
    }

    void setDockerAgentForCustomNodeAndNpmVersion(String dockerPath, String dockerTargetName) {
        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        def dockerFile
        if (exists) {
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: "conf.yml"    
            def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)

            def versionImageResource = context.libraryResource "custom/${projectType}-project/${projectType}-docker.yml"  
            // def versionImageResource = context.libraryResource "${projectType}-docker.yml"  
            context.writeFile file: "${projectType}-docker.yml", text: versionImageResource
            def versionImageYml = context.readYaml file: "${projectType}-docker.yml"
            def mayorVersion = config.nodeVersion
            def dockerBaseVersion = versionImageYml."node-${mayorVersion}"

            if (dockerBaseVersion == null) {
                context.error("-- ⚠️ -- Node Version ${mayorVersion} not supported. Please check docs.")
            }

            //NPM version
            def npmVersion = config.npmVersion
            def dockerNpmVersion = versionImageYml."npm-${npmVersion}"

            dockerFile = context.libraryResource "custom/${projectType}-project/${dockerTargetName}"
            // dockerFile = context.libraryResource "${dockerTargetName}"

            // Check if npm 10 is usable in the pipeline
            def nodeVersion = config.nodeVersion
            if (npmVersion == 10 && nodeVersion <= 16) {
                context.error("-- ⚠️ -- NPM 10 is incompatible with Node.js version: ${nodeVersion}. Please check your configuration file.")
            }
            
            if (npmVersion >= 9) {
                dockerFile = dockerFile.replace("&& npm config set unsafe-perm true",'&& echo "Removed unsafe-perm while using npm version 9"')                
            }

            if (npmVersion < 9) {
                context.error("-- ⚠️ -- NPM version is deprecated. Please update your configuration file.")
            }

            dockerFile = dockerFile.replace("node:0.0.0-alpine", "node:${dockerBaseVersion}-alpine")
            context.echo "-- 💁 -- You are currently using Node Version: ${dockerBaseVersion}"

            dockerFile = dockerFile.replace("npm@0", "npm@${dockerNpmVersion}")
            context.echo "-- 💁 -- You are currently using NPM Version: ${dockerNpmVersion}"

        }
        context.writeFile file: dockerTargetName, text: dockerFile
    }

    /**
    * Set up a specific docker agent for the run of the generic pipeline. By default, if the file searched does not
    * exist, it will try to load the dockerFile from the /resources folder.
    * @param dockerPath Relative path (inside the workspace) to the dockerfile to be used (including the name)
    * @param dockerTargetName Name of the target file to be created.
    */
    void setDockerAgentJava(String dockerPath, String dockerTargetName) {
        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        def dockerFile
        if (exists) {
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: "conf.yml"    
            def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)

            def versionImageResource = context.libraryResource "custom/${projectType}-project/${projectType}-docker.yml"  
            context.writeFile file: "${projectType}-docker.yml", text: versionImageResource
            def versionImageYml = context.readYaml file: "${projectType}-docker.yml"
            def mayorJavaVersion = config.javaVersion
            def dockerBaseVersion = versionImageYml."java-${mayorJavaVersion}"

            def mayorMavenVersion = config.mavenVersion
            def dockerMavenVersion = versionImageYml."maven-${mayorMavenVersion}"

            dockerFile = context.libraryResource "custom/${projectType}-project/${dockerTargetName}"

            dockerFile = dockerFile.replace("maven:0.0.0", "maven:${dockerMavenVersion}-${dockerBaseVersion}")
            context.echo "-- 💁 -- You are currently using JDK Version ${mayorJavaVersion}"
            context.echo "-- 💁 -- You are currently using Maven Version: ${dockerMavenVersion}"
        }
        context.writeFile file: dockerTargetName, text: dockerFile
    }

    void setDockerAgentPython(String dockerPath, String dockerTargetName) {
        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        def dockerFile
        if (exists) {
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: "conf.yml"    
            def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)

            def versionImageResource = context.libraryResource "custom/${projectType}-project/${projectType}-docker.yml"  
            // def versionImageResource = context.libraryResource "${projectType}-docker.yml"  
            context.writeFile file: "${projectType}-docker.yml", text: versionImageResource
            def versionImageYml = context.readYaml file: "${projectType}-docker.yml"
            def mayorVersion = config.pythonVersion
            def dockerBaseVersion = versionImageYml."python-${mayorVersion}"

            if (dockerBaseVersion == null) {
                context.error("-- ⚠️ -- Python Version ${mayorVersion} not supported. Please check docs.")
            }

            dockerFile = context.libraryResource "custom/${projectType}-project/${dockerTargetName}"
            // dockerFile = context.libraryResource "${dockerTargetName}"

            dockerFile = dockerFile.replace("python:0.0.0-slim-bookworm", "python:${dockerBaseVersion}-slim-bookworm")
            context.echo "-- 💁 -- You are currently using Python Version: ${dockerBaseVersion}"

        }
        context.writeFile file: dockerTargetName, text: dockerFile
    }

    void checkoutLibrary() {
        def exists = context.fileExists "${libraryRepoName}"
        if (!exists) {
            def configData = context.readYaml file: 'conf.yml'

            // Set up git
            def cred = ValuesUtils.getVariable(configData, 'gitHubCredential')
            def mail = ValuesUtils.getVariable(configData, 'gitEmail')
            def user = ValuesUtils.getVariable(configData, 'gitUsername')
            def url = ValuesUtils.getVariable(configData, 'gitHubUrl')

            url = (url == null || url == '') ? DefaultConfiguration.PDXC_GITHUB_HOST : url

            context.withCredentials([context.usernamePassword(credentialsId: cred, passwordVariable: 'GIT_PASSWORD', usernameVariable: 'GIT_USER')]) {
                context.sh script: """
                        touch ~/.netrc
                        echo 'machine ${url}' >> ~/.netrc
                        echo 'login ${context.env.GIT_USER}' >> ~/.netrc
                        echo 'password ${context.env.GIT_PASSWORD}' >> ~/.netrc
                        git config --global --replace-all user.email '${context.env.GIT_USER}@dxc.com'
                        git config --global --replace-all user.name '${context.env.GIT_USER}'
                    """, label: 'Set up Git credentials'
            }

            // Clone Assure library repo
            def org = ValuesUtils.getVariable(configData, 'libraryOrg')
            def repo = ValuesUtils.getVariable(configData, 'libraryRepo')
            def branch = ValuesUtils.getVariable(configData, 'libraryBranch')

            url = DefaultConfiguration.PDXC_GITHUB_URL
            def branchCommand = (branch != null && branch != '') ? "-b ${branch}" : ''
            context.sh script: """
                    rm -rf ./${repo}
                    git clone ${branchCommand} ${url}/${org}/${repo}.git
                """, label: 'Delete directory and perform git clone'
        }
    }

    /**
    * Push docker image to artifactory repository.
    *
    * @param credentials Jenkins CredentialID (user:password) for Artifactory.
    * @param repository Name of the Artifactory repository where artifact is stored.
    * @param imgName Name to be given in Artifactory (IMAGE_ID).
    * @param tag Tag to be used when pushing artifact.
    * @param dockerFile Path to DockerFile (it will be added ./ at the start).
    * @param url Artifactory url. OPTIONAL: Default is PDXC Docker Artifactory url.
    */
    def uploadDockerArtifact(String credentials, String repository, String imgName, String tag, String dockerFile, String targetPath='.', String url = '', def configData, def dockerName) {
        if (credentials == null || credentials == '' || repository == null || repository == '' ||
                imgName == null || imgName == '' || tag == null || tag == '' || dockerFile == null && dockerFile == '') {
            Utils.throwExceptionInstance('IllegalArgumentException', 'Missing required input parameters')
        }

        url = (url == '') ? DefaultConfiguration.DOCKER_ARTIFACTORY_URL : url
        url = ValuesUtils.removeStartEndChars(url, '/', true, true)
        repository = ValuesUtils.removeStartEndChars(repository, '/', true, true)
        
        context.docker.withRegistry("https://${url}", credentials) {
            context.withCredentials([context.usernamePassword(credentialsId: credentials, usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                dockerFile = ValuesUtils.removeStartEndChars(dockerFile, '/', true, true)
                context.sh "docker login ${url} --username ${context.env.USERNAME} --password \"${context.env.PASSWORD}\""

                def imageBuildArgs = ""                
                def credRequired = ValuesUtils.getVariable(configData, 'npmcredentials-required')
                if (credRequired != null && credRequired == true) {
                    def credUserpwd = ValuesUtils.getVariable(configData, 'npmcredentials-userpassword')
                    context.withCredentials([context.usernamePassword(credentialsId: credUserpwd, usernameVariable: 'NPM_USER', passwordVariable: 'NPM_PASSWORD')]) {
                        imageBuildArgs = " --build-arg NPM_USER=${context.env.NPM_USER} --build-arg NPM_PASSWORD=${context.env.NPM_PASSWORD} "
                    }
                    def credToken = ValuesUtils.getVariable(configData, 'npmcredentials-token')
                    context.withCredentials([context.string(credentialsId:credToken, variable:'NPMTOKEN')]) {
                        imageBuildArgs += " --build-arg NPM_TOKEN=${context.env.NPMTOKEN} "
                    }
                    def credAuth = ValuesUtils.getVariable(configData, 'npmcredentials-auth')
                    context.withCredentials([context.string(credentialsId:credAuth, variable:'NPMAUTH')]) {
                        imageBuildArgs += " --build-arg NPM_AUTH=${context.env.NPMAUTH} "
                    }
                }
                imageBuildArgs += """
                                    --build-arg application=${imgName} \
                                    --build-arg version=${tag} --no-cache -f ./${dockerFile} ${targetPath}
                                """.trim()

                // --secret id=assure-docker 

                def img 

                try { 
                    img = context.docker.build("${repository}/${imgName}:${tag}", imageBuildArgs)
                } catch (Exception e) {
                    context.echo "--❌️-- Error detected during docker build, trying again"
                    context.withCredentials([context.usernamePassword(credentialsId: 'DOCKER-PRO', usernameVariable: 'USERNAMEPRO', passwordVariable: 'PASSWORDPRO')]) {
                        context.sh "docker login --username ${context.env.USERNAMEPRO} --password \"${context.env.PASSWORDPRO}\""
                        context.sh "docker build --no-cache -f ./${dockerFile} ${targetPath}"
                    }
                    context.sh "docker login ${url} --username ${context.env.USERNAME} --password \"${context.env.PASSWORD}\""
                    img = context.docker.build("${repository}/${imgName}:${tag}", imageBuildArgs)
                }

                def imageID = context.sh (
                    script: "docker images ${repository}/${imgName}:${tag} --format '{{.ID}}'",
                    returnStdout: true,
                    label: 'Loading docker ID').trim()
                context.echo "Obtained image ID: ${imageID}"

                def prismaStatusCode = 1
                def prismaSkip = ValuesUtils.getVariable(configData, 'skip', 'prisma')

                // Twistlock Stage
                context.stage("Twistlock Scan ${imgName}") {
                    if (prismaSkip == false){
                        def prismaQualityGate = ValuesUtils.getVariable(configData, 'prismaQualityGate', 'prisma')
                        def (prismaData, uuid) = decryptYmlFile(configData.passphrase_id, configData.prisma_conf_file, "prisma/prismaConfiguration.yml.gpg")
                        def scriptPath = "prisma/prisma.sh"
                        prismaScan('docker', "${imageID}", "${imgName}", scriptPath, prismaData, uuid)

                        prismaStatusCode = context.sh (
                            script: "grep -wq 'check results: FAIL' twistlock-scan-results-${imageID}_${uuid}.log",
                                    returnStatus: true)

                        if (prismaStatusCode!= 1 && prismaQualityGate){
                            context.currentBuild.result = 'ABORTED'
                            context.error("-- ❌ -- Pipeline ABORTED ❗❗ Twistlock Scan 🛸 status: FAIL (prismaQualityGate is ${prismaQualityGate})")
                            return
                        }

                        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                            if (prismaStatusCode!= 1){
                                context.error("-- ⚠️ -- Twistlock Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                                return
                            }
                        }
                    } else {
                        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                            context.error("-- ⚠️ -- Twistlock Scan 🛸 Stage SKIPPED ❗❗ functions['prisma'] = ['prismaSkip': ${prismaSkip}]")
                            return
                        }
                    }
                }

                def checkovStatusCode = 0
                def checkovSkip = ValuesUtils.getVariable(configData, 'skip', 'checkov')

                // Checkov Stage
                context.stage("Checkov scan ${imgName}") {
                    if (checkovSkip == false){
                        def checkovQualityGate = ValuesUtils.getVariable(configData, 'checkovQualityGate', 'checkov')
                        def (checkovData, uuidCheckov) = decryptYmlFile(configData.passphrase_id, configData.checkov_conf_file, "checkov/checkovConfiguration.yml.gpg")
                        def scriptPath = "checkov/checkov.sh"

                        checkovScan('docker', "${imageID}", "${imgName}", scriptPath, uuidCheckov, dockerName)

                        def checkName = 'Vulnerabilities'
                        context.withChecks("${checkName}") {
                            context.junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: "**/results*.xml")
                        }
                        markGitHubCheckNeutral(configData, checkName)

                        checkovStatusCode = checkovErrorCheck(imageID, uuidCheckov)
                        
                        if(checkovStatusCode != 0 && checkovQualityGate){
                            context.currentBuild.result = 'ABORTED'
                            context.error ("-- ❌ -- Pipeline ABORTED ❗❗ Checkov Scan 🛸 status: FAIL (checkovQualityGate is ${checkovQualityGate})")
                            return
                        }

                        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                            if(checkovStatusCode != 0){
                                context.error("-- ⚠️ -- Checkov Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                                return
                            }
                        }
                    }
                    else{
                        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                            context.error("-- ⚠️ -- Checkov Scan 🛸 Stage SKIPPED ❗❗ functions['checkov'] = ['checkovSkip': ${checkovSkip}]")
                            return
                        }
                    }     
                }

                // Upload Docker image Stage
                context.stage("Upload ${imgName} Docker Image") {
                    img.push()
                    img = context.docker.build("${repository}/${imgName}:latest", imageBuildArgs)
                    img.push()
                }

                // List Docker images for the current repo and image name
                def images = listImages("${repository}/${imgName}")

                def buildVersion = ValuesUtils.getVariable(configData, 'artifactoryTag', 'setup')
                try { 
                    // Removes (and un-tags) buildVersion/latest undesired path from the host node
                    context.sh """
                            set +x;
                            docker rmi ${repository}/${imgName}:${buildVersion};
                            docker rmi ${repository}/${imgName}:latest                            
                        """ 
                    
                    // Removes (and un-tags) previous/latest undesired path from the host node
                    untagDockerImages(images, buildVersion)

                    //listImages("${repository}/${imgName}")

                    def outputInspect = context.sh (
                            script: """  
                                        docker inspect ${imageID}                                        
                                    """,
                            returnStdout: true,
                            label: 'Executing docker inspect').trim()
                    context.echo "DOCKER INSPECT ${outputInspect}"
                    
                    context.writeJSON file: "./${imgName}_${tag}.json", json: outputInspect
                } catch (Exception e) {
                    context.echo "--⚠️-- Error detected during docker inspect"	
                }

                def jsonUrl = ""
                if (context.fileExists("${imgName}_${tag}.json")) {
                    context.stage('Upload Docker JSON') {                    
                        def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
                        def artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL                   
                        def repo = ValuesUtils.getVariable(configData, 'artifactRepositoryJson', 'upload')                  
                        def artifactPath = ValuesUtils.getVariableArrayList(configData, 'artifactPath').toLowerCase()                    
                        uploadGenericArtifact(cred, repo, artifactPath, "${imgName}_${tag}" + '.json', "", artifactoryURL)
                        jsonUrl = "${artifactoryURL}/${repo}/${artifactPath}/${imgName}_${tag}.json"
                    }
                }

                return (context.fileExists("./${imgName}_${tag}.json")) ? ["${jsonUrl}", checkovStatusCode] : "Json file not generated"
            }
        }
    }

    /**
    * Push multidocker image to artifactory repository.
    *
    * @param credentials Jenkins CredentialID (user:password) for Artifactory.
    * @param repository Name of the Artifactory repository where artifact is stored.
    * @param imgName Name to be given in Artifactory (IMAGE_ID).
    * @param tag Tag to be used when pushing artifact.
    * @param dockerFile Path to DockerFile (it will be added ./ at the start).
    * @param url Artifactory url. OPTIONAL: Default is PDXC Docker Artifactory url.
    */
    def uploadMultiDockerArtifact(String credentials, String repository, String imgName, String tag, String dockerFile, String targetPath='.', String url = '', def configData) {
        if (credentials == null || credentials == '' || repository == null || repository == '' ||
                imgName == null || imgName == '' || tag == null || tag == '' || dockerFile == null && dockerFile == '') {
            Utils.throwExceptionInstance('IllegalArgumentException', 'Missing required input parameters')
        }

        url = (url == '') ? DefaultConfiguration.DOCKER_ARTIFACTORY_URL : url
        url = ValuesUtils.removeStartEndChars(url, '/', true, true)
        repository = ValuesUtils.removeStartEndChars(repository, '/', true, true)

        context.docker.withRegistry("https://${url}", credentials) {
            context.withCredentials([context.usernamePassword(credentialsId: credentials, usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                dockerFile = ValuesUtils.removeStartEndChars(dockerFile, '/', true, true)
                context.sh "docker login ${url} --username ${context.env.USERNAME} --password \"${context.env.PASSWORD}\""

                def imageBuildArgs = ""                
                def credRequired = ValuesUtils.getVariable(configData, 'npmcredentials-required')
                if (credRequired != null && credRequired == true) {
                    def credUserpwd = ValuesUtils.getVariable(configData, 'npmcredentials-userpassword')
                    context.withCredentials([context.usernamePassword(credentialsId: credUserpwd, usernameVariable: 'NPM_USER', passwordVariable: 'NPM_PASSWORD')]) {
                        imageBuildArgs = " --build-arg NPM_USER=${context.env.NPM_USER} --build-arg NPM_PASSWORD=${context.env.NPM_PASSWORD} "
                    }
                    def credToken = ValuesUtils.getVariable(configData, 'npmcredentials-token')
                    context.withCredentials([context.string(credentialsId:credToken, variable:'NPMTOKEN')]) {
                        imageBuildArgs += " --build-arg NPM_TOKEN=${context.env.NPMTOKEN} "
                    }
                    def credAuth = ValuesUtils.getVariable(configData, 'npmcredentials-auth')
                    context.withCredentials([context.string(credentialsId:credAuth, variable:'NPMAUTH')]) {
                        imageBuildArgs += " --build-arg NPM_AUTH=${context.env.NPMAUTH} "
                    }
                }
                imageBuildArgs += """
                                    --build-arg application=${imgName} \
                                    --build-arg version=${tag} --no-cache -f ./${dockerFile} ${targetPath}
                                """.trim()

                // --secret id=assure-docker 

                def img 

                try { 
                    img = context.docker.build("${repository}/${imgName}:${tag}", imageBuildArgs)
                } catch (Exception e) {
                    context.echo "--❌️-- Error detected during docker build, trying again"
                    context.withCredentials([context.usernamePassword(credentialsId: 'DOCKER-PRO', usernameVariable: 'USERNAMEPRO', passwordVariable: 'PASSWORDPRO')]) {
                        context.sh "docker login --username ${context.env.USERNAMEPRO} --password \"${context.env.PASSWORDPRO}\""
                        context.sh "docker build --no-cache -f ./${dockerFile} ${targetPath}"
                    }
                    context.sh "docker login ${url} --username ${context.env.USERNAME} --password \"${context.env.PASSWORD}\""
                    img = context.docker.build("${repository}/${imgName}:${tag}", imageBuildArgs)
                }

                def imageID = context.sh (
                    script: "docker images ${repository}/${imgName}:${tag} --format '{{.ID}}'",
                    returnStdout: true,
                    label: 'Loading docker ID').trim()
                context.echo "Obtained image ID: ${imageID}"

                def prismaStatusCode = 1
                def prismaSkip = ValuesUtils.getVariable(configData, 'skip', 'prisma')

                // Twistlock Stage
                context.stage("Twistlock scan ${imgName}") {
                    if (prismaSkip == false){
                        def prismaQualityGate = ValuesUtils.getVariable(configData, 'prismaQualityGate', 'prisma') 
                        def (prismaData, uuid) = decryptYmlFile(configData.passphrase_id, configData.prisma_conf_file, "prisma/prismaConfiguration.yml.gpg")
                        def scriptPath = "prisma/prisma.sh"           

                        prismaScan('docker', "${imageID}", "${imgName}", scriptPath, prismaData, uuid)

                        prismaStatusCode = context.sh (
                                                script: "grep -wq 'Scan failed' twistlock-scan-results-${imageID}_${uuid}.log",
                                                returnStatus: true)

                        if(prismaStatusCode == 0 && prismaQualityGate){
                            context.currentBuild.result = 'ABORTED'
                            context.error ("-- ❌ -- Pipeline ABORTED ❗❗ Twistlock Scan 🛸 status: FAIL (prismaQualityGate is ${prismaQualityGate})")
                            return
                        }
                        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                            if(prismaStatusCode == 0){
                                context.error("-- ⚠️ -- Twistlock Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                                return
                            }
                        }
                    }
                    else{
                        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                context.error("-- ⚠️ -- Twistlock Scan 🛸 Stage SKIPPED ❗❗ functions['prisma'] = ['prismaSkip': ${prismaSkip}]")
                                return
                        }
                    }     
                }

                def checkovStatusCode = 0
                def skip = ValuesUtils.getVariable(configData, 'skip', 'checkov')

                // Checkov Stage
                context.stage("Checkov scan ${imgName}") {
                    if (skip == false){
                        def checkovQualityGate = ValuesUtils.getVariable(configData, 'checkovQualityGate', 'checkov')
                        def (checkovData, uuidCheckov) = decryptYmlFile(configData.passphrase_id, configData.checkov_conf_file, "checkov/checkovConfiguration.yml.gpg")
                        def scriptPath = "checkov/checkov.sh"

                        checkovScan('multidocker', "${imageID}", "${imgName}", scriptPath, uuidCheckov, '')

                        def checkName = 'Vulnerabilities'
                        context.withChecks("${checkName}") {
                            context.junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: "**/results*.xml")
                        }
                        markGitHubCheckNeutral(configData, checkName)
                        
                        checkovStatusCode = checkovErrorCheck(imageID, uuidCheckov)
                        
                        if(checkovStatusCode != 0 && checkovQualityGate){
                            context.currentBuild.result = 'ABORTED'
                            context.error ("-- ❌ -- Pipeline ABORTED ❗❗ Checkov Scan 🛸 status: FAIL (checkovQualityGate is ${checkovQualityGate})")
                            return
                        }
                        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                            if(checkovStatusCode != 0){
                                context.error("-- ⚠️ -- Checkov Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                                return
                            }
                        }
                    }
                    else{
                        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                            context.error("-- ⚠️ -- Checkov Scan 🛸 Stage SKIPPED ❗❗ functions['checkov'] = ['skip': ${skip}]")
                            return
                        }
                    }     
                }
                
                // Upload Docker image Stage
                context.stage("Upload ${imgName} Docker Image") {
                    img.push()
                    img = context.docker.build("${repository}/${imgName}:latest", imageBuildArgs)
                    img.push()
                }

                // List Docker images for the current repo and image name
                def images = listImages("${repository}/${imgName}")

                def buildVersion = ValuesUtils.getVariable(configData, 'artifactoryTag', 'setup')
                try { 
                    // Removes (and un-tags) buildVersion/latest undesired path from the host node
                    context.sh """
                            set +x;
                            docker rmi ${repository}/${imgName}:${buildVersion};
                            docker rmi ${repository}/${imgName}:latest                            
                        """ 

                    // Removes (and un-tags) previous/latest undesired path from the host node
                    untagDockerImages(images, buildVersion)

                    //listImages("${repository}/${imgName}")

                    def outputInspect = context.sh (
                            script: """  
                                        docker inspect ${imageID}                                        
                                    """,
                            returnStdout: true,
                            label: 'Executing docker inspect').trim()
                    context.echo "DOCKER INSPECT ${outputInspect}"

                    context.writeJSON file: "./${imgName}_${tag}.json", json: outputInspect
                } catch (Exception e) {
                    context.echo "--⚠️-- Error detected during docker inspect"	
                }

                def jsonUrl = ""
                if (context.fileExists("${imgName}_${tag}.json")) {
                    context.stage('Upload Docker JSON') {                    
                        def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
                        def artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL                   
                        def repo = ValuesUtils.getVariable(configData, 'artifactRepositoryJson', 'upload')                  
                        def artifactPath = ValuesUtils.getVariableArrayList(configData, 'artifactPath').toLowerCase()                    
                        uploadGenericArtifact(cred, repo, artifactPath, "${imgName}_${tag}" + '.json', "", artifactoryURL)
                        jsonUrl = "${artifactoryURL}/${repo}/${artifactPath}/${imgName}_${tag}.json"
                    }
                }

                return (context.fileExists("./${imgName}_${tag}.json")) ? ["${jsonUrl}", checkovStatusCode] : "Json file not generated"
            }
        }
    }

    def listImages(String imageName) {
        def images = context.sh(
            script: "docker images --format '{{.Repository}}:{{.Tag}}:{{.ID}}' | grep ${imageName}",
            returnStdout: true
        ).trim()
        context.echo "Docker images 💿 tagged:\n${images}"
        return images
    }

    def untagDockerImages(def images, String buildVersion) {
        def name_image
        images.split('\n').each { line ->  // https://issues.jenkins.io/browse/JENKINS-46988
            name_image = line.split(":")
            if (name_image[1] == "<none>")
                context.sh "set +xe; docker rmi ${name_image[2]} || true ;"
            else if (name_image[1] != buildVersion && name_image[1] != "latest")
                context.sh "set +xe; docker rmi ${name_image[0]}:${name_image[1]} || true ;"
        }
    }

    // fileName should have extension
    def decryptYmlFile(String credentials, String fileName, String pathFile, String defaultRoute = "") {
        def uuid = UUID.randomUUID().toString()
        def file = context.libraryResource(resource: pathFile, encoding: "Base64")
        context.writeFile(file: "${defaultRoute}${fileName}_${uuid}.gpg", text: file, encoding: "Base64")
        def fileData

        context.withCredentials([context.string(credentialsId:credentials, variable:'PASSPHRASEVAR')]) {
            context.writeFile file: "${defaultRoute}passphrase.txt", text: "${context.env.PASSPHRASEVAR}"
            context.sh "gpg --pinentry-mode loopback --passphrase-file=${defaultRoute}passphrase.txt --decrypt-files ${defaultRoute}${fileName}_${uuid}.gpg"
            // context.sh "cd ${targetPath} && gpg --batch --passphrase ${context.env.PASSPHRASEVAR} ${fileName}.gpg"
            fileData = context.readYaml file: "${defaultRoute}${fileName}_${uuid}"
            //context.sh "cat ${fileData}"
        }
        return [fileData , uuid]
    }

    void branchProtect (String currentRepoName, String gitHubCredential) {
        def match_org = context.GIT_URL =~ /^((http[s]?):\/)?\/?([^:\/\s]+)(:([^\/]*))?((\/?(?:[^\/\?#]+\/+)*)([^\?#]*))(\?([^#]*))?(#(.*))?$/
        def org_url = (match_org.matches()) ? match_org.group(7).replaceAll("/", "") : null
        match_org = null // https://github.com/jenkinsci/pipeline-plugin/blob/master/TUTORIAL.md#serializing-local-variables
        
        def api_url = "${DefaultConfiguration.PDXC_GITHUB_URL}/api/v3/repos/${org_url}/${currentRepoName}/branches/${context.env.BRANCH_NAME}/protection"
        
        // https://docs.github.com/en/enterprise-server@2.22/rest/reference/repos#update-branch-protection
        
        def body_parameters = '''{"required_status_checks": null, 
                                    "enforce_admins": true, 
                                    "required_pull_request_reviews": { 
                                        "dismissal_restrictions": {}, 
                                        "dismiss_stale_reviews": false, 
                                        "require_code_owner_reviews": false, 
                                        "required_approving_review_count": 3
                                        }, 
                                    "restrictions": null
                                    }'''

        GitApi gitApi = GitApi.getInstance(DefaultConfiguration.PDXC_GITHUB_URL, gitHubCredential)
        def protect = gitApi.post(api_url, body_parameters, 'PUT', [[name: 'Accept', value: 'application/vnd.github.luke-cage-preview+json']], [200], 'APPLICATION_JSON', 'APPLICATION_JSON')
        
        context.sh script: """
                    echo ${context.env.BRANCH_NAME} has been protected
                    """, label: "Branch protection"
    }

    void prismaScan (String repoType, String artifactName, String repoName, String scriptPath, def prismaData, def uuid) {

        RestApi api = RestApi.getInstance("${prismaData.prisma_host}/api/v1/authenticate")
        try {
            def params = [:]
            params.put('username', prismaData.prisma_access_id)
            params.put('password', prismaData.prisma_secret_key)
            def bodyParams = JsonOutput.toJson(params)        
            def tokenPrisma = api.post("${prismaData.prisma_host}/api/v1/authenticate", bodyParams, 'POST', [], [200],'APPLICATION_JSON', 'NOT_SET').token

            List headers = [[name: 'Authorization', value: "Bearer ${tokenPrisma}", maskValue: true]]        
            def httpResponse = context.httpRequest url: "${prismaData.prisma_host}/api/v1/util/twistcli",
                        httpMode: 'GET',
                        customHeaders: headers,
                        acceptType: 'NOT_SET', 
                        contentType: 'NOT_SET',
                        outputFile: 'twistcli'                     
                        validResponseCodes: '200'

            context.sh """
                mv -n ./twistcli /usr/bin/twistcli
                chmod a+x /usr/bin/twistcli                        
            """
            def prismaScript = context.libraryResource(resource: scriptPath, encoding: "Base64")
            context.writeFile(file: "prisma.sh", text: prismaScript, encoding: "Base64")

            def outputScript = context.sh (
                        script: """ 
                                    set +x
                                    bash +x ./prisma.sh ${repoType} ${repoName} ${artifactName} ${uuid}
                                    set -x
                                """,
                        returnStdout: true,
                        label: 'Executing prisma scan').trim()

            context.echo "-- ℹ️ -- Twistlock scan results: ${outputScript}"
            outputScript = outputScript.replaceAll("\\e\\[[\\d;]*[^\\d;]","")
            def prismaFileName = "twistlock-scan-results-${artifactName}_${uuid}.log"
            context.writeFile file: "${prismaFileName}", text: "${outputScript}"
            context.archiveArtifacts artifacts: "${prismaFileName}"
        }
        catch (Exception e) {
            context.echo "Something was wrong in Prisma Scan Analysis"
        }       
    }

    void checkovScan (String repoType, String artifactName, String repoName, String scriptPath, def uuid, def dockerName) {

        try {            
            def checkovScript = context.libraryResource(resource: scriptPath, encoding: "Base64")
            context.writeFile(file: "pipelinesTempFiles/checkov.sh", text: checkovScript, encoding: "Base64")

            def skipPolicies = context.libraryResource "custom/deploy-project/skipPolicies.txt"
            
            def outputScript = context.sh (
                        script: """ 
                                    set +x
                                    bash +x pipelinesTempFiles/checkov.sh ${repoType} ${repoName} ${artifactName} ${uuid} ${dockerName} ${skipPolicies}
                                    set -x
                                """,
                        returnStdout: true,
                        encoding: 'UTF-8',
                        label: 'Executing Checkov scan').trim()

            outputScript = outputScript.replaceAll("(?m)^(\\s)*(<|;).*(\r\n|\r|\n)?", "")
            outputScript = outputScript.replaceAll("(?m)^(\\s)*(---|;).*(\r\n|\r|\n)?", "");
            context.echo "-- ℹ️ -- Checkov scan results \n ${outputScript}"
            outputScript = outputScript.replaceAll("[^\\x00-\\x7F]","");
            def checkovFileName = "checkov-scan-results-${artifactName}_${uuid}.log" 
            context.writeFile(file: "${checkovFileName}", text: "${outputScript}")
            context.archiveArtifacts artifacts: "${checkovFileName}"

            if (repoType == 'nodejs' || repoType == 'deploy') {
                //Store sbom files
                context.archiveArtifacts artifacts: "**/sbom-*.xml"
                context.sh "rm -rf sbom-*.xml"
            }

            // Remove script
            context.sh(script: """                
                rm -rf pipelinesTempFiles/checkov.sh
                rm -rf pipelinesTempFiles/passphrase.txt
                rm -rf pipelinesTempFiles/checkovConfiguration.yml_${uuid}
                rm -rf pipelinesTempFiles/checkovConfiguration.yml_${uuid}.gpg
            """, label: "Remove checkov secrets")

        }
        catch (Exception e) {
            context.echo "Something was wrong in Checkov Scan Analysis"
        }       
    }

    void uploadNpmArtifact(String path, String repository, String tag = "", String artifactoryUrl = "") {

        if (repository == null || repository == '' || tag == null) {
            Utils.throwExceptionInstance('IllegalArgumentException', 'Missing required input parameters')
        }

        artifactoryUrl = ValuesUtils.removeStartEndChars(artifactoryUrl, '/', false, true)
        repository = ValuesUtils.removeStartEndChars(repository, '/', true, true)        
        def artifactoryNpmPath = '/api/npm'
        
        tag = (tag != "") ? "--tag " + "${tag}" : ""

        String pathModified = ValuesUtils.removeStartEndChars(path, '/', true, false)
        if (pathModified != '' && pathModified[0] == '/') {
            Utils.throwExceptionInstance('IllegalArgumentException', "Invalid path provided: ${path}")
        }
        
        context.sh """
                cd ./${pathModified}                
                npm publish --registry ${artifactoryUrl}${artifactoryNpmPath}/${repository}/ ${tag}
        """
    }

    @NonCPS
    def getAWSVersion (def dataFile, def tfVersion) {
                
        def versionAwsProvider = (tfVersion == '0.12') ? '3.0' : '2.0'

        def originVersionAwsProvider = 'unknown'

        def dataFileCopy = dataFile

        def posProviderAws
        def found = false
        def finalPos = 0
        def newAwsFile = ""
        def currentLine
        def sendEmail = false
        
        dataFileCopy.eachLine { line ->
            currentLine = line
            currentLine = currentLine.replaceAll("\\s", '')        
            if (currentLine ==~ /(provider+\"+aws+\"+\{)/) {            
                posProviderAws = dataFile.indexOf(line, finalPos)            
                finalPos = posProviderAws 
                found = true             
            }
            if (currentLine ==~ /^\}/ && found == true) {            
                finalPos = dataFile.indexOf(line, finalPos)            
                def sub = dataFile.substring(posProviderAws, finalPos)  
                if (!sub.contains('alias') && !sub.contains('version')) {                
                    newAwsFile = dataFile.substring(0, finalPos) +'\nversion = "~>'+versionAwsProvider+'"\n'+ dataFile.substring(finalPos + ('\nversion = "~>'+versionAwsProvider+'"\n').size()+1,dataFile.size())
                    sendEmail = true
                    originVersionAwsProvider = versionAwsProvider
                }
                else {                                  
                    found = false
                    if (sub.contains('version') && sub.contains('2.0')) {                                               
                        originVersionAwsProvider = '2.0'
                    }
                }
            }
            else {
                finalPos += line.size()            
            }
        }

        return [newAwsFile, sendEmail, originVersionAwsProvider]
    }

    void sonarReport (def securityHotspotsResponse, def bugsResponse, def overAllResponse, def projectResponse, def codeSmellResponse, def vulnerabilitiesResponse) {
        if (securityHotspotsResponse.hotspots != null && securityHotspotsResponse.hotspots.size() > 0
            || bugsResponse.issues != null && bugsResponse.issues.size() > 0
            || codeSmellResponse.issues != null && codeSmellResponse.issues.size() > 0
            || vulnerabilitiesResponse.issues != null && vulnerabilitiesResponse.issues.size() > 0
            || overAllResponse.component != null && overAllResponse.component.measures.size() > 0
            || projectResponse.projectStatus != null && projectResponse.projectStatus.conditions != null && projectResponse.projectStatus.conditions.size() > 0) {

            def responseProjectStatus = projectResponse.projectStatus
            def projectStatus = (responseProjectStatus?.status != 'ERROR') ? 'PASSED' : 'FAILED'
            def projectColor = (responseProjectStatus?.status != 'ERROR') ? 'green' : 'red'

            def statusTable = """ 
                            <table style="text-align: center; width: 200; height: 60">
                            <tr style="border-bottom: 1px solid grey">
                            <td style="background-color: ${projectColor}; color: white">${projectStatus}</th>
                            </tr>
                            </table>
                            <br>
                            """
            def bugsTable = "<table style='text-align: center'><tr style='border-bottom:1px solid grey'>" +
                    "<th style='background-color: #5f249f; color: white'>Component</th>" +
                    "<th style='background-color: #5f249f; color: white'>Line</th>" +
                    "<th style='background-color: #5f249f; color: white'>Message</th>" +
                    "<th style='background-color: #5f249f; color: white'>Severity</th>" +
                    "<th style='background-color: #5f249f; color: white'>Type</th>" +                
                    "</tr>"
            bugsResponse.issues[0].each { issue ->
                String issueComponent = issue.component
                String issueLine = (issue.line != null) ? issue.line : 'n/a'
                String issueMessage = issue.message
                String issueSeverity = issue.severity
                String issueType = issue.type

                bugsTable += "<tr style='border-bottom:1px solid grey'>" +
                        "<td><b> ${issueComponent}</b></td>" +
                        "<td>${issueLine}</td>" +
                        "<td>${issueMessage}</td>" +
                        "<td>${issueSeverity}</td>" +
                        "<td>${issueType}</td>" +
                        "</tr>"
            }

            def codeSmellsTable = "<table style='text-align: center'><tr style='border-bottom:1px solid grey'>" +
                    "<th style='background-color: #5f249f; color: white'>Component</th>" +
                    "<th style='background-color: #5f249f; color: white'>Line</th>" +
                    "<th style='background-color: #5f249f; color: white'>Message</th>" +
                    "<th style='background-color: #5f249f; color: white'>Severity</th>" +
                    "<th style='background-color: #5f249f; color: white'>Type</th>" +
                    "</tr>"
            codeSmellResponse.issues[0].each { issue ->
                String issueComponent = issue.component
                String issueLine = (issue.line != null) ? issue.line : 'n/a'
                String issueMessage = issue.message
                String issueSeverity = issue.severity
                String issueType = issue.type

                codeSmellsTable += "<tr style='border-bottom:1px solid grey'>" +
                        "<td><b>${issueComponent}</b></td>" +
                        "<td>${issueLine}</td>" +
                        "<td>${issueMessage}</td>" +
                        "<td>${issueSeverity}</td>" +
                        "<td>${issueType}</td>" +
                        "</tr>"
            }
            def vulnerabilitiesTable = "<table style='text-align: center'><tr style='border-bottom:1px solid grey'>" +
                    "<th style='background-color: #5f249f; color: white'>Component</th>" +
                    "<th style='background-color: #5f249f; color: white'>Line</th>" +
                    "<th style='background-color: #5f249f; color: white'>Message</th>" +
                    "<th style='background-color: #5f249f; color: white'>Severity</th>" +
                    "<th style='background-color: #5f249f; color: white'>Type</th>" +                
                    "</tr>"
            vulnerabilitiesResponse.issues[0].each { issue ->
                String issueComponent = issue.component
                String issueLine = (issue.line != null) ? issue.line : 'n/a'
                String issueMessage = issue.message
                String issueSeverity = issue.severity
                String issueType = issue.type

                vulnerabilitiesTable += "<tr style='border-bottom:1px solid grey'>" +
                        "<td><b>${issueComponent}</b></td>" +
                        "<td>${issueLine}</td>" +
                        "<td>${issueMessage}</td>" +
                        "<td>${issueSeverity}</td>" +
                        "<td>${issueType}</td>" +
                        "</tr>"
            }
            
            def securityHotspotsTable = "<table style='text-align: center'><tr style='border-bottom:1px solid grey'>" +
                    "<th style='background-color: #5f249f; color: white'>Component</th>" +
                    "<th style='background-color: #5f249f; color: white'>Line</th>" +
                    "<th style='background-color: #5f249f; color: white'>Message</th>" +                
                    "</tr>"
            securityHotspotsResponse.hotspots[0].each { hotspots ->
                String hotspotsComponent = hotspots.component
                String hotspotsLine = (hotspots.line != null) ? hotspots.line : 'n/a'
                String hotspotsMessage = hotspots.message

                securityHotspotsTable += "<tr style='border-bottom:1px solid grey'>" +
                        "<td><b>${hotspotsComponent}</b></td>" +
                        "<td>${hotspotsLine}</td>" +
                        "<td>${hotspotsMessage}</td>" +              
                        "</tr>"
            }
            
            def overAllTable = "<table style='text-align: center'><tr style='border-bottom:1px solid grey'>" +
                    "<th style='background-color: #5f249f; color: white'>Component</th>" + 
                    "<th style='background-color: #5f249f; color: white'>Bugs</th>" +
                    "<th style='background-color: #5f249f; color: white'>Code smells</th>" +
                    "<th style='background-color: #5f249f; color: white'>Vulnerabilities</th>" +
                    "<th style='background-color: #5f249f; color: white'>Security hotspots</th>" +
                    "<th style='background-color: #5f249f; color: white'>Coverage</th>" +
                    "<th style='background-color: #5f249f; color: white'>Duplicated lines</th>" +
                    "</tr>"
            def componentName = overAllResponse.component.name
            def bugs = overAllResponse.component.measures.find { measure -> measure.metric == 'bugs' }?.value
            def codeSmells = overAllResponse.component.measures.find { measure -> measure.metric == 'code_smells' }?.value            
            def vulnerabilities = overAllResponse.component.measures.find { measure -> measure.metric == 'vulnerabilities' }?.value            
            def securityHotspots = overAllResponse.component.measures.find { measure -> measure.metric == 'security_hotspots' }?.value            
            def coverage = overAllResponse.component.measures.find { measure -> measure.metric == 'coverage' }?.value            
            def duplicatedLines = overAllResponse.component.measures.find { measure -> measure.metric == 'duplicated_lines_density' }?.value
            def securityHotspotsErrorThreshold = overAllResponse.metrics.find { metric -> metric.key == 'security_hotspots_reviewed'}?.bestValue
            def coverageErrorThreshold = responseProjectStatus?.conditions.find { conditions -> conditions.metricKey == 'new_coverage' }?.errorThreshold
            def duplicatedLinesErrorThreshold = responseProjectStatus?.conditions.find { conditions -> conditions.metricKey == 'new_duplicated_lines_density' }?.errorThreshold

            overAllTable += "<tr style='border-bottom:1px solid grey'>" +
                        "<td style='text-align: center'><b>${componentName}</b></td>" +
                        "<td style='text-align: center'>${bugs}</td>" +
                        "<td style='text-align: center'>${codeSmells}</td>" +
                        "<td style='text-align: center'>${vulnerabilities}</td>" +
                        "<td style='text-align: center'>${securityHotspots}" + 
                        ((securityHotspots < securityHotspotsErrorThreshold && projectStatus != 'PASSED') ? "<br /><i>is less than ${securityHotspotsErrorThreshold}%</i>" : '') + "</td>" +
                        "<td style='text-align: center'>${coverage}%" + 
                        ((coverage < coverageErrorThreshold && projectStatus != 'PASSED') ? "<br /><i>is less than ${coverageErrorThreshold}%</i>" : '') + "</td>" +
                        "<td style='text-align: center'>${duplicatedLines}%" + 
                        ((duplicatedLines > duplicatedLinesErrorThreshold && projectStatus != 'PASSED') ? "<br /><i>is greater than ${duplicatedLinesErrorThreshold}%</i>" : '') + "</td>" +
                        "</tr>"

            def sonarQubeReport = "sonar-report-${componentName}.html"
            
            context.writeFile file: "${sonarQubeReport}", text: "<html><head></head><body style='font-family: Open Sans'><h1>SonarQube Overall Status</h1>${statusTable}${overAllTable}</table><hr><h1>Bugs</h1>${bugsTable}</table><hr><h1>Code Smells</h1>${codeSmellsTable}</table><hr><h1>Vulnerabilities</h1>${vulnerabilitiesTable}</table><hr><h1>Security Hotspots</h1>${securityHotspotsTable}</table><hr></body></html>"                                                  
            context.archiveArtifacts artifacts: "${sonarQubeReport}"

        }
    }

    def getSonarIssues(def path, def sonarHost, def encodedToken, def type) {
        try {
            def combinedIssues = []
            int issuesPerPage = 500
            int actualPage = 1
            int totalPages = 1
            def status = "OPEN"
            def sortField = "SEVERITY"
            for(actualPage; actualPage<=totalPages; actualPage++) {
                def apiPath = "${path}&s=${sortField}&statuses=${status}&ps=${issuesPerPage}&p=${actualPage}&asc=false"
                def issues = context.httpRequest url: sonarHost + apiPath, validResponseCodes: '200:599',  customHeaders: [[name: 'Authorization', value: "Basic ${encodedToken}", maskValue: true]]
                def issuesJson = context.readJSON text: issues.content
                int totalIssues

                switch(type) {
                    case "hotspots":
                        (issuesJson.paging != null) ? (totalIssues = issuesJson.paging.total) : (totalIssues = 0)
                    break;
                    case "vulnerabilities":
                        (issuesJson.total != null) ? (totalIssues = issuesJson.total) : (totalIssues = 0)
                    break;
                    case "bugs":
                        (issuesJson.total != null) ? (totalIssues = issuesJson.total) : (totalIssues = 0)
                    break;
                    case "codeSmell":
                        (issuesJson.total != null) ? (totalIssues = issuesJson.total) : (totalIssues = 0)
                    break;
                    default:
                        totalIssues = 0
                }

                double resultDiv = totalIssues.div(issuesPerPage)
                double pages = totalPages

                combinedIssues.add(issuesJson)

                if (resultDiv > pages) {
                    totalPages++
                }
            }
            return combinedIssues
        } catch (e) {
            context.error("-- ⚠️ -- SonarQube report 🛸 creation FAILED ❗❗ Something went wrong during the analysis")
        }
    }

    void composeSonarReport(def sonarHost, def currentName, def encodedToken) {
        def keyword = 'ASR-'
        currentName = keyword + currentName

        def overallPath = "/api/measures/component?additionalFields=period%2Cmetrics&component=${currentName}&metricKeys=alert_status%2Cquality_gate_details%2Cbugs%2Cnew_bugs%2Creliability_rating%2Cnew_reliability_rating%2Cvulnerabilities%2Cnew_vulnerabilities%2Csecurity_rating%2Cnew_security_rating%2Csecurity_hotspots%2Cnew_security_hotspots%2Csecurity_hotspots_reviewed%2Cnew_security_hotspots_reviewed%2Csecurity_review_rating%2Cnew_security_review_rating%2Ccode_smells%2Cnew_code_smells%2Csqale_rating%2Cnew_maintainability_rating%2Csqale_index%2Cnew_technical_debt%2Ccoverage%2Cnew_coverage%2Clines_to_cover%2Cnew_lines_to_cover%2Ctests%2Cduplicated_lines_density%2Cnew_duplicated_lines_density%2Cduplicated_blocks%2Cncloc%2Cncloc_language_distribution%2Cprojects%2Clines%2Cnew_lines"
        def overAllResponse = context.httpRequest url: sonarHost + overallPath, validResponseCodes: '200:599',  customHeaders: [[name: 'Authorization', value: "Basic ${encodedToken}", maskValue: true]]                                                            
        def overAllResponseJson = context.readJSON text: overAllResponse.content

        def projectPath = "/api/qualitygates/project_status?projectKey=${currentName}"
        def projectResponse = context.httpRequest url: sonarHost + projectPath, validResponseCodes: '200:599',  customHeaders: [[name: 'Authorization', value: "Basic ${encodedToken}", maskValue: true]]                                                            
        def projectResponseJson = context.readJSON text: projectResponse.content

        def bugsPath = "/api/issues/search?types=BUG&componentKeys=${currentName}"
        def bugsResponse = getSonarIssues(bugsPath, sonarHost, encodedToken, 'bugs')

        def codeSmellPath = "/api/issues/search?types=CODE_SMELL&componentKeys=${currentName}"
        def codeSmellResponse = getSonarIssues(codeSmellPath, sonarHost, encodedToken, 'codeSmell')

        def vulnerabilitiesResponsePath = "/api/issues/search?types=VULNERABILITY&componentKeys=${currentName}"
        def vulnerabilitiesResponse = getSonarIssues(vulnerabilitiesResponsePath, sonarHost, encodedToken, 'vulnerabilities')

        def securityHotspotsPath = "/api/hotspots/search?projectKey=${currentName}"
        def securityHotspotsResponse = getSonarIssues(securityHotspotsPath, sonarHost, encodedToken, 'hotspots')

        try {
            sonarReport(securityHotspotsResponse, bugsResponse, overAllResponseJson, projectResponseJson, codeSmellResponse, vulnerabilitiesResponse)
        } catch (Exception e) {
            context.echo("-- ❌ -- 📂No Sonar Report was generated due to an error")
        }
    }

    def uploadGenericArtifact(String credentials, String repository, String path, String artifactName, String localFilePath = '', String url = '') {
        if (credentials == null || credentials == '' || repository == null || repository == '' || path == null
                || path == '' || artifactName == null || artifactName == '' || localFilePath == null) {            
            Utils.throwExceptionInstance('IllegalArgumentException', 'Missing required input parameters')
        }

        path = ValuesUtils.removeStartEndChars(path, '/', true, true)
        url = ValuesUtils.removeStartEndChars((url == '') ? DefaultConfiguration.PDXC_ARTIFACTORY_URL : url, '/', true, true)
        localFilePath = ValuesUtils.removeStartEndChars(localFilePath, '/', true, true)
        localFilePath = (localFilePath != null && localFilePath != '') ? localFilePath + '/' : ''

        def sha1 = context.sh(returnStdout: true, script: "sha1sum './${localFilePath}${artifactName}' | awk '{ print \$1 }'")
        sha1 = sha1.substring(0, 40)
        def sha256 = context.sh(returnStdout: true, script: "sha256sum './${localFilePath}${artifactName}' | awk '{ print \$1 }'")
        sha256 = sha256.substring(0, 64)

        context.withCredentials([context.usernamePassword(credentialsId: credentials, usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
            context.sh script: """
                        curl -f -s -u "${context.env.USERNAME}:${context.env.PASSWORD}" \
                                    -T ./${localFilePath}${artifactName} \
                                    -H "X-Checksum-Sha1:${sha1}" \
                                    -H "X-Checksum-Sha256:${sha256}" \
                                    ${url}/${repository}/${path}/${artifactName} \
                        """, label: "Upload artifact ${artifactName} through REST API call"
        }        
    }

    def checkov(String tfVersion) {
        def failed
        if ("${tfVersion}" == '0.12') {
            def skipPolicies = context.libraryResource "custom/deploy-project/skipPolicies.txt"

            try {
                context.sh """
                                cd code                                
                                checkov -d . --framework terraform -o junitxml --output-file-path . --download-external-modules False --skip-check ${skipPolicies} 
                            """
                            
                context.sh """
                                cd code 
                                checkov -d . --framework terraform -o json --output-file-path . --download-external-modules False --skip-check ${skipPolicies}                                
                            """
                failed = context.sh (
                                    script: """
                                                cat code/results_json.json | jq '.summary.failed'
                                            """,
                                    returnStdout: true)                
            }
            catch (Exception e) {
                failed = '1'
            }
        }
        else {
            failed = '1'
            context.echo("-- ⚠️ -- Checkov is not available for TF ${tfVersion} version")
        }

        context.junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: '**/*.xml')
        //DELETE TMP checkov_results.xml 
        context.sh """
                        rm -rf code/results_junitxml.xml
                        rm -rf code/results_json.json
                        rm -rf code/checkov_results.xml
                    """

        return failed
    }

    void prepareParamsTesting(String environment, LinkedHashMap data) {
        def credId = (PullPipelineUtils.credentialsAwsExist('ASSURE-AWS-CLI-RW') == true) ? 'ASSURE-AWS-CLI-RW' : 'DIAAS-AWS-CLI'
        
        context.withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: credId, accessKeyVariable: 'AWS_ACCESS_KEY_ID', secretKeyVariable: 'AWS_SECRET_ACCESS_KEY']]) {
            context.withCredentials([context.usernamePassword(credentialsId:'diaas-rw', passwordVariable:'ARTIF_PASSWORD', usernameVariable:'ARTIF_USER')]) {
                def setParamsFile = context.libraryResource(resource: "testScripts/setParams.sh")
                context.writeFile(file: "setParams.sh", text: setParamsFile)

                def arn = data."${environment}_environment_arn"
                def region = data."${environment}_environment_region"
                def environmentType = data."${environment}_environmentType"
                def customer = ValuesUtils.getVariable(data, 'customerName', environment)

                def UUID = UUID.randomUUID().toString()
                UUID = UUID.substring(0, UUID.indexOf('-'))
                
                context.sh script: """
                                    bash +x ./setParams.sh ${UUID} ${arn} ${region} ${environmentType} ${customer}
                                """,
                        returnStatus: true,
                        label: "-- ⚙️-- Set params"
            }
        }
    }

    boolean createGitHubRepository(String organization, String repoName, def configData, String visibility = 'public') {
        if (organization == null || organization == '' || repoName == null || repoName == '') {
            Utils.throwExceptionInstance('IllegalArgumentException', 'Missing required input parameters')
        }

        if (visibility == null || !(visibility in ['public', 'private', 'internal'])) {
            Utils.throwExceptionInstance('IllegalArgumentException', "Invalid value provided for visibility (${visibility}")
        }

        def gitHubCredential = ValuesUtils.getVariable(configData, 'gitHubCredential', 'setup')
        def apiURL = "${DefaultConfiguration.PDXC_GITHUB_URL}"
        GitApi gitApi = GitApi.getInstance(DefaultConfiguration.PDXC_GITHUB_URL, gitHubCredential)
        def body_parameters = "{\"name\":\"${repoName}\", \"visibility\":\"${visibility}\"}"
        def response = gitApi.post("${apiURL}/api/v3/orgs/${organization}/repos", body_parameters, 'POST', [[name: 'Accept', value: 'application/vnd.github.nebula-preview+json']], [201,422], 'APPLICATION_JSON', 'APPLICATION_JSON')

        boolean created
        if (response != null && response.id != null) {
            created = true
        } else if (response != null && response.errors != null &&
                   response.errors[0].message == 'name already exists on this account') {
            created = false
        } else {
            throw new Exception('Error creating the repository')
        }
        return created
    }

    def executeScript(String path = '', String scriptName = '', String params = '') {
        if (path == null || scriptName == null || scriptName == '') {
            Utils.throwExceptionInstance('IllegalArgumentException', 'Missing required input parameters')
        }

        String pathModified = ValuesUtils.removeStartEndChars(path, '/', true, false)
        if (pathModified != '' && pathModified[0] == '/') {
            Utils.throwExceptionInstance('IllegalArgumentException', "Invalid path provided: ${path}")
        }

        String execScript = "${scriptName} ${params}"
        context.sh script: """
                cd ./${pathModified}
                ${execScript}
            """, label: 'Execute script'
    }

    void createReleaseNotes (def configuration, def newVersion, def branchName) {
        context.echo "-- 📂 -- Generating Changelog in Github repository..."
        try {
            def cred = ValuesUtils.getVariable(configuration, 'gitHubCredential')
            def repoName = ValuesUtils.getVariable(configuration, 'repositoryName')
            def url = DefaultConfiguration.PDXC_GITHUB_URL

            GitApi gitApi = GitApi.getInstance(url, cred)

            def organization = ("${context.env.BUILD_URL}" =~ /(?:[job])(\/.*?\/)(?:[job])/)[0][1]
            organization = organization.replaceAll('/', '')

            def body_parameters = """{
                    "tag_name": "${newVersion}",
                    "name": "Release: ${newVersion}",
                    "target_commitish":"${branchName}",
                    "generate_release_notes": true
            }"""

            def callUrl = "${url}/api/v3/repos/${organization}/${repoName}/releases"
            def response = gitApi.post(callUrl, body_parameters, 'POST', [[name: 'Accept', value: 'application/vnd.github+json']], [201], 'APPLICATION_JSON', 'APPLICATION_JSON')

        } catch (Exception e) {
            context.echo "-- ❌ -- 📂No Changelog was generated due to an error"
        }
    }

    void javaSonarStage(String repoName, String scriptExecute = 'mvn clean install sonar:sonar', String dependenciesPath = '', String sonarSources = '', String sonarExclusions = '') {
        context.stage("SonarQube Scan: ${repoName}") {            
            context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                context.withCredentials([context.string(credentialsId:'ASSURE-SONAR-HOST', variable:'SONARHOST')]) {
                    context.withCredentials([context.string(credentialsId:'ASSURE-SONAR-TOKEN', variable:'SONARTOKEN')]) {

                        // context.echo "-- ❌ -- Sonar disabled due to Network connectivity has been interrupted to Plano D1 resources."
                        
                        def sonarHost = "${context.env.SONARHOST}"
                        def sonarToken = "${context.env.SONARTOKEN}"   
                        def sonarQualityGate = false
                        def sonarDescription = "assure"

                        def path = '/api/users/identity_providers'
                        def response = context.httpRequest url: sonarHost + path, validResponseCodes: '200:599'
                        
                        dependenciesPath = (dependenciesPath == null || dependenciesPath == '') ? '.' : dependenciesPath
                        def dependenciesPathModified = ValuesUtils.removeStartEndChars(dependenciesPath, '/', true, true)

                        if (response.status == 200) {
                            def sonarStatusCode = context.sh script: """
                                    cd ${dependenciesPathModified}
                                    ${scriptExecute} -Dsonar.login=${sonarToken} \
                                                    -Dsonar.host.url=${sonarHost} \
                                                    -Dsonar.projectKey=ASR-${repoName} \
                                                    -Dsonar.projectName=${repoName} \
                                                    -Dsonar.sources="${sonarSources}" \
                                                    -Dsonar.projectDescription="${sonarDescription}" \
                                                    -Dsonar.exclusions="${sonarExclusions}" \
                                                    -Dsonar.qualitygate.wait=true
                                    """ , 
                                    label: 'Sonar-scanner analysis', 
                                    returnStatus: true

                            def encodedToken = context.sh (
                                script: "echo -n '${context.env.SONARTOKEN}:' | base64", 
                                returnStdout: true).trim()
                            
                            context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                composeSonarReport(sonarHost, repoName, encodedToken)
                            }

                            if(sonarStatusCode != 0 && sonarQualityGate){
                                context.currentBuild.result = 'ABORTED'
                                context.error ("-- ❌ -- Pipeline ABORTED ❗❗ SonarQube Quality Gate Status: FAILED (sonarQualityGate is ${sonarQualityGate})")
                                return
                            }
                            context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                if(sonarStatusCode != 0){
                                    context.error("-- ⚠️ -- SonarQube Scan 🛸 quality gate status FAILED ❗❗ Please check 👀 Log for details")
                                    return
                                }
                            }
                        }
                        else {
                            context.error("-- ⚠️ -- SonarQube Scan 🛸 connection 🔌 FAILED ❗❗ Please check 👀 Log for details")
                        }
                    }
                }
            }
        }
    }

    def checkovErrorCheck (def repoName, def uuidCheckov) {
        def checkovTestsStatus = []
        try {
            List testValues = context.readFile("checkov-scan-results-${repoName}_${uuidCheckov}.log").split('scan results:')

            testValues.each { value -> 
                if (value.contains("By bridgecrew.io")) {
                    checkovTestsStatus.add('skip')
                } else if (value.contains("Failed checks: 0")) {
                    checkovTestsStatus.add('ok')
                } else if (value.contains("Failed checks:")){
                    checkovTestsStatus.add('fail')
                } else {
                    checkovTestsStatus.add('ok')
                }
            }

            if (checkovTestsStatus.contains('fail')) {
                return 1
            } else {
                return 0
            }


        } catch (Exception e) {
            context.echo("-- ⚠️ -- Something went wrong during the checkov log analysis")
        }
    }

    def pipelineInfoSteps(String pipelineName) {
        def conf = context.libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
        context.writeFile file: "${pipelineName}-conf.yml", text: conf
        def defaultConfigYml = context.readYaml file: "${pipelineName}-conf.yml"
        def repoData = context.readYaml file: 'conf.yml'

        def configData = defaultConfigYml + repoData
        configData = writeConfigData(configData, 'generic')

        context.println 'Loaded configuration values: \n\n' + JsonOutput.prettyPrint(JsonOutput.toJson(configData))

        context.writeYaml file: 'conf.yml', data: configData, overwrite: true

        return configData
    }

    void uploadArtifact(def configData, def artifactName) {
        try {
            def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
            def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'upload')
            if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
            def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'upload')
            def localPath = ValuesUtils.getVariable(configData, 'artifactLocalPath', 'upload')
            def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
            context.functiongroup_artifactory.uploadGenericArtifact(cred, repo, artifactPath,
                    artifactName + '.zip', localPath, artifactoryURL)
        } catch (Exception e) {
            context.error("--❌️-- Error detected during the upload process")
        }
    }

    def compressArtifact(def configData, def newVersion) {
        def artifactName
        def zipScript = ValuesUtils.getVariable(configData, 'zipScript', 'zip')
        def zipInclude = ValuesUtils.getVariable(configData, 'zipInclude', 'zip')
        if (zipInclude == null) zipInclude = ''
        def sourceFolder = ValuesUtils.getVariable(configData, 'zipSourceFolder', 'zip')
        artifactName = ValuesUtils.getVariable(configData, 'targetZipName', 'zip') + ".${newVersion}"
        //Delete if file already exist
        context.sh(script: "rm -rf ${artifactName}.zip", label: 'Delete old version of zip file')
        if (zipScript != "" && zipScript != null) {
            context.sh(script: "${zipScript} ${artifactName}.zip ${sourceFolder} ${zipInclude}", label: 'Zip file using custom script')
        } else {
            context.zip(glob: "${zipInclude}", zipFile: "${artifactName}.zip", dir: "${sourceFolder}")
        }
        return artifactName
    }

    void updateArtifactProps(def configData, def  artifactName) {
        def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'update')
        if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
        def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'update')
        ArtifactoryApi artfApi = new ArtifactoryApi(artifactoryURL, cred)

        def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'update')
        def props = ValuesUtils.getVariableArrayList(configData, 'artifactProperties', 'update')

        def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'update')
        artfApi.updateArtifactProperties(repo, artifactPath, artifactName + '.zip', props)
    }

    def sonarScan(def SONARHOST, def SONARTOKEN, def configData, boolean usesNpm, boolean alpineImage= true, def dockerName='') {
        def sonarStatusCode = 0
        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {            
            // context.echo "-- ❌ -- Sonar disabled due to Network connectivity has been interrupted to Plano D1 resources."
            def sonarHost = (ValuesUtils.getVariable(configData, 'sonarHost', 'sonar') != null) ? ValuesUtils.getVariable(configData, 'sonarHost', 'sonar') : "${SONARHOST}"
            def sonarToken = (ValuesUtils.getVariable(configData, 'sonarToken', 'sonar') != null) ? ValuesUtils.getVariable(configData, 'sonarToken', 'sonar') : "${SONARTOKEN}"
            def sonarSources= ValuesUtils.getVariable(configData, 'sonarSources', 'sonar')
            def sonarExclusions= ValuesUtils.getVariable(configData, 'sonarExclusions', 'sonar')
            def sonarQualityGate = ValuesUtils.getVariable(configData, 'sonarQualityGate', 'sonar')
            def sonarCoverage = (ValuesUtils.getVariable(configData, 'sonarCoverage', 'sonar') != null) ? ValuesUtils.getVariable(configData, 'sonarCoverage', 'sonar') : "sonar.coverageReportPaths"
            def sonarCoveragePath = (ValuesUtils.getVariable(configData, 'sonarCoveragePath', 'sonar') != null) ? ValuesUtils.getVariable(configData, 'sonarCoveragePath', 'sonar') : ""

            def path = '/api/users/identity_providers'
            def response = context.httpRequest url: sonarHost + path, validResponseCodes: '200:599'

            def dependenciesPath = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'sonar')
            dependenciesPath = (dependenciesPath == null || dependenciesPath == '') ? '.' : dependenciesPath
            def dependenciesPathModified = ValuesUtils.removeStartEndChars(dependenciesPath, '/', true, true)

            if (response.status == 200) {
                def currentName
                def sonarScript

                if (alpineImage != true) {
                    sonarScript = 'install-sonar-scanner-generic.sh'
                } else {
                    sonarScript = 'install-sonar-scanner.sh'
                }

                def sonnarInstalation = context.libraryResource(resource: "sonar/${sonarScript}")
                context.writeFile(file: "${sonarScript}", text: sonnarInstalation)

                def sonarBasicConf = context.libraryResource(resource:"sonar/sonar-conf.yml")
                context.writeFile(file: "sonar-conf.yml", text: sonarBasicConf)

                def sonarConf = context.readYaml file: 'sonar-conf.yml'
                def sonarDescription = "assure"

                if (usesNpm != true) {
                    currentName = configData.repositoryName                
                } else {
                    def file = ValuesUtils.getVariable(configData, 'dependenciesPackageFile', 'setup')
                    def attr = "name"
                    def dependenciesJsonPath = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'setup')
                    currentName = FileUtils.getAttributeFromJsonFile(file, attr, dependenciesJsonPath)
                    currentName = currentName.replaceAll('\\.', '')
                }

                def sonarJavaVersion = sonarConf.sonarJavaVersion

                if (configData.sonarJavaVersion != null && configData.sonarJavaVersion != '') {
                    sonarJavaVersion = configData.sonarJavaVersion
                }

                context.sh script: """
                        bash ${sonarScript} ${sonarConf.sonarScannerVersion} ${sonarJavaVersion}
                        rm -rf ${sonarScript}
                        """, label: "Test sonnar scanner instalation"

                sonarStatusCode = context.sh script: """
                        cd ${dependenciesPathModified}
                        sonar-scanner -Dsonar.host.url=${sonarHost} \
                                        -Dsonar.projectKey=ASR-${currentName} \
                                        -Dsonar.login=${sonarToken} \
                                        -Dsonar.sources="${sonarSources}" \
                                        -Dsonar.projectDescription="${sonarDescription}" \
                                        -Dsonar.exclusions="${sonarExclusions}, ${dockerName}" \
                                        -D${sonarCoverage}=${sonarCoveragePath} \
                                        -Dsonar.qualitygate.wait=true
                        """ , 
                        label: 'Sonar-scanner analysis', 
                        returnStatus: true

                def encodedToken = context.sh (
                    script: "echo -n '${SONARTOKEN}:' | base64", 
                    returnStdout: true).trim()

                context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                    composeSonarReport(sonarHost, currentName, encodedToken)
                }

                if(sonarStatusCode != 0 && sonarQualityGate){
                    currentBuild.result = 'ABORTED'
                    error ("-- ❌ -- Pipeline ABORTED ❗❗ SonarQube Quality Gate Status: FAILED (sonarQualityGate is ${sonarQualityGate})")
                    return
                }
                context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                    if(sonarStatusCode != 0){
                        context.error("-- ⚠️ -- SonarQube Scan 🛸 quality gate status FAILED ❗❗ Please check 👀 Log for details")
                        return
                    }
                }
            }
            else {
                context.error("-- ⚠️ -- SonarQube Scan 🛸 connection 🔌 FAILED ❗❗ Please check 👀 Log for details")
            }
        }
        return sonarStatusCode   
    }

    def sonarJavaScan(def SONARHOST, def SONARTOKEN, def configData, boolean usesNpm, boolean alpineImage= true, def dockerName='') {
        def sonarStatusCode = 0
        context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
            // context.echo "-- ❌ -- Sonar disabled due to Network connectivity has been interrupted to Plano D1 resources."
            def sonarHost = (ValuesUtils.getVariable(configData, 'sonarHost', 'sonar') != null) ? ValuesUtils.getVariable(configData, 'sonarHost', 'sonar') : "${SONARHOST}"
            def sonarToken = (ValuesUtils.getVariable(configData, 'sonarToken', 'sonar') != null) ? ValuesUtils.getVariable(configData, 'sonarToken', 'sonar') : "${SONARTOKEN}"
            def sonarSources= ValuesUtils.getVariable(configData, 'sonarSources', 'sonar')
            def sonarBinaries= ValuesUtils.getVariable(configData, 'sonarBinaries', 'sonar')
            def sonarExclusions= ValuesUtils.getVariable(configData, 'sonarExclusions', 'sonar')
            def sonarQualityGate = ValuesUtils.getVariable(configData, 'sonarQualityGate', 'sonar')
            def sonarCoverage = (ValuesUtils.getVariable(configData, 'sonarCoverage', 'sonar') != null) ? ValuesUtils.getVariable(configData, 'sonarCoverage', 'sonar') : "sonar.coverageReportPaths"
            def sonarCoveragePath = (ValuesUtils.getVariable(configData, 'sonarCoveragePath', 'sonar') != null) ? ValuesUtils.getVariable(configData, 'sonarCoveragePath', 'sonar') : ""

            def path = '/api/users/identity_providers'
            def response = context.httpRequest url: sonarHost + path, validResponseCodes: '200:599'

            def dependenciesPath = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'sonar')
            dependenciesPath = (dependenciesPath == null || dependenciesPath == '') ? '.' : dependenciesPath
            def dependenciesPathModified = ValuesUtils.removeStartEndChars(dependenciesPath, '/', true, true)

            if (response.status == 200) {
                def currentName
                def sonarScript

                if (alpineImage != true) {
                    sonarScript = 'install-sonar-scanner-generic.sh'
                } else {
                    sonarScript = 'install-sonar-scanner.sh'
                }

                def sonnarInstalation = context.libraryResource(resource: "sonar/${sonarScript}")
                context.writeFile(file: "${sonarScript}", text: sonnarInstalation)

                def sonarBasicConf = context.libraryResource(resource:"sonar/sonar-conf.yml")
                context.writeFile(file: "sonar-conf.yml", text: sonarBasicConf)

                def sonarConf = context.readYaml file: 'sonar-conf.yml'
                def sonarDescription = "assure"

                if (usesNpm != true) {
                    currentName = configData.repositoryName                
                } else {
                    def file = ValuesUtils.getVariable(configData, 'dependenciesPackageFile', 'setup')
                    def attr = "name"
                    def dependenciesJsonPath = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'setup')
                    currentName = FileUtils.getAttributeFromJsonFile(file, attr, dependenciesJsonPath)
                    currentName = currentName.replaceAll('\\.', '')
                }

                context.sh script: """
                        bash ${sonarScript} ${sonarConf.sonarScannerVersion} ${sonarConf.sonarJavaVersion}
                        rm -rf ${sonarScript}
                        """, label: "Test sonnar scanner instalation"

                sonarStatusCode = context.sh script: """
                        cd ${dependenciesPathModified}
                        sonar-scanner -Dsonar.host.url=${sonarHost} \
                                        -Dsonar.projectKey=ASR-${currentName} \
                                        -Dsonar.login=${sonarToken} \
                                        -Dsonar.java.sources="${sonarSources}" \
                                        -Dsonar.java.binaries="${sonarBinaries}" \
                                        -Dsonar.exclusions="${sonarExclusions}, ${dockerName}" \
                                        -Dsonar.projectDescription="${sonarDescription}" \
                                        -D${sonarCoverage}=${sonarCoveragePath} \
                                        -Dsonar.qualitygate.wait=true
                        """ , 
                        label: 'Sonar-scanner analysis', 
                        returnStatus: true

                def encodedToken = context.sh (
                    script: "echo -n '${SONARTOKEN}:' | base64", 
                    returnStdout: true).trim()

                context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                    composeSonarReport(sonarHost, currentName, encodedToken)
                }

                if(sonarStatusCode != 0 && sonarQualityGate){
                    currentBuild.result = 'ABORTED'
                    error ("-- ❌ -- Pipeline ABORTED ❗❗ SonarQube Quality Gate Status: FAILED (sonarQualityGate is ${sonarQualityGate})")
                    return
                }
                context.catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                    if(sonarStatusCode != 0){
                        context.error("-- ⚠️ -- SonarQube Scan 🛸 quality gate status FAILED ❗❗ Please check 👀 Log for details")
                        return
                    }
                }
            }
            else {
                context.error("-- ⚠️ -- SonarQube Scan 🛸 connection 🔌 FAILED ❗❗ Please check 👀 Log for details")
            }
        }
        return sonarStatusCode   
    }

    void markGitHubCheckNeutral (def configuration, def checkName) {        
        try {
            def cred = ValuesUtils.getVariable(configuration, 'gitHubCredential')
            def repoName = ValuesUtils.getVariable(configuration, 'repositoryName')
            def url = DefaultConfiguration.PDXC_GITHUB_URL

            GitApi gitApi = GitApi.getInstance(url, cred)

            def organization = ("${context.env.BUILD_URL}" =~ /(?:[job])(\/.*?\/)(?:[job])/)[0][1]
            organization = organization.replaceAll('/', '')

            def response = gitApi.get(url+"/api/v3/repos/${organization}/${repoName}/commits/${context.GIT_COMMIT}/check-runs", [200, 409])
            def checkRunID = response.check_runs.find { search -> search.name == "${checkName}" }.id
            def checkRunOutput = response.check_runs.find { search -> search.name == "${checkName}" }.output
            
            def credId = "githubapp-${organization}"
            
            context.withCredentials([context.usernamePassword(credentialsId: credId, usernameVariable: 'GITHUB_APP', passwordVariable: 'GITHUB_JWT_TOKEN')]) {
                RestApi api = RestApi.getInstance("${DefaultConfiguration.PDXC_GITHUB_URL}/api/v3/app/installations")
                def headers = [[name: 'Accept', value: "application/vnd.github+json"], [name: 'Authorization', value: "Bearer ${context.env.GITHUB_JWT_TOKEN}"]]

                def body = """{                    
                    "conclusion":"neutral",
                    "output":{"title":\"${checkRunOutput.title}\","summary":\"${checkRunOutput.summary}\","text": ${JsonOutput.toJson(checkRunOutput.text)}}
                }"""                

                api.post("${DefaultConfiguration.PDXC_GITHUB_URL}/api/v3/repos/${organization}/${repoName}/check-runs/${checkRunID}", body, 'PATCH', headers, [200],'APPLICATION_JSON', 'NOT_SET')
            }

        } catch (Exception e) {
            context.echo "-- ❌ -- GitHub check could not be marked as neutral due to an error"
        }
    }

    void checkVersionFormat() {
        // The content of the version file should be: { "version": "x.x.x" }
        context.echo "-- 📂 -- Checking version file..."

        def versionRegex = '^(\\d+\\.\\d+\\.\\d+)$'
        def result = null

        // Check if the version file exists
        def versionFilePath = "./version.json"
        def exists = context.fileExists versionFilePath
        if (!exists) {
            context.error("-- ⚠️ -- Version file not found. Please check your configuration.")
        }

        def version = context.readFile versionFilePath
        def versionValue = (new JsonSlurper().parseText(version)).version

        // Check if the version file format is correct
        try {
            def matcher = (versionValue =~ /${versionRegex}/)
            if (matcher != null && matcher[0] != null) {
                if (matcher[0] instanceof String) {
                    result = matcher[0]
                } else {
                    result = matcher[0][0]
                }
            }
        } catch (Exception e) {
            context.error("-- ❌ -- Version file format is incorrect. Please check your configuration. The version.json file should contain a version in the format: { \"version\": \"x.x.x\" }")
        }
        
        // If the version file format is correct, print the version
        context.echo "-- ✅ -- Version file format is correct. Version: ${result}"

    }
}
