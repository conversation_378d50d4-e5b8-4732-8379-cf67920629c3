resource "aws_sagemaker_model" "sagemaker_model" {
  # name               = var.sagemaker_model_name
  name = "${local.environment_specific_resource_pre_name}-${var.sagemaker_model_name}"
  execution_role_arn = aws_iam_role.sagemaker_iam_role.arn

  primary_container {
    # image = data.aws_sagemaker_prebuilt_ecr_image.sagemaker_prebuilt_ecr_image.registry_path
    image="${var.aws_account_id}.dkr.ecr.${var.aws_region}.amazonaws.com/${var.sagemaker_prebuilt_ecr_image_repository_name}:${var.sagemaker_prebuilt_ecr_image_tag}"
    model_data_url = "s3://${module.pca_bucket.bucket_name}/${var.pca_service_name}/${var.pca_model}.tar.gz"
    
    environment = {
      SAGEMAKER_REGION            = var.sagemaker_image_aws_region
      SAGEMAKER_PROGRAM           = "${var.sagemaker_program}.py"
      SAGEMAKER_SUBMIT_DIRECTORY  = "s3://${module.pca_bucket.bucket_name}/${var.pca_service_name}/${var.pca_model}.tar.gz"
      SAGEMAKER_CONTAINER_LOG_LEVEL = var.sagemaker_container_log_level
    }
  }

  vpc_config {
    subnets = split(",",data.aws_ssm_parameter.environment_app_subnets.value)
    security_group_ids = [ aws_security_group.sagemaker.id ]
  }
  depends_on = [
    aws_security_group.sagemaker
  ]
}

resource "aws_security_group" "sagemaker" {
  description = "Only allow outbound traffic from Sagemake"
  vpc_id      = data.aws_ssm_parameter.vpc_id.value
 
  revoke_rules_on_delete = true
  ingress {
    #
    # Should probably be reduced because external access should only be to valid exterior addresses
    # or interior targets such as the ALB or NLB.
    #
    from_port   = "0"
    to_port     = "65535"
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Permit inbound access to anywhere"
  }
 
  egress {
    #
    # Should probably be reduced because external access should only be to valid exterior addresses
    # or interior targets such as the ALB or NLB.
    #
    from_port   = "0"
    to_port     = "65535"
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Permit outbound access to anywhere"
  }
  tags = merge(tomap({"Name" = "sagemaker-sg"}), local.resource_tags)
 
  lifecycle {
    create_before_destroy = true
  }
 
}

resource "aws_iam_role" "sagemaker_iam_role" {
  name = "${local.environment_specific_resource_pre_name}-sagemaker_iam_role"
  assume_role_policy = data.template_file.sagemaker_assume_role_policy_template.rendered
}


resource "aws_iam_policy" "amazon_bedrock_iam_policy" {
  name        = "${local.environment_specific_resource_pre_name}-amazon_bedrock_iam_policy"
  policy      = data.template_file.amazon_bedrock_policy_template.rendered
}

resource "aws_iam_role_policy_attachment" "amazon_bedrock_iam_role_policy_attachment" {
  role       = aws_iam_role.sagemaker_iam_role.name
  policy_arn = aws_iam_policy.amazon_bedrock_iam_policy.arn
}

resource "aws_iam_role_policy_attachment" "amazon_sagemaker_full_access_iam_role_policy_attachment" {
  role       = aws_iam_role.sagemaker_iam_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSageMakerFullAccess"
}

resource "aws_iam_role_policy_attachment" "amazon_s3_full_access_iam_role_policy_attachment" {
  role       = aws_iam_role.sagemaker_iam_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

module "kms_key_arn" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-fetch-global-parameter/code"
 
  client_name      = local.client_short_name
  environment_name = local.environment_name
  target_service   = "kms"
  key_name         = "KEY_ARN"
  using_service    = "sagemaker"
}

output "kms_key_arn" {
  value = module.kms_key_arn.ssm_global_parameter_value
}
resource "aws_sagemaker_endpoint_configuration" "sagemaker_endpoint_configuration" {
  name = "${local.environment_specific_resource_pre_name}-${var.sagemaker_endpoint_configuration_name}"
  # kms_key_arn = module.kms_key_arn.ssm_global_parameter_value == "" ? null : module.kms_key_arn.ssm_global_parameter_value
  production_variants {
    variant_name           = var.sagemaker_endpoint_configuration_production_variant_name
    model_name             = aws_sagemaker_model.sagemaker_model.name
    initial_instance_count = var.sagemaker_endpoint_configuration_production_variant_initial_instance_count
    instance_type          = var.sagemaker_endpoint_configuration_production_variant_instance_type
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(
            tomap(
              {
                Name        = "${var.sagemaker_endpoint_configuration_name}"
                Environment = "${local.environment_name}"
                ManagedBy   = "terraform"
              }
            ),
            local.resource_tags
          )
}

resource "aws_sagemaker_endpoint" "sagemaker_endpoint" {
  name                 = "${local.environment_specific_resource_pre_name}-${var.sagemaker_endpoint_name}"
  endpoint_config_name = aws_sagemaker_endpoint_configuration.sagemaker_endpoint_configuration.name
  
  lifecycle {
    create_before_destroy = true
  }

  depends_on = [
    aws_sagemaker_model.sagemaker_model,
    aws_sagemaker_endpoint_configuration.sagemaker_endpoint_configuration
  ]
  tags =  merge(
            tomap(
              {
                Name = "${var.sagemaker_endpoint_name}"
                Environment = "${local.environment_name}"
                ManagedBy   = "terraform"
              }
            ),
            local.resource_tags
          )
}

resource "aws_cloudwatch_metric_alarm" "endpoint_invocation_errors" {
  alarm_name          = "${local.environment_specific_resource_pre_name}-endpoint-errors"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name        = "Invocation4XXErrors"
  namespace          = "AWS/SageMaker"
  period             = "300"
  statistic          = "Sum"
  threshold          = "10"
  alarm_description  = "This metric monitors endpoint 4XX errors"
  
  dimensions = {
    EndpointName = aws_sagemaker_endpoint.sagemaker_endpoint.name
  }
}
