#!/usr/bin/env groovy

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.pdxc.rest.GitApi
import org.assure.envservice.EnvServiceApi
import org.assure.util.AssureUtils
import org.assure.pullpipeline.PullPipelineUtils
import org.assure.pullpipeline.Artifactory
import groovy.json.JsonOutput
import org.assure.util.EmailUtils

/**
 * Pipeline creation and execution.
 *
 * @param stagesMap Specific data for each stage.
 * @param dockerPath Full path and name of a dockerFile to be used on the pipeline. If not provided, default is used.
 * @return void
 */
def call() {
    Map configuration = [:]
    Artifactory artifactory
    EnvServiceApi envService
    def foundArts = []
    def finalEnvironmentsMap = [:]

    def overallStatus = true
    def deployedData = []

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            timeout(time: 24, unit: 'HOURS')
        }

        stages {
            stage('🎛️ Set up and init') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root --network=host -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {                    
                        JenkinsContext.setContext(this)
                        
                        AssureUtils.echoBanner(["Set up and init", "Loading required information"])
                        
                        // error("-- ❌ -- Pipeline disabled")

                        Map configurationNew = readYaml file: 'conf.yml'
                        
                        println 'Loaded confRepository values: \n\n' + JsonOutput.prettyPrint(JsonOutput.toJson(configurationNew))

                        // Validate pipeline
                        def repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configurationNew.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo "Configured repository name matches current repository: ${repoName}"

                        def defaultConfigYmlFile = libraryResource "custom/promotion-project/promotion-conf.yml"
                        writeFile file: "promotion-conf.yml", text: defaultConfigYmlFile

                        Map defaultConfigYml = readYaml file: 'promotion-conf.yml'
                        
                        configurationNew = defaultConfigYml + configurationNew                        
                        
                        configurationNew = AssureUtils.flattenHierarchyYml(configurationNew)

                        try {
                            withCredentials([file(credentialsId: 'promotion_config', variable: 'CONFIG')]) {
                                sh "cat ${env.CONFIG} > promotion-config.yml"
                                def promotionConfigYml = readYaml file: 'promotion-config.yml'
                                // echo "promotionConfigYml ${promotionConfigYml}"
                                configurationNew = configurationNew + promotionConfigYml
                            }
                        } catch (Exception e) {
                            error 'Please, check the required credentials stored in Jenkins'
                        }

                        def envServFile = libraryResource(resource: "${configurationNew.env_service_conf_file}.gpg", encoding: "Base64")
                        writeFile(file: "${configurationNew.env_service_conf_file}.gpg", text: envServFile, encoding: "Base64")

                        Map envServData = AssureUtils.decryptYmlFile(configurationNew.passphrase_id, configurationNew.env_service_conf_file, '.')

                        def resultMap = [:]
                        resultMap.putAll(envServData)
                        resultMap.putAll(configurationNew)
                        
                        resultMap.each { key, value ->
                            if( !(envServData[key] && configurationNew[key]))
                            {
                                configuration.put(key, value)
                            }
                            else {
                                configuration.remove(key)
                                configuration.put(key, configurationNew.get(key))
                            }
                        }
                        
                        // Configure Git global data
                        def cred = ValuesUtils.getVariable(configuration, 'gitHubCredential', 'setup')
                        def mail = ValuesUtils.getVariable(configuration, 'gitEmail', 'setup')
                        def user = ValuesUtils.getVariable(configuration, 'gitUsername', 'setup')
                        def url = ValuesUtils.getVariable(configuration, 'gitHubUrl', 'setup')
                        functiongroup_git.setup(cred, mail, user, url)

                        artifactory = new Artifactory(ValuesUtils.getVariable(configuration, "artifactory_url"), ValuesUtils.getVariable(configuration, "artifactory_credential"))                    
                        envService = new EnvServiceApi(configuration.environment_service_url, configuration.environment_service_credential, configuration.oauth_host, configuration.env_api_key)
                        envService.setToken()                    
                    }
                }
            }
            stage('🐸 Get artifacts data (AQL)') {
                steps {
                    script {
                        AssureUtils.echoBanner(["Get artifacts data (AQL)", "Getting artifacts from Artifactory"])
                        
                        def allArtifacts = artifactory.manageAQLQuery(configuration)                    
                        AssureUtils.printPrettyMessage(['Artifacts': allArtifacts], '-- 🐸 -- Print full list of artifacts retrieved from Artifactory')

                        def notFoundArts = []
                        (foundArts, notFoundArts) = PullPipelineUtils.findArtefacts(allArtifacts, configuration)                                  

                        AssureUtils.printPrettyMessage(['Artifacts': foundArts], '-- 🐸📦 -- Print full list of valid artifacts') 
                        if (notFoundArts != null && notFoundArts.size()>0) AssureUtils.printPrettyMessage(['Artifacts discarded': notFoundArts], '-- 🐸🚮 -- Print list of discarded artifacts')                    
                    }
                }
            }
            stage('🔀 Manage Target Environments') {
                when { expression { return (foundArts != null && foundArts.size() > 0) } }
                steps {
                    script {
                    AssureUtils.echoBanner(["Manage Target Environments", "Blocking sdlc environments", "Blocking artefacts"])
                       
                        // artefacts group by environment
                        def environmentsMap = foundArts.collect().findAll { artifact -> (artifact?.targetEnv) }.groupBy { artifact -> artifact.targetEnv }

                        environmentsMap.each { env ->
                            try {                                                  
                                
                                echo "-- ℹ️ -- Environment ${env.key} is available. Marking it as busy and blocking its artifacts"
                                def urlPath = envService.composeURLPath(configuration, env.key, true)
                                def envData = envService.getWithCustomAuth(configuration.environment_service_url + '/' + urlPath + '?embedded=[deployments.item]&type=service')
                                
                                if (envService.isAvailable(env.key, configuration, envData)) {

                                    def filteredList = PullPipelineUtils.filterAvailableDeploymentPackages(env, envData)

                                    echo "-- ℹ️ -- filteredList ${filteredList}"

                                    def list
                                    try {
                                        if (filteredList != null && filteredList != []) {
                                            finalEnvironmentsMap.put(env.key, filteredList)
                                            // blocking env
                                            // TMP - disabled block environment
                                            // envService.setEnvironmentStatus(urlPath, true)
                                            // blocking artefacts
                                            list = artifactory.blockArtifacts(filteredList, true)
                                            if (list == null || list == []) {                                        
                                                Utils.throwExceptionInstance("Exception", "-- ❌ -- No artifact could be blocked.")
                                            }
                                        }
                                    } catch (Exception e) {
                                        envService.setEnvironmentStatus(urlPath, false)
                                        list = artifactory.blockArtifacts(foundArts, false)
                                        finalEnvironmentsMap.remove(env.key)
                                        Utils.throwExceptionInstance('Exception', "${e.message}")
                                    }
                                } else {
                                    finalEnvironmentsMap.remove(env.key)
                                    echo "-- ⚠️🚮 -- Environment ${env.key} is busy or locked. Its target artifacts will not be processed"
                                }
                            } catch (Exception e) {
                                echo "-- ❌ -- Error during environment (${env.key}) or artifacts blockage. Error: ${e}"
                            }
                        }

                        AssureUtils.printPrettyMessage(['Envs': finalEnvironmentsMap], '-- 🐸📦 -- Print full list of valid artifacts grouped by env')
                    }
                }
            }
            stage('🚀 Deploy and Tests') {
                when { expression { return (finalEnvironmentsMap != null && finalEnvironmentsMap.size() > 0) } }
                steps {
                    catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
                        script {
                            AssureUtils.echoBanner(["Deploy and Test", "Deploy", "Test"])

                            ArrayList allEnvs = ValuesUtils.getVariableArrayList(configuration, 'stages')
                            def parallelSteps = [:]
                            allEnvs.each { env ->
                                parallelSteps[env] = {
                                    try {             
                                        def deployment = PullPipelineUtils.runDeploymentStage(env, finalEnvironmentsMap.get(env), configuration)                                   
                                        deployedData.addAll(deployment.deployedArtifacts)
                                        if (deployment.deployedArtifacts.size() > 0) {
                                            def operationResult = deployment.deployedArtifacts.findAll { entry -> entry.testresult[0] == false }
                                            if (operationResult != []) {
                                                echo "-- ❌ -- Error during deployment / testing to stage ${env}"
                                                overallStatus = false
                                            }         
                                        }                                    
                                    } catch (Exception e) {
                                        overallStatus = false
                                        error("-- ❌ -- Error during deployment to ${env}. Please check logs. Error: ${e}")
                                    }
                                }
                            }
                            parallel parallelSteps
                        }
                    }
                }
            }
            stage('🏁 Closure and Summary') {
                steps {
                    script {
                        AssureUtils.echoBanner(["Process status of any missing artifact and deployment", "1", "2"])
    
                        def multiple = deployedData.findAll { entry -> entry.multiple[0] == true }
    
                        multiple.groupBy({ val -> val.artifact }).each { entry ->
                            def artifactStatus
                            def testStatus
                            def referenceEntry = entry.value.find({ search -> (search.deployresult[0] == false || search.testresult[0] == false) })
                            
                            referenceEntry = (referenceEntry != null) ? referenceEntry : entry.value.find({ search2 ->(search2.deployresult[0] == true && search2.testresult[0] == true) })
                            
                            if (referenceEntry != null) {
                                referenceEntry.each{ item ->
                                    artifactStatus = item.artifactStatus
                                    testStatus = item.testStatus
                                    def art = [path: item.path, repo: item.repo, name: item.name]
                                    // update artifact status (1 operation only)
                                    artifactory.deployArtifactsCompleted([art], artifactStatus)
                                    envService.setTestResults(item.location, testStatus)
                                }
                            }
                        }

                        if (!overallStatus) {
                            currentBuild.result = 'UNSTABLE'
                        }
                    }  
                }
            }
            stage('✉️ Pipeline Notifications') {
                steps {
                    script {
                        echo '-- ✉️ -- Preparing notifications'
                        if (configuration.promotionNotifications != false) {
                            def releasesJson = null
                            def blueOceanUrl = AssureUtils.getBlueOceanUri(env)

                            def exists = fileExists("new_releases.json")
                            if (exists) releasesJson = readJSON file: "new_releases.json"

                            (devOrTestArts, stageOrProdArts, successArts) = PullPipelineUtils.prepareDataForNotifications(deployedData, blueOceanUrl, releasesJson, configuration)
                        
                            if (devOrTestArts.size()>0) PullPipelineUtils.manageNotifications(devOrTestArts, releasesJson, configuration)
                            if (stageOrProdArts.size()>0) PullPipelineUtils.manageNotifications(stageOrProdArts, releasesJson, configuration)
                            if (successArts.size()>0) PullPipelineUtils.manageNotifications(successArts, releasesJson, configuration)
                        } else {
                            echo '-- ✉️ -- Notifications disabled'
                        }
                    }
                }
            }
        }
        post {
            always {
                script {
                    String warnMessage

                    def warnings = libraryResource "warnings/warnings.yml"
                    writeFile file: "warnings.yml", text: warnings
                    def defaultConfigYml = readYaml file: "warnings.yml"

                    List pipelineWarns = ValuesUtils.getVariableArrayList(defaultConfigYml, 'pipelineWarns')
                    sh(script: "rm -rf warnings.yml", label:"Remove unnecesary file")

                    pipelineWarns.each { warn ->
                        if (warn.pipelineName == 'promotion') {
                            warnMessage = warn.message
                        }
                    }

                    if (currentBuild.result == 'SUCCESS') {
                        currentBuild.displayName = currentBuild.displayName.replace("#", "") + ' - 💃 Way to go!'
                    } else {
                        currentBuild.displayName = currentBuild.displayName.replace("#", "") + ' - ☠️ Something went wrong'
                    }

                    if (warnMessage != '') {
                        currentBuild.description =  "🤖 Automation team warning: ${warnMessage}"
                    }
                    
                    echo '--🧹️-- Clean workspace'
                    sh 'cd /'
                    deleteDir()
                }
            }
        }
    }
}
