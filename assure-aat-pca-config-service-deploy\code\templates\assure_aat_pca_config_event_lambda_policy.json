{"Version": "2012-10-17", "Statement": [{"Sid": "DynamoDBAccessToTrainingStatusTable", "Effect": "Allow", "Action": ["dynamodb:*", "logs:CreateLogGroup"], "Resource": ["${training_status_table_arn}", "${training_status_table_arn}/index/*"]}, {"Sid": "DynamoDBAccessToReportsTable", "Effect": "Allow", "Action": ["dynamodb:Query", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:PutItem", "dynamodb:UpdateItem", "dynamodb:BatchWriteItem"], "Resource": ["${reports_table_arn}", "${reports_table_arn}/index/*"]}, {"Sid": "BucketAccess", "Effect": "Allow", "Action": ["s3:ListBucket", "s3:DeleteObject", "s3:DeleteObjectVersion", "s3:ListBucketVersions", "s3:GetObjectVersion"], "Resource": ["${s3_bucket_arn}", "${s3_bucket_arn}/*"]}, {"Sid": "LambdaAccess", "Effect": "Allow", "Action": ["lambda:InvokeFunction"], "Resource": ["${pca_preprocessing_lambda_arn}"]}]}