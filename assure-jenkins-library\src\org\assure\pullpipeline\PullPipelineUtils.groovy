/* groovylint-disable ThrowException */
package org.assure.pullpipeline

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.exception.ContextNotSetException
import org.pdxc.util.Utils
import org.pdxc.util.ValuesUtils
import org.pdxc.notification.MailNotification
import org.assure.artifactory.ArtifactoryWrapper
import org.assure.util.AssureUtils
import org.assure.envservice.EnvServiceApi
import org.jenkinsci.plugins.workflow.libs.Library
import com.cloudbees.groovy.cps.NonCPS
import org.pdxc.util.DefaultConfiguration
import groovy.json.JsonOutput
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.rest.RestApi
import org.assure.pullpipeline.Artifactory
import org.assure.util.EmailUtils
import org.assure.util.MsTeamsUtils


/**
* Utility class
*/
class PullPipelineUtils {
 
    private PullPipelineUtils() { }

    /**
     * Create a representation of an artifact that we will use during the custom deploy process.
     * @param source Map Configuration data
     * @return Representation of the artifact as a Map.
     * @throws Exception Unable to create the representation due to missing details.
     */
    static def composeArtifact(Map source) throws Exception {

            def artifact = [:]
            def properties = []
            
            try {
                artifact.put('repo', source.artifactRepository)
                artifact.put('path', source.artifactPath)
                artifact.put('name', source.targetZipName+'.'+source.version+'.'+source.extension)

                source.artifactProperties.each { item -> properties.add([key: item.prop, value: item.value])}            
                artifact.put('properties', properties)

                def artifactUniqueName = AssureUtils.getFromRegEx(artifact.name, source.regex_artifact_uniquename)
                artifact.put('uniquename', artifactUniqueName)

            } catch (Exception e) {
                Utils.throwExceptionInstance('Exception', "-- ⚠ -- Artifact could not be composed. Error: ${e.getMessage()}")
            }
            return artifact
    }

    static def composeArtifactJson(Map source) throws Exception {

            def artifact = [:]
            def properties = []
            
            try {
                artifact.put('repo', source.artifactRepository)
                artifact.put('path', source.artifactoryPath +'/'+ source.artifactoryBranch)
                artifact.put('name', source.artifactoryFileName+'.'+source.version+'.json')

                source.artifactProperties.each { item -> properties.add([key: item.prop, value: item.value])}            
                artifact.put('properties', properties)
                artifact.put('uniquename', source.artifactoryFileName)

            } catch (Exception e) {
                Utils.throwExceptionInstance('Exception', "-- ⚠ -- Artifact could not be composed. Error: ${e.getMessage()}")
            }
            return artifact
    }

    static def composeArtifactDocker(Map source) throws Exception {

        def artifact = [:]
        def properties = []
        
        try {
            artifact.put('repo', source.artifactRepository)
            artifact.put('path', source.artifactPath+'/'+source.artifactoryFileName+'/'+source.version)
            artifact.put('name', 'manifest.json')

            source.artifactProperties.each { item -> properties.add([key: item.prop, value: item.value])}            
            artifact.put('properties', properties)

            def value = AssureUtils.getFromRegEx(artifact.path as String, source.regex_docker_version)
            value = ValuesUtils.removeStartEndChars(artifact.path.substring(0, (artifact.path.size() - value.size())), '/', true, true)

            def uniquename = AssureUtils.getFromRegEx(value as String, source.regex_docker_identifier) // base
            value = ValuesUtils.removeStartEndChars(value.substring(0, (value.size() - uniquename.size())), '/', true, true)

            def branch = AssureUtils.getFromRegEx(value as String, source.regex_docker_branch)

            def artifactName = value.substring(0, (value.size() - branch.size())) + uniquename
  
            artifactName = '/' + artifact.repo + '/' + artifactName
            artifactName = artifactName.replace('/feature', '')

            artifact.put('uniquename', artifactName)

        } catch (Exception e) {
            Utils.throwExceptionInstance('Exception', "-- ⚠ -- Artifact could not be composed. Error: ${e.getMessage()}")
        }
        return artifact
}
        
    /**
     * Take the values retrieved from Artifactory and create a representation of an artifact that we will use during
     * the Pull Pipeline process. It works with different types of artifacts to generate the required data structures.
     * @param source Map with the artifact data retrieved from Artifactory.
     * @param data Configuration data.
     * @param apiWrapper Loaded wrapper classes for the different APIs we consume.
     * @return Representation of the artifact as a Map.
     * @throws Exception Unable to create the representation due to missing details.
     */
    static def composeAndValidateArtefact(Map source, LinkedHashMap data) throws Exception {
        
        ArtifactoryWrapper artifactory = new ArtifactoryWrapper(ValuesUtils.getVariable(data, "artifactory_url"), ValuesUtils.getVariable(data, "artifactory_credential"))  

        if (source == null || source.properties == null) {
            Utils.throwExceptionInstance('Exception', 'Artifact does not contain any property')
        }

        def status = source.properties.find { property -> property.key == 'status' }
        if (status == null) {
            Utils.throwExceptionInstance('Exception', 'Artifact does not contain a status property')
        }
              
        def artifact = source
        def invalidArtifacts = [:]
        try {
            //Shared logic across any type of artifacts
            artifact.put('git_url', source.properties.find { property -> property.key == 'git_url' }.value)
            artifact.put('commit', source.properties.find { property -> property.key == 'commit' }.value)
            artifact.put('repo', source.repo)
            artifact.put('path', source.path)
            artifact.put('name', source.name)
            artifact.put('status', status.value)            
            def committer = source.properties.find { property -> property.key == 'committer' }?.value
            if (committer) {
                def newEmail
                def domainName = committer.replaceAll("(.*@)", "")
                if (domainName == 'csc.com') {
                    newEmail = committer.replaceAll("(@.*)", "@dxc.com")
                } else {
                    newEmail = committer
                }
                artifact.put('committer', newEmail) 
            }
            def type = source.properties.find { property -> property.key == 'type' }.value
            artifact.put('type', type)
            artifact.put('targetEnv', getTargetEnv(data, artifact))
                        
            //Specific logic based on the type of artifact we are handling
            switch (type) {
                case 'docker':
                    //CHECK
                    def value = AssureUtils.getFromRegEx(artifact.path as String, ValuesUtils.getVariable(data, 'regex_docker_version'))
                    artifact.put('version', value.substring(0, value.size()))

                    value = ValuesUtils.removeStartEndChars(artifact.path.substring(0, (artifact.path.size() - value.size())), '/', true, true)
                    def uniquename = AssureUtils.getFromRegEx(value as String, ValuesUtils.getVariable(data, 'regex_docker_identifier'))

                    value = ValuesUtils.removeStartEndChars(value.substring(0, (value.size() - uniquename.size())), '/', true, true)

                    def branch = AssureUtils.getFromRegEx(value as String, ValuesUtils.getVariable(data, 'regex_docker_branch'))
                    artifact.put('branch', ValuesUtils.removeStartEndChars(branch, '/', true, true))
                    artifact.put('pathnobranch', ValuesUtils.removeStartEndChars(value.substring(0, (value.size() - branch.size())), '/', true, true))

                    def artifactName = value.substring(0, (value.size() - branch.size())) + uniquename
                    artifactName = '/' + source.repo + '/' + artifactName

                    value = AssureUtils.getFromRegEx(artifact.name as String, ValuesUtils.getVariable(data, 'regex_docker_extension'))
                    artifact.put('extension', value.substring(1, value.size()))

                    // TMP!
                    artifactName = artifactName.replace('/feature', '')
                    
                    artifact.put('deploymentPackages', artifact.service_name)
                    break
                case 'tf':
                    def jsonDeployInfo = artifactory.getArtifact(artifact.repo + '/' + artifact.path + '/' + artifact.name)                    
                    artifact.put('deploy_git_url', jsonDeployInfo.git_url)
                    artifact.put('tag', jsonDeployInfo.tag)
                    artifact.put('version', jsonDeployInfo.version)
                    def repoName = AssureUtils.getFromRegEx(jsonDeployInfo.git_url as String, ValuesUtils.getVariable(data, 'regex_tf_reponame'))                    

                    artifact.put('deploymentPackages', artifact.service_name)
                    break
                case 'generic':
                    artifact.put('version', AssureUtils.getFromRegEx(artifact.name as String, ValuesUtils.getVariable(data, 'regex_artifact_version')))
                    artifact.put('extension', AssureUtils.getFromRegEx(artifact.name as String, ValuesUtils.getVariable(data, 'regex_artifact_extension')))
                    def branch = AssureUtils.getFromRegEx(artifact.path as String, ValuesUtils.getVariable(data, 'regex_artifact_branch'))
                    artifact.put('branch', branch)
                    def path = AssureUtils.getFromRegEx(artifact.path as String, ValuesUtils.getVariable(data, 'regex_artifact_pathnobranch'))
                    artifact.put('pathnobranch', ValuesUtils.removeStartEndChars(path, '/', true, true))

                    artifact.put('deploymentPackages', artifact.service_name)
                    break
                default:
                    invalidArtifacts.put(artifact.name, 'Unknown artifact type')
                    // Utils.throwExceptionInstance('Exception', "-- ⚠ -- Unknown artifact type ${source.name}")
            }
            
            artifact = cleanUp (artifact)            
            
        } catch (Exception e) {            
            invalidArtifacts.put(artifact.name, "${e.getMessage().replaceAll("[({})]", "")}")
            // Utils.throwExceptionInstance('Exception', "-- ⚠ -- Artifact could not be composed. Error: ${e.getMessage()}")
        }        
        return [artifact, invalidArtifacts]
    }

    static Map getDeploymentPackagesByRepoName(String repoName, def deploymentPackages) {        
        def deploymentPackagesFound = deploymentPackages._links?.item.findAll {deploymentPackage ->
                                        deploymentPackage.summary.source.contains(repoName)                                        
                                   }
        if (deploymentPackagesFound == []) {
            Utils.throwExceptionInstance('Exception', "${repoName} - no deployment packages found")
        }
        return deploymentPackagesFound.name
    }

    static Map getArtifactData(String artName, def deploymentPackages) {        
        def deploymentPackagesFound = deploymentPackages._links?.item.findAll {deploymentPackage ->
                                        deploymentPackage.summary.artefacts.findAll { art -> art.name == artName }                                        
                                   }
        if (deploymentPackagesFound == []) {
            Utils.throwExceptionInstance('Exception', "${artName} - no deployment packages found")
        }
        return deploymentPackagesFound.summary.service_name
    }

    static String getTargetEnv(LinkedHashMap data, Map artifact) {          

        // def deploymentPackageType = (artifact.service_type == 'Infrastructure') ? 'infra' : 'no-infra'

        Map envStatusMap = ValuesUtils.getVariableArrayList(data, 'environment_status_mapping', '').collectEntries { entry -> [entry[0], entry[1]] } 

        // def envTarget = envStatusMap.get(artifact.status + '-' + deploymentPackageType)
        def envTarget = envStatusMap.get(artifact.status)

        return envTarget
    }

    static Map cleanUp (Map artifact) {
        def removeKeys = ['properties', 'size', 'created', 'created_by', 'modified', 'modified_by', 'updated', 'service_type', 'tags']
        removeKeys.each { k ->
            artifact.remove(k)
        }        
        return artifact
    }

    static def findArtefacts (def allArtifacts, Map configuration) {    
        
        EnvServiceApi envService = new EnvServiceApi(configuration.environment_service_url, configuration.environment_service_credential, configuration.oauth_host, configuration.env_api_key)

        def foundArts = []
        def notFoundArts = []
        allArtifacts.each { art ->
            def artifactUniqueName = AssureUtils.getFromRegEx(art.name, configuration.regex_artifact_uniquename)

            def type = art.properties.find { property -> property.key == 'type' }.value            
            if (type == 'docker') {
                def value = AssureUtils.getFromRegEx(art.path as String, configuration.regex_docker_version)
                value = ValuesUtils.removeStartEndChars(art.path.substring(0, (art.path.size() - value.size())), '/', true, true)
                def uniquename = AssureUtils.getFromRegEx(value as String, configuration.regex_docker_identifier)
                value = ValuesUtils.removeStartEndChars(value.substring(0, (value.size() - uniquename.size())), '/', true, true)

                def branch = AssureUtils.getFromRegEx(value as String, configuration.regex_docker_branch)
                def artifactName = value.substring(0, (value.size() - branch.size())) + uniquename
                artifactName = '/' + art.repo + '/' + artifactName

                // TMP!
                artifactName = artifactName.replace('/feature', '')

                artifactUniqueName = artifactName
            }
            def found = false                                                                                                                  
            if (type == 'tf') {                                                                            
                def gitUrl = art.properties.find { property -> property.key == 'git_url' }.value                                                                    
                def repoName = AssureUtils.getFromRegEx(gitUrl as String, ValuesUtils.getVariable(configuration, 'regex_tf_reponame'))
                repoName = repoName.substring(0, repoName.indexOf('_'))
                
                def dpsFilteredByRepoName = envService.getWithCustomAuth(configuration.environment_service_url + '/deployment-packages?repo=' + repoName)

                dpsFilteredByRepoName._links.item.each { item -> 
                    found = true
                    def mergeMap = [:]
                    mergeMap.put('uniquename', artifactUniqueName)                                                                                                                                       
                    mergeMap.putAll(item.summary)                                                                    
                    mergeMap.putAll(art)
                    def (result, invalidArtifacts) = composeAndValidateArtefact(mergeMap, configuration)                                                                    
                    if (result != []) foundArts.add(result)                                                                    
                    if (invalidArtifacts != [:]) notFoundArts.add(invalidArtifacts)
                }
            }
            else {
                def dpsFilteredByArt = envService.getWithCustomAuth(configuration.environment_service_url + '/deployment-packages?artefact=' + artifactUniqueName)
                dpsFilteredByArt._links.item.each { item -> 
                    found = true
                    def mergeMap = [:]
                    mergeMap.put('uniquename', artifactUniqueName)
                    mergeMap.putAll(item.summary)
                    mergeMap.putAll(art)
                    def (result, invalidArtifacts) = composeAndValidateArtefact(mergeMap, configuration)                                                                   
                    if (result != []) foundArts.add(result)                                                                    
                    if (invalidArtifacts != [:]) notFoundArts.add(invalidArtifacts)
                }
            }                                                                
            if (!found) {
                def notFoundMergeMap = [:]
                notFoundMergeMap.put(((type != 'docker') ? art.name : art.path), "was discarded because no Deployment Package was found")
                notFoundArts.add(notFoundMergeMap)
            }                        
        }
        
        return [foundArts, notFoundArts]
    }

    static def filterAvailableDeploymentPackages(def currentEnv, def environment) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def deploymentPackages = environment._embedded._links.item     
        def values = []        
        currentEnv.value.each { artifact ->
            def deploymentPackage = deploymentPackages.find { dp -> dp.summary.service_name == artifact.service_name }            
            if (deploymentPackage != null) {
                values.add(artifact)
            } else {                
                context.echo "-- ⚠️🚮 -- Artifact ${artifact.name} will not be processed. Deployment package ${artifact.service_name} is not available in target environment ${currentEnv.key}"                
            }
        }
        
        return values
    }

/**
    * This method replace version to a format that allows sorting easier.
    * @param version Passed version.
    */
    static def versionReplacer(def version) {
        def versionReplaced = version.replaceAll("[+-]", '.').split("\\.")
        def sortedVersion = versionReplaced.collect{it -> Integer.parseInt(it) + 100000000}.join(".")
        return sortedVersion
    }
    
    /**
    * This method reorders the list by version number. Version format examples: 0.0.0 or 0.0.0+1
    * @param artifacts Passed artifacts.
    */
    static def orderByVersion(ArrayList artifacts) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }
        def sortedArtifacts = artifacts

        try {
            sortedArtifacts.sort { a, b ->
                def fa = versionReplacer(a.version)
                def fb = versionReplacer(b.version)
                if (fa > fb) return -1
                else if (fa < fb) return 1
                else return 0
            }
        } catch (Exception e) {
            context.echo "-- ☠️ -- Something went wrong sorting the artifacts . Error: ${e.getMessage()}"
            sortedArtifacts = sortByVersionNonCPS(artifacts)
        }
        return sortedArtifacts
    }

    static def runDeploymentStage(String environment, ArrayList artifacts, LinkedHashMap configuration) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def everyDeployedArtifact = []

        EnvServiceApi envService = new EnvServiceApi(configuration.environment_service_url, configuration.environment_service_credential, configuration.oauth_host, configuration.env_api_key)
        Artifactory artifactory = new Artifactory(ValuesUtils.getVariable(configuration, "artifactory_url"), ValuesUtils.getVariable(configuration, "artifactory_credential"))

        context.stage(environment) {
            if (artifacts != null && artifacts.size() > 0) {
                try {
                    def environmentType = ValuesUtils.getVariable(configuration, "stages_${environment}_type")
                    switch (environmentType) {
                        case 'development':                            
                            def resultStage = runDevOrTestStage(environment, artifacts, configuration, 'dev')
                            everyDeployedArtifact.add(resultStage)                            
                            break
                        case 'test':                            
                            def resultStage = runDevOrTestStage(environment, artifacts, configuration, 'test')
                            everyDeployedArtifact.add(resultStage)
                            break
                        case 'staging':
                            def resultStage = runStagingOrProductionStage(environment, artifacts, configuration, 'staging')
                            everyDeployedArtifact.add(resultStage)
                            break
                        case 'prod':
                            def resultStage = runStagingOrProductionStage(environment, artifacts, configuration, 'production')
                            everyDeployedArtifact.add(resultStage)
                            break
                        default:
                            context.echo "-- ❌ -- Unknown environment type ${environmentType} for environment ${environment}"
                            // unblockEverythingOnError(environment, artifacts, data, apiWrapper)
                    }
                    
                } catch (Exception e) {                    
                    context.echo "-- ❌ -- Error found during deployment to ${environment}: ${e.getMessage()}"
                    def urlPath = envService.composeURLPath(configuration, environment, true)
                    envService.setEnvironmentStatus(urlPath, false)
                    artifactory.blockArtifacts(artifacts, false)
                }
                // if (!operationResult) {
                //     // setupResultBadge("badge_${environment}", "Deployment to ${environment}", 'Failure', 'red')
                //     context.echo("-- ❌ -- Deployment to ${environment} was not successful. Please check logs.")
                // }
            } else {
                // This marks the stage as skipped
                org.jenkinsci.plugins.pipeline.modeldefinition.Utils.markStageSkippedForConditional(environment)
                context.echo "-- ℹ -- Skipping deployment to ${environment} - no artifacts found"               
            }
        }
        def result = [:]
        // result.put('operationResult', operationResult)
        result.put('deployedArtifacts', everyDeployedArtifact)
        return result
    }

    static def runDevOrTestStage(String environment, ArrayList artifacts, LinkedHashMap configuration, String type) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def successStatus = ValuesUtils.getVariable(configuration, "environment_${environment}_status_success")
        def failureStatus = ValuesUtils.getVariable(configuration, "environment_${environment}_status_failure")
        def rollBack = (ValuesUtils.getVariable(configuration, 'rollback', environment) == 'true' ) ? true : false

        def everyDeployedArtifact = []
        def operationResult = true

        setupTestExecution(environment, configuration)

        //TMP
        EnvServiceApi envService = new EnvServiceApi(configuration.environment_service_url, configuration.environment_service_credential, configuration.oauth_host, configuration.env_api_key)
        Artifactory artifactory = new Artifactory(ValuesUtils.getVariable(configuration, "artifactory_url"), ValuesUtils.getVariable(configuration, "artifactory_credential"))                    

        orderByVersion(artifacts).each { artifact ->
            try {
                context.stage("${artifact.name}") {
                    context.echo "-- ℹ️-- Deploy artifact: ${artifact.name} for deployment package ${artifact.service_name}"
                    def deployResult
                    try {                                                
                        deployResult = envService.deploy(environment, artifact.service_name, [artifact], configuration)                        
                    } catch (Exception e) {                        
                        context.echo "-- ❌ -- Error deploying artifact ${artifact.name} to environment ${environment} for deployment package ${artifact.service_name}"
                    }
                    // TEST
                    def testResult = false
                    if (deployResult?.success) {
                        try {
                            if ('dev' == type) {
                                String targetEnvironment = configuration."${environment}_environment_resource_name"                                
                                testResult = runTest(environment, targetEnvironment, artifact, 'development', 'main', configuration)
                            }
                            else if ('test' == type) {                                
                                String targetEnvironment = configuration."${environment}_environment_resource_name" 
                                boolean selfTests = runTest(environment, targetEnvironment, artifact, 'test', 'main', configuration)
                                boolean siblingsTests = runOtherArtifactsTests(environment, artifact, configuration, 'test', false)
                                boolean dependentsTests = runDependentsTests(environment, artifact, configuration, 'test')
                                testResult = (selfTests && siblingsTests && dependentsTests)                                
                            }
                            else
                                Utils.throwExceptionInstance('Exception', 'Unknown type of testing required')
                        } catch (Exception e) {
                            testResult = false
                            operationResult = false
                            context.echo "-- ❌ -- Error testing artifact ${artifact.name} to environment ${environment} for deployment package ${artifact.service_name}"
                        }
                    } else {
                        context.echo '-- ⏭️-- Skipping testing due to deployment error'
                    }
                    def multipleArtifacts = ((artifacts.findAll { search -> search.name == artifact.name}).size() > 1)

                    everyDeployedArtifact.add([identifier    : environment + '-' + artifact.name + '-' + artifact.service_name,
                                               artifact      : artifact.name,
                                               deploymentPackage : artifact.service_name,
                                               environment   : environment,
                                               deployresult  : deployResult?.success,
                                               location      : deployResult.location,
                                               multiple      : multipleArtifacts,
                                               testresult    : testResult,                                               
                                               artifactStatus: (deployResult?.success && testResult) ? successStatus : failureStatus,
                                               testStatus    : testResult ? 'successful' : 'failed',
                                               repo          : artifact.repo,
                                               name          : artifact.name,
                                               path          : artifact.path,
                                               committer     : artifact.committer,
                                               commit        : artifact.commit,
                                               git_url       : (artifact.type != "generic") ? ValuesUtils.removeStartEndChars(artifact.git_url, '.git', false, true) : artifact.git_url,
                                               log_url       : deployResult.log_url
                                            ])

                    if (multipleArtifacts != true) {
                        artifactory.deployArtifactsCompleted([artifact], (deployResult?.success && testResult) ? successStatus : failureStatus)
                        envService.setTestResults(deployResult.location, (testResult ? 'successful' : 'failed'))
                    } else {
                        context.echo '-- ⏭️-- Results cannot be stored until the artifact is deployed to every environment and deployment package'
                    }

                    if (!deployResult?.success || !testResult) {
                        operationResult = false
                        context.echo "--❌️-- Error detected during deployment / testing of ${artifact.name} for deployment package ${artifact.service_name}"                        
                        Utils.throwExceptionInstance("Failure during deployment and testing of ${artifact.name}")
                    } else {
                        context.echo "--🥳-- ${artifact.name} successfully deployed and tested"
                    }                   
                }
            } catch (Exception e) {                
                context.echo "-- ❌ -- Failure during Deploy and Test of ${artifact.name} to environment ${environment}"

                if (rollBack) {
                    context.stage("Rollback deployment package: ${artifact.service_name}") {
                        def rollbackResult = envService.rollBack(artifact.service_name, configuration, environment)
                        context.echo "--🔙-- Rollback result ${rollbackResult.success} for deployment package ${artifact.service_name}"
                    }
                }
            }
        }
        
        context.echo "-- ℹ️-- Set environment ${environment} as available"
        def urlPath = envService.composeURLPath(configuration, environment, true)  
        envService.setEnvironmentStatus(urlPath, false)
        context.echo "-- ℹ️-- Deployment for environment ${environment} is completed."
        return everyDeployedArtifact
    }

    static void runStagingOrProductionStage(String environment, ArrayList artifacts, LinkedHashMap configuration, String type) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }        

        def successStatus = ValuesUtils.getVariable(configuration, "environment_${environment}_status_success")
        def failureStatus = ValuesUtils.getVariable(configuration, "environment_${environment}_status_failure")
        def rollBack = (ValuesUtils.getVariable(configuration, 'rollback', environment) == 'true' ) ? true : false

        def prefix = (type == 'staging') ? 'staging' : 'prod'
        
        EnvServiceApi envService = new EnvServiceApi(configuration.environment_service_url, configuration.environment_service_credential, configuration.oauth_host, configuration.env_api_key)
        Artifactory artifactory = new Artifactory(ValuesUtils.getVariable(configuration, "artifactory_url"), ValuesUtils.getVariable(configuration, "artifactory_credential"))
        
        def customList
        def awsData
        def deploymentPackages = [:]
        def everyDeployedArtifact = []
        def createdReleases = []
        def failedReleases = []
        context.stage('Remove duplicated artifacts') {
            // Take only one version (oldest) of the same artifact, rest will not be deployed on this iteration            
                        
            customList = orderByVersion(artifacts)
            //TMP - configuration as parameter
            deploymentPackages = filterArtifacts(customList, configuration)
            customList = deploymentPackages // USED in closure                       
            deploymentPackages = deploymentPackages.groupBy({ artifact -> artifact.service_name})            
                               
            context.sh(
                    script: "{ echo \"${JsonOutput.prettyPrint(JsonOutput.toJson(deploymentPackages))}\"; } 2> /dev/null",
                    label: "\"--🐸📦-- Print list of deployment packages and artifacts in scope after 'unique' filter"
            )
            context.writeFile file: "trace/${environment}-final-environment-mapping.json", text: "${JsonOutput.prettyPrint(JsonOutput.toJson(deploymentPackages))}"
        }

        def noFailure = true
        
        // CHECKS
        def skipRelease = false
        def testResult = true
        def operationResult = true
        
        def deploymentPackageResults = [:]
        //TODO: can might be optimized to run the deployment in parallel - problem: dependencies
        deploymentPackages.each { dp, list ->
            try {
                context.stage("Deploy deployment package: ${dp}") {
                    context.echo "-- ℹ️-- Deploy deployment package ${dp}"                    
                    def result = envService.deploy(environment, dp, list, configuration)
                    deploymentPackageResults.put(dp, result)
                    if (!result.success) {
                        noFailure = false
                        context.error("-- ❌ -- Deployment of deployment package ${dp} failed")
                    }
                }
            } catch (Exception e) {
                noFailure = false
            }
        }

        //Only perform the tests if all deployment was successful. If not, just mark testing as failed.
        if (noFailure) {
            setupTestExecution(environment, configuration)

            if ('staging' == type) {
                //TODO: can may be optimized to run the tests in parallel
                deploymentPackages.each { dp, list ->
                    def deploymentPackageTest = false
                    try {
                        context.stage("Test deployment package: ${dp}") {
                            context.echo "-- ℹ️-- Test deployment package ${dp}"
                            String targetEnvironment = configuration."${environment}_environment_resource_name"
                            // Assumption: all artifacts passed need to be from the same deployment package, so taking first one already provides all the required information.
                            // Mejora: no ejecutar los siguientes tests si ya fallaron
                            boolean sameDeploymentPackage = runOtherArtifactsTests(environment, list[0], configuration, 'staging', true)                            
                            boolean dependentsTests = runDependentsTests(environment, list[0], configuration, 'staging')                           
                            boolean dependenciesTests = runDependenciesTests(environment, list[0], configuration, 'staging')
                            
                            testResult = (sameDeploymentPackage && dependentsTests && dependenciesTests)                                                   
                        }
                    } catch (Exception e) {
                        testResult = false
                        context.echo("-- ❌ -- Error testing deployment package ${dp}")
                    }
                }
                if (testResult == true) {
                    context.stage('e2e Test') {                    
                        boolean e2eTestResult = runPlatformTests(environment, configuration)
                        testResult = (testResult && e2eTestResult)                        
                    }
                }
                context.echo "-- ${(testResult) ? '👍 Test process successfully completed' : '👎 -- Test process failed'}"

            } else if ('production' == type) {
                context.stage('e2e Test') {
                    skipRelease = ValuesUtils.getVariable(configuration, 'skipRelease')
                    testResult = runPlatformTests(environment, configuration)
                    context.echo "-- ${(testResult) ? '👍 Test process successfully completed' : '👎 -- Test process failed'}"
                }
                
                // //Burp Suite stage
                // if (testResult == true) {
                //     context.stage('Burp Suite scan') {
                //         def urls_to_scan = configuration.get('burp-suite-urls', [])
                //         if (urls_to_scan == null || !(urls_to_scan instanceof List)) {
                //             urls_to_scan = []
                //         }
                //         context.echo "-- ℹ️ -- URLs provided:"
                //         def burp_start_urls = urls_to_scan.join(',').replaceAll(' ', '')
                //         context.echo "Loaded URLs: ${urls_to_scan}"

                //         if (burp_start_urls.trim().isEmpty()) {
                //             context.echo "-- ⚠️ -- No URLs provided! Skipping the scanning process."
                //         } else {
                            
                //             // context.echo "-- ℹ️ -- VPN Conection"
                //             context.withCredentials([context.file(credentialsId: 'burpsuite_vpn', variable: 'CONFIG_BURP')]) {
                //                 context.sh(script: 'cat ${CONFIG_BURP} > burpsuite.jenkins.ovpn')                                         
                //                 context.sh script: """
                //                     set +x
                //                     sudo apt-get update && sudo apt-get install -y openvpn
                //                     sudo mkdir -p /etc/openvpn
                //                     mv burpsuite.jenkins.ovpn /etc/openvpn/client.conf
                //                     mkdir -p /dev/net                           
                //                     chmod 666 /dev/net/tun
                //                     sudo openvpn --config /etc/openvpn/client.conf &
                //                     set -x                                            
                //                 """, label: 'Burp suite settings'
                //                 context.sleep(10)                                        
                //             }

                //             context.echo '-- ℹ️ -- Installing dependencies'
                //             context.sh script: '''
                //                 sudo apt-get update && sudo apt-get install -y python3 python3-pip python3-lxml xsltproc
                //                 python3 -m pip install --no-cache-dir --upgrade pip lxml
                //             '''

                //             context.echo '-- ℹ️ -- Preparing files'
                //             //Importng burp_to_html.py
                //             def burp_to_html = context.libraryResource(resource:"custom/promotion-project/burp-suite/burp_to_html.py")
                //             context.writeFile(file: "${context.WORKSPACE}/burp_to_html.py", text: burp_to_html)
                //             // Importing burp_config.yml
                //             def burp_config = context.libraryResource(resource:"custom/promotion-project/burp-suite/burp_config.yml")
                //             context.writeFile(file: "${context.WORKSPACE}/burp_config.yml", text: burp_config)

                //             def repositoryName = configuration.get('repositoryName')
                //             context.echo "Project name: ${repositoryName}"

                //             def envServFile = context.libraryResource(resource: "custom/promotion-project/burp-suite/${configuration.burp_conf_file}.gpg", encoding: "Base64")
                //             context.writeFile(file: "${configuration.burp_conf_file}.gpg", text: envServFile, encoding: "Base64")

                //             def burpData = AssureUtils.decryptYmlFile(configuration.passphrase_id, configuration.burp_conf_file)
                            
                //             context.echo '-- ℹ️ -- Running Burp Suite scan'
                //             context.withEnv([
                //                 'BURP_CONFIG_FILE_PATH=burp_config.yml',
                //                 "BURP_START_URLS=${burp_start_urls}",
                //                 'BURP_SCAN_CONFIGURATIONS=Crawl and Audit - CICD Optimized',
                //                 "BURP_CORRELATION_ID=${repositoryName}"
                //             ]) {
                //                 def reportFile = "scan-report.html"

                //                 context.sh script: """
                //                     set +x
                //                     docker run --rm --pull=always \
                //                     -u \$(id -u) -v ${context.WORKSPACE}:${context.WORKSPACE}:rw -w ${context.WORKSPACE} \
                //                     -e BURP_LOGIN_CREDENTIALS=${configuration.user_pool}:${configuration.password_pool} \
                //                     -e BURP_CONFIG_FILE_PATH=${context.WORKSPACE}/\${BURP_CONFIG_FILE_PATH} \
                //                     -e BURP_ENTERPRISE_API_KEY=${burpData.burp_enterprise_api_key} \
                //                     -e BURP_ENTERPRISE_SERVER_URL=${burpData.burp_enterprise_server_url} \
                //                     -e BURP_START_URLS="\${BURP_START_URLS}" \
                //                     -e BURP_SCAN_CONFIGURATIONS="\${BURP_SCAN_CONFIGURATIONS}" \
                //                     -e BURP_CORRELATION_ID=${context.env.BURP_CORRELATION_ID} \
                //                     -e BURP_REPORT_FILE_PATH=${context.WORKSPACE}/${reportFile} \
                //                     public.ecr.aws/portswigger/enterprise-scan-container:latest || true
                //                     set -x
                //                 """, label: 'Burp suite execution'
                                
                //                 context.echo '-- ℹ️ -- Convert XML to HTML'
                //                 def outputFile = "burp-scan.html"

                //                 context.sh script: """
                //                     python3 ${context.WORKSPACE}/burp_to_html.py --input ${context.WORKSPACE}/${reportFile} --output ${context.WORKSPACE}/${outputFile}
                //                 """

                //                 context.archiveArtifacts artifacts: outputFile
                //             }
                       
                //             context.sh script: 'rm burp_creds.yml && rm burp_creds.yml.gpg'
                //         }
                //     } 
                // } else {
                //     context.echo '-- ⚠️ -- Burp Suite stage skipped due to e2e failed'
                // }

                if (testResult == true && skipRelease != true) { //! skipRelease allows to skip release creation stage at will
                    context.stage('⛳ Release Service') {
                        try {
                            // Deployment packages has been deployed & tests successfully
                            def env = 'release'
                            deploymentPackages.each{ dp, list ->
                                context.echo "Deployment package ⛳: ${dp}"
                                def result
                                def failedArtifact
                                (result, failedArtifact) = releaseService(env, list[0], configuration)
                                if (result.size() > 0) createdReleases.add(result)
                                if (failedArtifact.size() > 0)  failedReleases.add(failedArtifact)
                            }
                        } catch (Exception e) {
                            context.error("-- ❌ -- Error: ${e.getMessage()}")
                        }
                        if (createdReleases != null && !createdReleases.isEmpty()) {        
                            context.echo "Create release file in the Update Environments Pipeline"

                            context.writeJSON file: "new_releases.json", json: createdReleases

                            def repoUpdateEnv = ValuesUtils.getVariable(configuration, 'updateEnvRepo')
                            def branchUpdateEnv = ValuesUtils.getVariable(configuration, 'updateEnvBranch')
                            def orgUpdateEnv = ValuesUtils.getVariable(configuration, 'updateEnvOrg')

                            try {
                                def cred = ValuesUtils.getVariable(configuration, 'gitHubCredential')
                                def mail = ValuesUtils.getVariable(configuration, 'gitEmail')
                                def user = ValuesUtils.getVariable(configuration, 'gitUsername')
                                def url = ValuesUtils.getVariable(configuration, 'gitHubUrl')

                                AssureUtils.gitSetupCred(cred, mail, user, url)

                                context.withCredentials([context.usernamePassword(credentialsId: configuration.gitHubCredential, passwordVariable:'GITHUB_PASSWORD', usernameVariable:'GITHUB_USER')]) {
                                    context.sh script: """
                                    rm -rf ${repoUpdateEnv}
                                    git clone -b ${branchUpdateEnv} https://${context.env.GITHUB_USER}:${context.env.GITHUB_PASSWORD}@${DefaultConfiguration.PDXC_GITHUB_HOST}/${orgUpdateEnv}/${repoUpdateEnv}.git                                    
                                    cp new_releases.json ./${repoUpdateEnv}                                
                                    cd ${repoUpdateEnv}                                                                       
                                    cat new_releases.json
                                    git add new_releases.json                                    
                                    git commit -m 'New release'
                                    git push ${DefaultConfiguration.PDXC_GITHUB_URL}/${orgUpdateEnv}/${repoUpdateEnv}.git ${branchUpdateEnv}
                                """, label: 'Git clone and commit a new release'
                                }
                                // git commit --amend --reset-author --no-edit
                            } catch (Exception e) {
                                context.echo "-- ❌ -- Update environments can not be triggered. Error: ${e.getMessage()}"
                                // context.error("-- ❌ -- Error: ${e.getMessage()}")
                            }
                        }
                        if (failedReleases != null && failedReleases.size() > 0) {
                            def releasesJson = context.readJSON file: "new_releases.json"
                            def rsFailedArts = prepareRsErrorDataForNotifications(failedReleases)
                            manageNotifications(rsFailedArts, releasesJson, configuration)
                            context.unstable("-- ❌ -- Something went wrong creating the release/s: ${failedReleases}")
                        }
                    }
                }
                else {
                    if (skipRelease == true) {
                        context.echo '-- ⚠️ -- Release creation stage skipped'
                    } else {
                        context.echo '-- ℹ️ -- Skipping release creation due to testing failed'
                    }
                }
            } else {
                Utils.throwExceptionInstance('Exception', 'Unknown type of testing required')
            }
        } else {
            testResult = false
            context.stage('Skip all testing') {
                context.echo '-- ℹ️-- Skipping testing due to deployment error'
            }
        }

        context.stage('Closure') {
            if (!noFailure || !testResult) {
                operationResult = false
            }
            // Update every entity with the corresponding status and values
            def artStatus = (testResult) ? successStatus : failureStatus
            def deployStatus = (operationResult) ? 'successful' : 'failed'
            context.sh(
                    script: "{ echo \"${JsonOutput.prettyPrint(JsonOutput.toJson(['Artifacts': customList]))}\"; } 2> /dev/null",
                    label: "\"-- ℹ -- Set status to ${artStatus} for..."
            )

            customList.each { art ->
                def failureOnRs = false
                if (failedReleases != null) {
                    failureOnRs = ((failedReleases.findAll { search -> search.name[0] == art.name}).size() > 0)
                }

                artStatus = (art.status == failureStatus && testResult == false) ? 'no-promoted-'+failureStatus : ((testResult) ? successStatus : failureStatus)
                
                def deployResponse = deploymentPackageResults.get(art.service_name)
                def multipleArtifacts = ((customList.findAll { search -> search.name == art.name}).size() > 1)

                everyDeployedArtifact.add([identifier    : environment + '-' + art.name + '-' + art.service_name,
                                           artifact      : art.name,
                                           deploymentPackage : art.service_name,
                                           environment   : environment,
                                           deployresult  : noFailure,
                                           testresult    : testResult,
                                           location      : deployResponse.location,
                                           multiple      : multipleArtifacts,                                           
                                           artifactStatus: artStatus,
                                           testStatus    : testResult ? 'successful' : 'failed',
                                           repo          : art.repo,
                                           name          : art.name,
                                           path          : art.path,
                                           committer     : art.committer,
                                           commit        : art.commit,
                                           git_url       : (art.type != "generic") ? ValuesUtils.removeStartEndChars(art.git_url, '.git', false, true) : art.git_url,
                                           log_url       : deployResponse.log_url,
                                           fail_on_release : failureOnRs
                ])

                if (rollBack && (!noFailure || !testResult)) {
                    context.stage("Rollback deployment package: ${art.service_name}") {
                        def rollbackResult = envService.rollBack(art.service_name, configuration, environment)
                        context.echo "--🔙-- Rollback result ${rollbackResult.success} for deployment package ${art.service_name}"
                    }
                }
                
                if (multipleArtifacts != true) {
                    artifactory.deployArtifactsCompleted([art], artStatus)
                    envService.setTestResults(deployResponse.location, deployStatus)
                } else {
                    context.echo "-- ⏭️-- Results cannot be stored until the artifact ${art.name} is deployed to every environment and deployment package"
                }

                if (failureOnRs != false && environment == 'prod') {
                    context.unstable(message: "-- ❌ -- Error during the creation of the release/s for: ${art.name}")
                }
            }

            def discardedArtifacts = artifacts.minus(customList)
            context.echo "-- ℹ️-- Unblock any discarded artifact in Artifactory: ${discardedArtifacts}"
            if (discardedArtifacts != null && discardedArtifacts.size() > 0) {
                artifactory.blockArtifacts(discardedArtifacts, false)
            }

            context.echo "-- ℹ️-- Set environment ${environment} as available"
            def urlPath = envService.composeURLPath(configuration, environment, true)  
            envService.setEnvironmentStatus(urlPath, false)       

            createdReleases.each{ release ->
                if (release.version != null && release.deployment_package_name != null) {
                    context.echo "-- ⛳ -- New version: ${release.version} for Deployment Package: ${release.deployment_package_name}"   
                }
            }           
        }
        return everyDeployedArtifact
    }

    static def filterArtifacts (def customList, LinkedHashMap configuration) {
        def filtered = []

        Artifactory artifactory = new Artifactory(ValuesUtils.getVariable(configuration, "artifactory_url"), ValuesUtils.getVariable(configuration, "artifactory_credential"))
        
        customList.groupBy({ artifact -> "${artifact.uniquename + artifact.service_name}" }).collect { k,v -> 
                                        if (v.size() > 1) {                                            
                                            if (v[0].status != 'general-error' && v[0].status != 'prod-error') {                                                
                                                filtered.add(v[0])
                                            }
                                            else {
                                                def valid = false                                                
                                                v.find{ ent ->
                                                    if (ent.status == 'general-error' || ent.status == 'prod-error') {
                                                        artifactory.updateArtifactsStatus([ent], "in-error")                                                        
                                                    }
                                                    else {
                                                        filtered.add(ent)
                                                        valid = true                                                        
                                                        return true
                                                    }
                                                }                                                
                                                if (valid==false) filtered.add(v[v.size() - 1])
                                            }
                                        }
                                        else {                                            
                                            filtered.add(v[0])
                                        }
                                    }
        return filtered
    }

    @NonCPS
    static List sortByVersionNonCPS(ArrayList list) {
        return list.sort { value ->
            value.version
        }
    }

    //////////////////////////////////////////////////////////////////TEST////////////////////////////////////////////////////////////////

    static Map setupTestExecution(String environment, LinkedHashMap data) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def arn = data."${environment}_environment_arn"
        def region = data."${environment}_environment_region"
        def environmentType = data."${environment}_environmentType"// + '-' + data."${environment}_environmentId"
        def poolId = data."${environment}_poolId"
        def clientId = data."${environment}_clientId"
        def apiKey = data."${environment}_apiKey"
        def user_pool = data.user_pool
        def password_pool = data.password_pool
        def customerName = ValuesUtils.getVariable(data, 'customerName', environment)

        def UUID = UUID.randomUUID().toString()
        UUID = UUID.substring(0, UUID.indexOf('-'))

        def setupFile = context.libraryResource(resource: "testScripts/setup.sh")
        context.writeFile(file: "setup.sh", text: setupFile)

        def credId = (credentialsAwsExist('ASSURE-AWS-CLI-RW') == true) ? 'ASSURE-AWS-CLI-RW' : 'DIAAS-AWS-CLI'
        
        context.withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: credId, accessKeyVariable: 'AWS_ACCESS_KEY_ID', secretKeyVariable: 'AWS_SECRET_ACCESS_KEY']]) {
            context.withCredentials([context.usernamePassword(credentialsId:'diaas-rw', passwordVariable:'ARTIF_PASSWORD', usernameVariable:'ARTIF_USER')]) {
                context.sh script: """
                                rm -rf /home/<USER>/.aws/credentials
                                rm -rf /home/<USER>/.aws/config
                                bash ./setup.sh ${UUID} ${arn} ${region} ${environmentType} ${poolId} ${clientId} ${apiKey} ${user_pool} ${password_pool} ${customerName}
                            """
            }
        }


        // CHECK
        Map result = [:]
        result.put('arn', arn)
        result.put('region', region)        
        result.put('environmentType', environmentType)
        result.put('UUID', UUID)
        return result
    }

    static boolean runTest(String environment, String targetEnv, Map artifact, String stage, String scope, LinkedHashMap data) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }
        
        boolean testResult = true
        try {

            if (artifact.git_url ==  null && artifact.commit == null) {
                ArtifactoryWrapper artifactory = new ArtifactoryWrapper(ValuesUtils.getVariable(data, "artifactory_url"), ValuesUtils.getVariable(data, "artifactory_credential"))
                def props = artifactory.getArtifactProperties (artifact)                
                artifact.put ('commit', props.commit[0])
                artifact.put ('git_url', props.git_url[0])                    
                artifact.put ('name', props.name) 
            }
            
            getTestFolder(artifact.name + environment, artifact.git_url, artifact.commit, data)

            def testShPath = "testrun/${artifact.name + environment}/test/test.sh"
            def testConfPath = "testrun/${artifact.name + environment}/test/testConf.yml"
            def exists = context.fileExists testShPath
            def existsTestYml = context.fileExists testConfPath
            if (exists || existsTestYml) {
                testResult = executeTest(artifact.name + environment, stage, scope, environment, artifact, data)
                persistTestResults(artifact.name + environment)
            }
            else {
                context.echo("--🚨⏭️-- Artifact ${artifact.uniquename} does not have specific tests to be run")
            }
        } catch (Exception e) {
            testResult = false
            context.error("-- ❌ -- Error preparing and running tests for ${artifact.uniquename}")
        }
        cleanTest(artifact.name + environment)
        return testResult
    }

    static boolean getTestFolder(String name, String repoURL, String commitID, def configuration) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def removedUrl = repoURL.replaceAll(".*//", '')

        try { 
            context.withCredentials([context.usernamePassword(credentialsId: configuration.gitHubCredential, passwordVariable:'GITHUB_PASSWORD', usernameVariable:'GITHUB_USER')]) {
                def newUrl = "https://${context.env.GITHUB_USER}:${context.env.GITHUB_PASSWORD}@${removedUrl}"
                
                context.sh script: """
                                    echo 'Prepare for testing...'
                                    rm -rf ${name}
                                    mkdir -p testrun
                                    cd testrun
                                    mkdir -p ${name}
                                    cd ${name}
                                    rm -rf .git
                                    git init
                                    git remote add origin -f ${newUrl}
                                    git pull origin ${commitID}
                                    git reset --hard ${commitID}
                                    if [ -f test/test.sh ]; then chmod +x test/test.sh ; fi
                                    if [ -f test/test.sh ]; then cat test/test.sh ; fi
                                """,
                            label: "Get Testing resources for ${name} from GitHub repository"
            }
        } catch (Exception e) {
            context.error("-- ❌ -- Error: ${e.getMessage()}")
        }
    }

    static boolean executeTest(String name, String stage, String scope, String environment, Map artifact, LinkedHashMap data) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def statusCode
        def path = "testrun/${name}/test"
        def UUID = UUID.randomUUID().toString()
        UUID = UUID.substring(0, UUID.indexOf('-'))

        def endpoint = data."${environment}_environment_endpoint"        
        def customer = ValuesUtils.getVariable(data, 'customerName', environment)//data.customerName
        def environmentType = data."${environment}_environmentType" //+ '-' + data."${environment}_environmentId"
        def testTokenUrl = data."${environment}_testTokenUrl"        

        def testParameters = context.readYaml file: "./test-parameters-${environmentType}.yml"
        
        def files = context.findFiles(glob: "testrun/${name}/**/login.json")
        if (files != null && files != [] && files.size() > 0) {
            def loginJson = context.readJSON file: "testrun/${name}/cypress/fixtures/login.json"
            loginJson.username = testParameters.USER
            loginJson.password = testParameters.PASSWORD
            loginJson.loginURL = loginJson.loginURL.substring(0, loginJson.loginURL.indexOf('.') + 1) + "${endpoint}" + '/'
            
            context.writeJSON file: "testrun/${name}/cypress/fixtures/login.json", json: loginJson
            context.sh script: "cat testrun/${name}/cypress/fixtures/login.json", label: 'Reading login.json'
        }
        else {
            // Check token
            def headers = [[name: 'x-api-key', value: "${testParameters.KEY}"], [name: 'Authorization', value: "Bearer ${testParameters.TOKEN}"]]
            def httpResponse = context.httpRequest url: testTokenUrl, customHeaders: headers, validResponseCodes: '200:599'
            def response = context.readJSON text: httpResponse.content

            if (response?.message == 'Unauthorized' || response?.message == 'Forbidden' || response?.messages?.message?.contains('Unauthorized') || response?.messages?.message?.contains('Forbidden')) {
                context.sh script: "rm -rf test-parameters-${environmentType}.yml", label: 'Removing old token file'
                setupTestExecution(environment, data)
                testParameters = context.readYaml file: "./test-parameters-${environmentType}.yml"
            }
        }

        buildTestParametersYml(path, testParameters, customer, endpoint, artifact)

        def credId = (credentialsAwsExist('ASSURE-AWS-CLI-RW') == true) ? 'ASSURE-AWS-CLI-RW' : 'DIAAS-AWS-CLI'
        
        def testConfPath = "${path}/testConf.yml"
        def exists = context.fileExists testConfPath
        if (exists) {
            def typeTest = context.readYaml file: "${path}/testConf.yml"
            path = (typeTest.type == 'cypress') ? "testrun/${name}" : "testrun/${name}/test"
            def cypressVersion  = (typeTest.type == 'cypress') ? AssureUtils.setCypressVersion(data) : ''
            def testPath = (typeTest.testsPath != null) ? typeTest.testsPath : ''
            context.withCredentials([context.file(credentialsId: 'npmrc', variable: 'CONFIG')]) {
                context.withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: credId, accessKeyVariable: 'AWS_ACCESS_KEY_ID', secretKeyVariable: 'AWS_SECRET_ACCESS_KEY']]) {
                    context.withCredentials([context.usernamePassword(credentialsId:'diaas-rw', passwordVariable:'ARTIF_PASSWORD', usernameVariable:'ARTIF_USER')]) {
                        def startTestFile = context.libraryResource(resource: "testScripts/startTest.sh")
                        context.writeFile(file: "${path}/startTest.sh", text: startTestFile)
                        def testEntryPointFile = context.libraryResource(resource: "testScripts/test-entrypoint.sh")
                        context.writeFile(file: "${path}/test-entrypoint.sh", text: testEntryPointFile)
                        context.sh "cp newman-parameters-${environmentType}.json ${path}/newman-parameters-${environmentType}.json 2>/dev/null"
                        statusCode = context.sh script: """                                                                           
                                        cd ./${path}
                                        bash +x ./startTest.sh ${UUID} ${path} ${typeTest.type} ${testPath} ${cypressVersion} ${environmentType}
                                    """,
                            returnStatus: true,
                            label: "-- ⚙️-- Running tests for ${name}"
                    }
                }
            }

            if (typeTest.type == 'cypress') {
                try {
                    context.archiveArtifacts artifacts: "${path}/cypress/videos/**/*.mp4"
                } catch (Exception e) {
                    def cypressRoute = context.sh(script: "test -d ${path}/cypress/ && echo '1' || echo '0' ", returnStdout: true, label: "-- 👁️‍🗨️ -- Checking cypress folder for content").trim()
                    if (cypressRoute != '0') {
                        context.error "-- ❌ -- Error during cypress testing"
                    }
                }
            }
        }
        else {
            context.withCredentials([context.file(credentialsId: 'npmrc', variable: 'CONFIG')]) {                
                context.withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: credId, accessKeyVariable: 'AWS_ACCESS_KEY_ID', secretKeyVariable: 'AWS_SECRET_ACCESS_KEY']]) {
                    def setupFile = context.libraryResource(resource: "testScripts/refreshToken.sh")
                    context.writeFile(file: "${path}/refreshToken.sh", text: setupFile)
                    
                    statusCode = context.sh script: """                                    
                                    cd ./${path}
                                    bash +x ./test.sh ${UUID} ${path}
                                """,
                        returnStatus: true,
                        label: "-- ⚙️-- Running tests for ${name}"
                }
            }
        }

        context.echo "ENDPOINT: ${endpoint} STAGE: ${stage} SCOPE: ${scope}"
        return (statusCode != 0) ? false : true
    }
    
    static void buildTestParametersYml(String path, LinkedHashMap testParameters, String customer, String endpoint, Map artifact) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def variablesYml = [:]
        variablesYml.put('arn', testParameters.AWS_ARN)
        variablesYml.put('region', testParameters.AWS_REGION)
        variablesYml.put('customer', customer)
        variablesYml.put('environmentType', testParameters.ENV_TYPE)
        variablesYml.put('clientId', testParameters.CLIENT_ID)
        variablesYml.put('poolId', testParameters.POOL_ID)
        variablesYml.put('user', testParameters.USER)
        variablesYml.put('password', testParameters.PASSWORD)
        variablesYml.put('token', testParameters.TOKEN)
        variablesYml.put('apiKey', testParameters.KEY)
        variablesYml.put('endpoint', endpoint)
        variablesYml.put('profile', testParameters.AWS_SOURCE_PROFILE)
        variablesYml.put('artifactname', artifact.name)
        variablesYml.put('servicename', artifact.service_name)
        context.writeYaml file: "${path}/testParameters.yml", data: variablesYml, overwrite: true
        
    }

    static boolean persistTestResults(String name) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        // warnError(message: 'SUCCESS', catchInterruptions: 'FAILURE') {

        // context.junit "testrun/${name}/**/*-report.xml"
        context.junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: "testrun/${name}/**/*-report.xml")
    }

    static boolean cleanTest(String name) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        context.sh script: """
                        rm -rf testrun/${name}
                    """,
                label: "Clean up testing generated resources for ${name}"
    }

    static boolean runOtherArtifactsTests(String environment, Map artifact, LinkedHashMap configuration, String stage, boolean allArtifacts = false) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        context.echo("-- 2️⃣-- Running tests for other artifacts of the same deployment package (${artifact.service_name})")
        // Get information other artifacts from same artifacts
        def siblingsTests = true

        EnvServiceApi envService = new EnvServiceApi(configuration.environment_service_url, configuration.environment_service_credential, configuration.oauth_host, configuration.env_api_key)
        def artifacts = envService.getDeployedArtefactsData(configuration.environment_service_url, environment, artifact.service_name, configuration)

        ArtifactoryApi artifactoryApi = ArtifactoryApi.getInstance(null, configuration.artifactory_credential)
        
        
        def otherArtifacts
        def scope = 'sibling'
        if (allArtifacts) {
            otherArtifacts = artifacts            
            scope = 'main'
        } else {
            otherArtifacts = artifacts?.findAll { art -> art.uniquename != artifact.uniquename }            
        }        
        if (otherArtifacts) {
            String targetEnvironment = configuration."${environment}_environment_resource_name"
            otherArtifacts.each { otherArtifact ->
                context.echo("-- ℹ️-- Artifact to be tested: ${otherArtifact.name}")
                context.echo("-- ℹ️-- Artifact to be tested PARAMS: ${otherArtifact}")
                                
                def result = runTest(environment, targetEnvironment, otherArtifact, stage, scope, configuration)                              
                siblingsTests = (siblingsTests) ? result : siblingsTests
            }
        } else {
            context.echo("-- ⏭️-- There are no other artifacts deployed under this deployment package (${artifact.service_name})")
        }
        return siblingsTests
    }

    static boolean runDependentsTests(String environment, Map artifact, LinkedHashMap configuration, String stage) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        context.echo("-- 3️⃣-- Running tests for ${artifact.service_name} dependent services")

        ArtifactoryApi artifactoryApi = ArtifactoryApi.getInstance(null, configuration.artifactory_credential)

        boolean dependentsTests = true
        EnvServiceApi envService = new EnvServiceApi(configuration.environment_service_url, configuration.environment_service_credential, configuration.oauth_host, configuration.env_api_key)        
        def dependentsResponse = envService.getWithCustomAuth(configuration.environment_service_url + '/deployment-packages?dependson=' + artifact.service_name)
        def dependents = (dependentsResponse?._links?.item?.size() == 0) ? [] : dependentsResponse._links.item
        if (dependents) {
            String targetEnvironment = configuration."${environment}_environment_resource_name"
            dependents.each { otherDeploymentPackage ->
                context.echo("-- ℹ️-- Dependent deployment package: ${otherDeploymentPackage.name}")
                def artifactsList = envService.getDeployedArtefactsData(configuration.environment_service_url, environment, otherDeploymentPackage.name, configuration)
                if (artifactsList) {
                    artifactsList.each { otherDeploymentPackageArtifact ->
                        context.echo("-- ℹ️-- runDependentsTests artefact ${otherDeploymentPackageArtifact}")
                                                
                        def result = runTest(environment, targetEnvironment, otherDeploymentPackageArtifact, stage, 'dependent', configuration)                        
                        dependentsTests = (dependentsTests) ? result : dependentsTests
                    }
                } else {
                    context.echo "-- ⏭️-- Dependent deployment package ${otherDeploymentPackage.name} not installed or does not have artifacts to test"
                }
            }
        } else {
            context.echo "-- ⏭️-- No deployment package depends on ${artifact.service_name} and no other test is required"
        }
        return dependentsTests
    }

    static boolean runDependenciesTests(String environment, Map artifact, LinkedHashMap configuration, String stage) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        context.echo("-- 4️⃣-- Running tests for ${artifact.service_name} dependencies")
        EnvServiceApi envService = new EnvServiceApi(configuration.environment_service_url, configuration.environment_service_credential, configuration.oauth_host, configuration.env_api_key)

        ArtifactoryApi artifactoryApi = ArtifactoryApi.getInstance(null, configuration.artifactory_credential)

        boolean dependentsTests = true
        def dependencies = envService.getDeploymentPackageDependencies(configuration.environment_service_url, artifact.service_name)
        if (dependencies) {
            String targetEnvironment = configuration."${environment}_environment_resource_name"
            dependencies.each { otherDeploymentPackage ->                
                def artifactsList = envService.getDeployedArtefactsData(configuration.environment_service_url, environment, otherDeploymentPackage.service_name, configuration)
                if (artifactsList) {
                    context.echo "-- ℹ -- Performing testing of dependency deployment package ${otherDeploymentPackage.service_name}"
                    artifactsList.each { otherDeploymentPackageArtifact ->
                        context.echo("-- ℹ️-- runDependenciesTests artefact ${otherDeploymentPackageArtifact}")
                                                
                        def result = runTest(environment, targetEnvironment, otherDeploymentPackageArtifact, stage, 'dependencies', configuration)                                                
                        dependentsTests = (dependentsTests) ? result : dependentsTests
                    }
                } else {
                    context.echo "-- ⏭️-- Dependency deployment package ${otherDeploymentPackage.service_name} does not have artifacts to test"
                }
            }
        } else {
            context.echo "-- ⏭️-- ${artifact.service_name} does not have any dependency and no other test is required"
        }
        return dependentsTests
    }

    static boolean runPlatformTests(String environment, LinkedHashMap configuration) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def endpoint = configuration."${environment}_environment_endpoint"        
        def environmentType = configuration."${environment}_environmentType"// + '-' + configuration."${environment}_environmentId"
        def testTokenUrl = configuration."${environment}_testTokenUrl"
        def repo = configuration.'e2eTest_repository'
        def branch = configuration.'e2eTest_branch'
        def org = configuration.'e2eTest_org'
        
        context.echo "e2e test run in environment ${environment}"
        // Clone e2e testing repo
        // context.functiongroup_git.clone(org, repo, branch)

        context.withCredentials([context.usernamePassword(credentialsId: configuration.gitHubCredential, passwordVariable:'GITHUB_PASSWORD', usernameVariable:'GITHUB_USER')]) {
            context.sh script: """
                rm -rf ${environmentType}/${repo}
                mkdir -p ${environmentType}
                cd ${environmentType}
                git clone -b ${branch} https://${context.env.GITHUB_USER}:${context.env.GITHUB_PASSWORD}@${DefaultConfiguration.PDXC_GITHUB_HOST}/${org}/${repo}.git
            """, label: 'Git clone'
        }

        //Run test
        def statusCode
        def UUID = UUID.randomUUID().toString()
        UUID = UUID.substring(0, UUID.indexOf('-'))

        def testParameters = context.readYaml file: "./test-parameters-${environmentType}.yml"

        def files = context.findFiles(glob: "${environmentType}/${repo}/cypress/fixtures/login*.json")        
        if (files != null && files != [] && files.size() > 0) {
            files.each { file ->
                def loginJson = context.readJSON file: "${environmentType}/${repo}/cypress/fixtures/${file.name}"
                loginJson.username = testParameters.USER
                loginJson.password = testParameters.PASSWORD
                loginJson.loginURL = loginJson.loginURL.substring(0, loginJson.loginURL.indexOf('.') + 1) + "${endpoint}" + '/'
                context.writeJSON file: "${environmentType}/${repo}/cypress/fixtures/${file.name}", json: loginJson                
            }            
        }

        // Check token
        def headers = [[name: 'x-api-key', value: "${testParameters.KEY}"], [name: 'Authorization', value: "Bearer ${testParameters.TOKEN}"]]
        def httpResponse = context.httpRequest url: testTokenUrl, customHeaders: headers, validResponseCodes: '200:599'
        def response = context.readJSON text: httpResponse.content

        if (response?.message == 'Unauthorized' || response?.message == 'Forbidden' || response?.messages?.message?.contains('Unauthorized') || response?.messages?.message?.contains('Forbidden')) {
            context.sh script: "rm -rf test-parameters-${environmentType}.yml", label: 'Removing old token file'
            setupTestExecution(environment, configuration)
            testParameters = context.readYaml file: "./test-parameters-${environmentType}.yml"
        }

        context.withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: 'ASSURE-AWS-CLI-RW', accessKeyVariable: 'AWS_ACCESS_KEY_ID', secretKeyVariable: 'AWS_SECRET_ACCESS_KEY']]) {
            context.withCredentials([context.usernamePassword(credentialsId: configuration.artifactory_credential, passwordVariable:'ARTIF_PASSWORD', usernameVariable:'ARTIF_USER')]) {
                statusCode = context.sh script: """
                            chmod -R 777 ${environmentType}/${repo}
                            cd ${environmentType}/${repo}
                            bash ./script-executor.sh ${UUID} ${testParameters.TOKEN} ${testParameters.KEY} ${endpoint} ${environmentType}/${repo} ${environmentType}
                        """,
                    returnStatus: true,
                    label: '-- ⚙️-- Running Platform Tests'
                
                if (statusCode != 0) context.unstable('E2E failed!')                
            }
        }

        try {
            context.junit(skipMarkingBuildUnstable: true, testResults: "${environmentType}/${repo}/**/*-report.xml")
        } catch (Exception e) {            
            context.echo "-- ❌ -- Error saving xml reports"            
        }

        try {
            context.archiveArtifacts artifacts: "${environmentType}/${repo}/cypress/videos/**/*.mp4"
        } catch (Exception e) {
            def cypressRoute = context.sh(script: "test -d ${repo}/cypress/ && echo '1' || echo '0' ", returnStdout: true, label: "-- 👁️‍🗨️ -- Checking cypress folder for content").trim()
            if (cypressRoute != '0') {
                context.error "-- ❌ -- Error during cypress testing"
            }
        }

        return (statusCode != 0) ? false : true
    }

    ///////////////////////////////////////////////////////////////////////////RELEASE//////////////////////////////////////////////////////////////////////

    static Map releaseService(String environment, Map list, LinkedHashMap configuration) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }
       
        EnvServiceApi envService = new EnvServiceApi(configuration.environment_service_url, configuration.environment_service_credential, configuration.oauth_host, configuration.env_api_key)
        def customer = ValuesUtils.getVariable(configuration, 'customerName', 'prod')
        def account = ValuesUtils.getVariable(configuration, 'accountName', 'prod')
        def resourceName = ValuesUtils.getVariable(configuration, 'environment_resource_name', 'prod')

        // Retrieve last deployment package info, within being able to compose the body parameters to call release service            
        // String urlLastDeploy = "/customers/${customer}/accounts/${account}/environments/${resourceName}/deployments/${list.service_name}-${resourceName}?trail=true"        
        // def previousArtifactsInfo = envService.getWithCustomAuth(configuration.environment_service_url + urlLastDeploy)
        // def lastDeployInfo = previousArtifactsInfo.trail[0].deploy_info
               
        // String deploymentUri = configuration.environment_service_url + urlLastDeploy + '&resource_name=' + previousArtifactsInfo.trail[0].resource_name

        String urlLastDeploy = "/customers/${customer}/accounts/${account}/environments/${resourceName}/deployment-packages/${list.service_name}/deployments"
        def deploymentsHistory = envService.getWithCustomAuth(configuration.environment_service_url + urlLastDeploy)
        def previousArtifactsInfo = envService.getWithCustomAuth(configuration.environment_service_url + urlLastDeploy + '/' + deploymentsHistory._links.item[0].summary.resource_name)
        def lastDeployInfo = previousArtifactsInfo.deploy_info
               
        String deploymentUri = configuration.environment_service_url + urlLastDeploy + '/' + deploymentsHistory._links.item[0].summary.resource_name

        
        def dependsOnData = (previousArtifactsInfo.dependsOn != null) ? previousArtifactsInfo.dependsOn : [] 
        def dependsOnDataMap = [:]
        dependsOnDataMap.put("dependsOn", dependsOnData)
        def preparedDependsOnData = JsonOutput.toJson(dependsOnDataMap)
        preparedDependsOnData = ValuesUtils.removeStartEndChars(preparedDependsOnData, "{", true, false)
        preparedDependsOnData = ValuesUtils.removeStartEndChars(preparedDependsOnData, "}", false, true)

        def ssmConditions = (previousArtifactsInfo.ssm_conditions != null) ? previousArtifactsInfo.ssm_conditions : []

        def ssmConditionsMap = [:]
        ssmConditionsMap.put("ssm_conditions", ssmConditions)
        def preparedssmConditionsJson = JsonOutput.toJson(ssmConditionsMap)
        preparedssmConditionsJson = ValuesUtils.removeStartEndChars(preparedssmConditionsJson, "{", true, false)
        preparedssmConditionsJson = ValuesUtils.removeStartEndChars(preparedssmConditionsJson, "}", false, true)

        def params = []
        // TEMP - OC-14380
        // parameters for csam-dev
        if (configuration.environment_service_url.contains('csam-dev') == true) {
            String urlDPParams = "/deployment-packages/${list.service_name}"
            def paramsInfoFromDP = envService.getWithCustomAuth(configuration.environment_service_url + urlDPParams)
            
            if (paramsInfoFromDP.service_parameters != null && paramsInfoFromDP.service_parameters.size() > 0 ) {
                paramsInfoFromDP.service_parameters.each { prop -> 
                    def paramsInfoFromDPMap = [:]
                    def preparedInfoFromDP = JsonOutput.toJson(prop)
                    params.add(preparedInfoFromDP)
                }
            }
        }
        else {
            // parameters for csam PROD
            if (previousArtifactsInfo.parameters != null) {
                previousArtifactsInfo.parameters.each { prop -> 
                    def paramsMap = [:]
                    paramsMap.put("${prop.key}", "${prop.value}")
                    def preparedParams = JsonOutput.toJson(paramsMap)
                    params.add(preparedParams)
                }
            }
        }
        
        // OJO git url tiene que ser con git:: //code
        def tag = ""        
        if (lastDeployInfo.tag != "null" && lastDeployInfo.tag != null) tag = ", \"tag\": \"${lastDeployInfo.tag}\""

        def body_parameters = """{
                "type": "deployment-package",
                "mode": "automatic",
                "creator": \"assure-pipeline\",
                "resource_name": \"${list.service_name}\",
                "description": "Automatic Release for DU ${list.service_name}",
                "deployment_descriptor": 
                {
                    "deploy_info": {
                        "git_url": \"${lastDeployInfo.git_url}\"
                        ${tag}                 
                    },
                    "artifacts": ${previousArtifactsInfo.artifacts.toString()},
                    ${preparedDependsOnData},
                    "parameters": ${params},
                    ${preparedssmConditionsJson}
                },
                "tests": [],               
                "deployment_log": \"${previousArtifactsInfo.locationURL}\",
                "deployment_uri": \"${deploymentUri}\"
            }"""

        context.echo "-- ⭐ -- BODY: ${body_parameters}"
        
        def apiKey = configuration.release_apiKey
        def environmentEndPoint = configuration.release_service_url
        def oauthHost = configuration.release_oauth_host
               
        def apiUrl = environmentEndPoint + "/releases"

        def updateEnvMap = [:]
        def failedArtifact = []

        // context.withCredentials([context.usernamePassword(credentialsId:'RELEASE_SERVICE_ID', passwordVariable:'REL_PASSWORD', usernameVariable:'REL_USER')]) {
        //     context.withCredentials([context.string(credentialsId:'RELEASE_CLIENTID', variable:'REL_CLIENT_ID')]) {
                
        def url = "https://cognito-idp.us-east-1.amazonaws.com/"
        def headers = [[name: 'Content-Type', value: "application/x-amz-json-1.1"], [name: 'X-Amz-Target', value: "AWSCognitoIdentityProviderService.InitiateAuth"]]
        
        def bodyTok = """ {
            "AuthParameters" : { "USERNAME" : \"${configuration.release_user}\", "PASSWORD" : \"${configuration.release_password}\"},
            "AuthFlow" : "USER_PASSWORD_AUTH",
            "ClientId" : \"${configuration.release_clientId_auth}\"
        }
        """
                                
        def httpResponse = context.httpRequest url: url,
                customHeaders: headers,
                httpMode: 'POST',
                requestBody: bodyTok,
                validResponseCodes: '200:599'

        // START Silver Image Filter
        def checkAssureRepo = lastDeployInfo.git_url.contains('assure-terraform-images')
        def checkTfapiRepo = lastDeployInfo.git_url.contains('dxc-assure-tfapi')
        if (checkAssureRepo == true || checkTfapiRepo == true ) {
            if (httpResponse.content != null) {
                try {  
                    def response = context.readJSON text: httpResponse.content
                    def token = response.AuthenticationResult.AccessToken

                    def headersCreate = [[name: 'x-api-key', value: "${apiKey}"], [name: 'Authorization', value: "Bearer ${token}"]]                    
                    def httpResponseCreate = context.httpRequest url: apiUrl,
                        contentType: "APPLICATION_JSON",
                        customHeaders: headersCreate,
                        httpMode: 'POST',
                        requestBody: body_parameters,
                        validResponseCodes: '201'

                    def responseRelease = context.readJSON text: httpResponseCreate.content

                    updateEnvMap.put('deployment_package_name', responseRelease.resource_name)
                    updateEnvMap.put('version', responseRelease.resource_version)
                } catch (Exception e) {
                    failedArtifact.add(list)
                }
            } else {
                context.echo "Unable to authenticate against Release Service"
            }
        } else {
            failedArtifact.add(list)
            context.unstable(message:"-- 👷 -- The current artifact is not a Silver Image. Skipping release creation for ${list.service_name}.")
        }

        return [updateEnvMap, failedArtifact]
    }
    
    static boolean credentialsAwsExist(String id) {

        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }
        
        try {
            context.withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: id, accessKeyVariable: 'AWS_ACCESS_KEY_ID', secretKeyVariable: 'AWS_SECRET_ACCESS_KEY']]) {
                return true
            }
        } catch (Exception e) {
            return false
        }
    }

    /**
    * This method concatenates messages and committers.
    * @param artifact Passed previous artifact
    * @param collector Passed current artifact
    * @param message Builded message
    */
    // static def concatenateMessagesAndCommitters (def artifact, def collector, def message) {
    //     if (artifact.deploymentPackage == collector.deploymentPackage && artifact.environment == collector.environment) {
    //         artifact.message = artifact.message + message
    //         artifact.committer = artifact.committer + ", " + collector.committer
    //     }
    //     return artifact
    // }

    /**
    * This method prepares the data of the artifact promotion status to send the notifications.
    * @param deployedData Passed deployed artifacts.
    * @param blueOceanUrl Pipeline url.
    * @param config Configuration variables.
    * @return [devOrTestEmailArts, stageOrProdEmailArts, successEmailArts] Filtered artifacts messages.
    */
     static def prepareDataForNotifications (ArrayList deployedData, String blueOceanUrl, def releasesJson, Map config) {
        
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def devOrTestArts = [:]
        def stageOrProdArts = [:]
        def successArts = [:]

        def devOrTestEmailObjects = []
        def stageOrProdEmailObjects = []
        def successEmailObjects = []

        def devOrTestTeamsObjects = []
        def stageOrProdTeamsObjects = []
        def successTeamsObjects = []

        def logUrlRegex = '[^/]+(?=/$|$)'
        def locationUrlRegex = '(/customers)(.+)'
        def environmentServiceUrl = config.environment_service_application_url

        if (deployedData != null && deployedData.size() > 0) {
            try {
                deployedData.each { data ->
                    data.each { art ->
                        if (art.location != null && art.location != '') { 
                            def overallStatus = (art.deployresult == true && art.testresult == true)
                            def errorStatus = (art.deployresult != true) ? '-- ❌ -- Error during deployment' : '-- ❌ -- Error during testing'
                            def logToken = AssureUtils.getFromRegEx(art.log_url, logUrlRegex)
                            def logPath = AssureUtils.getFromRegEx(art.location, locationUrlRegex)
                            def logUrl = environmentServiceUrl + logPath + '/logs/' + logToken

                            if (overallStatus != true) {
                                if (art.environment.equals('development') || art.environment.equals('test')) {
                                    def message = EmailUtils.composeNotificationMessage(art, logUrl, blueOceanUrl, errorStatus, true)
                                    def emailObject = EmailUtils.buildEmailObject(art, errorStatus, message)
                                    devOrTestEmailObjects.add(emailObject)
                                    def msTeamsObject = MsTeamsUtils.buildMsTeamsObject(art, blueOceanUrl, logUrl, overallStatus)
                                    devOrTestTeamsObjects.add(msTeamsObject)

                                } else if (art.environment.equals('prod')) {
                                    stageOrProdEmailObjects = EmailUtils.createEmailNotificationObjects(stageOrProdEmailObjects, art, errorStatus, overallStatus, logUrl, blueOceanUrl, releasesJson, false)
                                    def msTeamsObject = MsTeamsUtils.buildMsTeamsObject(art, blueOceanUrl, logUrl, overallStatus)
                                    stageOrProdTeamsObjects.add(msTeamsObject)
                                }
                                else {
                                    if (art.environment.equals('staging')) {
                                        stageOrProdEmailObjects = EmailUtils.createEmailNotificationObjects(stageOrProdEmailObjects, art, errorStatus, overallStatus, logUrl, blueOceanUrl, releasesJson, false)
                                        def msTeamsObject = MsTeamsUtils.buildMsTeamsObject(art, blueOceanUrl, logUrl, overallStatus)
                                        stageOrProdTeamsObjects.add(msTeamsObject)
                                    }
                                }

                            } else if (overallStatus != false && art.environment == 'prod'){
                                def exists = context.fileExists('new_releases.json')
                                if (exists && art.fail_on_release == false) {
                                    successEmailObjects = EmailUtils.createEmailNotificationObjects(successEmailObjects, art, errorStatus, overallStatus, logUrl, blueOceanUrl, releasesJson, true)
                                    def msTeamsObject = MsTeamsUtils.buildMsTeamsObject(art, blueOceanUrl, logUrl, overallStatus)
                                    successTeamsObjects.add(msTeamsObject)
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                context.error("-- ❌ -- Error preparing the notifications. Error:${e.getMessage()}")
            }
        }
        devOrTestArts.put('emailObjects', devOrTestEmailObjects)
        stageOrProdArts.put('emailObjects', stageOrProdEmailObjects)
        successArts.put('emailObjects', successEmailObjects)

        devOrTestArts.put('msTeamsObjects', devOrTestTeamsObjects)
        stageOrProdArts.put('msTeamsObjects', stageOrProdTeamsObjects)
        successArts.put('msTeamsObjects', successTeamsObjects)

        return [devOrTestArts, stageOrProdArts, successArts]
    }

    static def prepareRsErrorDataForNotifications (def failedReleases) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def blueOceanUrl = AssureUtils.getBlueOceanUri(context.env)

        def rsFailedArts = [:]
        def rsFailedEmailObjects = []
        def rsFailedTeamsObjects = []

        failedReleases.each { data ->
            data.each { art ->
                def emailMessage = EmailUtils.composeRsErrorNotification(art, blueOceanUrl)

                rsFailedEmailObjects.add([committer: art.committer, 
                    message: emailMessage, 
                    deploymentPackage: art.deploymentPackages])

                rsFailedTeamsObjects.add([deploymentPackage: art.deploymentPackages,
                    committer: art.committer,
                    git_url: art.git_url,
                    commit: art.commit,
                    artifact: art.name,
                    blueOceanUrl: blueOceanUrl,
                    version: art.version])
            }
        }

        rsFailedArts.put('emailObjects', rsFailedEmailObjects)
        rsFailedArts.put('msTeamsObjects', rsFailedTeamsObjects)

        return rsFailedArts
    }

    /**
    * This method manages the artifacts to send the notifications.
    * @param artifacts Passed notification artifacts.
    */
    static void manageNotifications (def artifacts, def releasesJson='', def configuration) {
         def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        if (artifacts != null && artifacts.emailObjects.size() > 0) {
            artifacts.emailObjects.each{ obj ->
                try {
                    EmailUtils.sendEmailNotifications(obj.committer, obj.message, obj.deploymentPackage, 'notification')
                } catch (Exception e) {
                    context.error("-- ❌ -- Error sending the Email notifications. Error:${e.getMessage()}")
                }
            }
        }
        if (artifacts != null && artifacts.msTeamsObjects.size() > 0) {
            artifacts.msTeamsObjects.each{ obj ->
                try {
                    MsTeamsUtils.sendMsTeamsNotifications(obj, releasesJson, configuration)
                } catch (Exception e) {
                    context.error("-- ❌ -- Error sending the Teams notifications. Error:${e.getMessage()}")
                }
            }
        }
    }
}