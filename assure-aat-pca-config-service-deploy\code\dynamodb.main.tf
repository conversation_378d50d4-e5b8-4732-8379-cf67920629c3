# training status table
module "training_status_table" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//assure-standard-dynamodb/code"

  # * Standard - platform parameters
  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region           = var.aws_region
  resource_tags        = local.resource_tags

  # * Required - platform parameters
  # ? Table Name
  service_name = var.service_name
  table_name   = "training_status_table"
  # ? Table Configure ( options )
  billing_mode            = "PAY_PER_REQUEST"
  hash_key                = "id"
  range_key               = "filename"
  non_key_attributes_list = []
  
  # Global secondary index name
  gsi_list = [{
    name               = "filename-index"
    hash_key           = "filename"
    range_key          = "last_updated_timestamp"
    projection_type    = "ALL"
  }]

  attribute_list = [{
    name = "id"
    type = "S"
    },
    {
      name = "filename"
      type = "S"
    },
    {
    name = "filename"
    type = "S"
    },
    {
      name = "last_updated_timestamp"
      type = "S"
    }
  ]

}


# reports table
module "reports_table" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//assure-standard-dynamodb/code"

  # * Standard - platform parameters
  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region          = var.aws_region
  resource_tags        = local.resource_tags

  # * Required - platform parameters
  service_name = var.service_name
  table_name   = "reports_table"
  
  billing_mode            = "PAY_PER_REQUEST"
  hash_key                = "id"
  range_key               = "PolicyID"
  non_key_attributes_list = ["ComplexityScore", "ComplexityFactor", "ProductID", "filename"]  
  attribute_list = [
    {
      name = "id"
      type = "S" 
    },
    {
      name = "PolicyID"
      type = "S"
    },
    {
      name = "ComplexityScore"
      type = "N"
    },
    {
      name = "ProductID"
      type = "S"
    }
  ]

  gsi_list = [
    {
      name               = "ComplexityScoreIndex"
      hash_key          = "ComplexityScore"
      range_key         = "PolicyID"
      projection_type   = "ALL"
    },
    {
      name               = "ProductIDIndex"
      hash_key          = "ProductID"
      range_key         = "PolicyID"
      projection_type   = "ALL"
    }
  ]
}