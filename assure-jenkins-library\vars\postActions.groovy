#!/usr/bin/env groovy

/**
 * Definition of default functions to be executed at the end of every pipeline. In general these would be
 * notifications to be sent to different channels.
 */

import org.pdxc.util.ValuesUtils

/**
 * Executor for the default functions.
 * @param configData Map with the custom values to be used during the execution.
 */
def call(Map configData) {
    echo 'Start post actions: notification, cleanup...'

    if (configData != null) {
        if (ValuesUtils.getVariable(configData, 'sendMail', 'post') == 'true') {
            def mailTo = ValuesUtils.getVariable(configData, 'emailTo', 'post')
            def mailFrom = ValuesUtils.getVariable(configData, 'emailFrom', 'post')
            def attachmentFile = ValuesUtils.getVariable(configData, 'attachmentFile', 'post')
            functiongroup_notification.sendJenkinsResultEmail(mailTo, mailFrom, attachmentFile)
        }

        def teamsCredential = ValuesUtils.getVariable(configData, 'teamsSecretHookId', 'post')
        if (teamsCredential != null && teamsCredential != '') {
            functiongroup_notification.sendJenkinsResultTeamsTemplateNotification(teamsCredential)
        }
    }

    // Cleanup
    sh 'cd /'
    deleteDir()
}

return this
