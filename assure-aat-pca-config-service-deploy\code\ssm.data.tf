data "aws_ssm_parameter" "environment_random_string" {
  name = "/${local.client_short_name}/${local.environment_name}/platform/ENVIRONMENT_RANDOM_STRING"
}

data "aws_ssm_parameter" "vpc_id" {
  name = "/${local.client_short_name}/${local.environment_name}/platform/VPC_ID"
   lifecycle {
    postcondition {
      condition     = length(self.value) > 0
      error_message = "VPC ID parameter must not be empty"
    }
  }
}

data "aws_ssm_parameter" "environment_app_subnets" {
  name = "/${local.client_short_name}/${local.environment_name}/platform/subnets/private-app/SUBNET_IDS"
}
data "aws_ssm_parameter" "fts_bucket_name" {
  name = "/${local.client_short_name}/${local.environment_name}/services/file-transfer/staging/BUCKET_NAME"
}