#!/usr/bin/env groovy

import org.pdxc.rest.GitApi
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.util.DefaultConfiguration
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.pdxc.util.Utils
import org.assure.util.AssureUtils
import org.assure.util.WarningUtils
import org.assure.envservice.EnvServiceApi
import org.assure.pullpipeline.PullPipelineUtils
import org.assure.pushpipeline.PushPipelineUtils
import groovy.json.JsonOutput

boolean validDeployCustom(String branch, Map configData) {

    def isValid = (ValuesUtils.getVariable(configData, 'deployment', 'custom') == true) && 
            (branch ==~ /(^feature\/{1}+.*)/ ||
            branch ==~ /(^fix\/{1}+.*)/ ||
            branch ==~ /(^prepare\/{1}+.*)/ ||
            branch == 'development'
            ) || (("${branch}" == 'master') && (ValuesUtils.getVariable(configData, 'deployment_from_master', 'custom') == true))

    return isValid
}

/**
 * Pipeline creation and execution.
 *
 * @param stagesMap Specific data for each stage.
 * @param dockerPath Full path and name of a dockerFile to be used on the pipeline. If not provided, default is used.
 * @return void
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'data.Dockerfile') {
    String pipelineName = 'data'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the artifact generated
    String artifactName
    // Calculated new version
    String newVersion
    // Current repository name
    def repoName

    def deployResultList = []
    def testResultList = []
    def composableArtifactList = []

    PushPipelineUtils   pushUtils
    EnvServiceApi       envService

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 1, unit: 'HOURS')
        }

        stages {
            stage ('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        warningUtils = new WarningUtils()
                        dockerName = "${pipelineName}.Dockerfile"
                    }
                }
            }
            stage('Pipeline info') {
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()

                         def conf = libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
                        writeFile file: "${pipelineName}-conf.yml", text: conf
                        def defaultConfigYml = readYaml file: "${pipelineName}-conf.yml"
                        def repoData = readYaml file: 'conf.yml'

                        configData = defaultConfigYml + repoData
                        configData = pushUtils.writeConfigData(configData, 'generic')

                        println 'Loaded configuration values: \n\n' + JsonOutput.prettyPrint(JsonOutput.toJson(configData))

                        writeYaml file: 'conf.yml', data: configData, overwrite: true
                        
                        pushUtils.setDockerAgentForCustomNodeAndNpmVersion(dockerPath, dockerName)
                        (buildDockerRegistryUrl, buildDockerRegistryCreds) = pushUtils.getDockerRegistryUrlAndCreds(configData, dockerName, pipelineName)

                        pushUtils.executePostStageFunction(stagesMap, 'info')
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo "Configured repository name matches current repository: ${repoName}"

                        pushUtils.executePostStageFunction(stagesMap, 'validate')
                    }
                }
            }
            stage ('Artifact & Deploy') {
                agent {
                    dockerfile {
                        args '-u root:root'
                        filename 'data.Dockerfile'
                        reuseNode true
                        registryCredentialsId buildDockerRegistryCreds
                        registryUrl buildDockerRegistryUrl 
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('Set up') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'setup') } }
                        steps {
                            script {
                                // Calculate and set new version to be built
                                echo 'Calculate and set new version:'

                                def currentVersion = ValuesUtils.getVariable(configData, 'version', 'setup')
                                newVersion = currentVersion + "+${env.BUILD_NUMBER}"
                                echo "Current Version: ${currentVersion} --- New Version: ${newVersion}"

                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Zipping artifact') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'zip') } }
                        steps {
                            script {
                                def zipScript = ValuesUtils.getVariable(configData, 'zipScript', 'zip')
                                def zipInclude = ValuesUtils.getVariable(configData, 'zipInclude', 'zip')
                                if (zipInclude == null) zipInclude = ''
                                def sourceFolder = ValuesUtils.getVariable(configData, 'zipSourceFolder', 'zip')
                                artifactName = ValuesUtils.getVariable(configData, 'targetZipName', 'zip') + ".${newVersion}"
                                // Delete if file already exist
                                sh script: "rm -rf ${artifactName}.zip", label: 'Delete old version of zip file'
                                if (zipScript != "" && zipScript != null) {
                                    sh script: "${zipScript} ${artifactName}.zip ${sourceFolder} ${zipInclude}", label: 'Zip file using custom script'
                                } else {
                                    zip glob: "${zipInclude}", zipFile: "${artifactName}.zip", dir: "${sourceFolder}"
                                }
                                pushUtils.executePostStageFunction(stagesMap, 'zip')
                            }
                        }
                    }
                    stage('Upload artifact') {
                        when { expression { return ((pushUtils.notSkipStage(stagesMap, 'upload')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                        steps {
                            script {
                                def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
                                def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'upload')
                                if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                                def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'upload')
                                def localPath = ValuesUtils.getVariable(configData, 'artifactLocalPath', 'upload')
                                def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
                                functiongroup_artifactory.uploadGenericArtifact(cred, repo, artifactPath,
                                        artifactName + '.zip', localPath, artifactoryURL)

                                pushUtils.executePostStageFunction(stagesMap, 'upload')
                            }
                        }
                    }
                }
            }
            stage('Deploy custom') {                
                when { expression { return ((pushUtils.notSkipStage(stagesMap, 'deploy') && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/))) } }
                steps {
                    catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
                        script {
                            if (validDeployCustom("${BRANCH_NAME}", configData) == true) {
                                
                                def envServFile = libraryResource(resource: "${configData.env_service_conf_file}.gpg", encoding: "Base64")
                                writeFile(file: "${configData.env_service_conf_file}.gpg", text: envServFile, encoding: "Base64")

                                def envServData = AssureUtils.decryptYmlFile(configData.passphrase_id, configData.env_service_conf_file, '.')

                                configData.put('version', "${newVersion}")
                                configData.put('extension', 'zip')
                                configData = envServData + configData
                                
                                def artefact = PullPipelineUtils.composeArtifact(configData)

                                envService = new EnvServiceApi(configData.environment_service_url, configData.environment_service_credential, configData.oauth_host, configData.env_api_key)
                                def deploymentPackagesResponse = envService.getWithCustomAuth(configData.environment_service_url + '/deployment-packages?artefact=' + artefact.uniquename)
                                envService.setToken()

                                def targetEnvs
                                def artifacts = []                           
                                    
                                targetEnvs = configData.findAll { k, v -> k.endsWith("_environment_resource_name") }
                                def tempTargets = targetEnvs.findAll{ it.key != "sdlc-environment_environment_resource_name" }
                                targetEnvs = tempTargets

                                // Check if environment is locked
                                def environments = envService.getWithCustomAuth(configData.environment_service_url + "/environments")._links.item

                                environments.findAll { item ->                                                    
                                    targetEnvs.each { k,v ->                                                            
                                            if (item.summary.resource_name == v && item.summary.is_locked == true) {
                                                targetEnvs.remove(k)
                                                echo "-- ⚠️🚮 -- Your environment ${v} is locked"                                                                
                                            }
                                    }
                                }
                                                                
                                def parallelSteps = [:]                            
                                targetEnvs.keySet().each { targetEnv ->

                                    artefact = PullPipelineUtils.composeArtifact(configData)
                                    def artifactUniqueName = AssureUtils.getFromRegEx(artefact.name, configData.regex_artifact_uniquename)
                                    def environment = ValuesUtils.removeStartEndChars(targetEnv, '_environment_resource_name', false, true)
                                    def urlPath = envService.composeURLPath(configData, environment, true)

                                    deploymentPackagesResponse._links.item.each { deploymentPackage ->
                                        def mergeMap = [:]
                                        mergeMap.put('uniquename', artifactUniqueName)
                                        mergeMap.putAll(deploymentPackage.summary)
                                        mergeMap.putAll(artefact)
                                        def (result, invalidArtifacts) = PullPipelineUtils.composeAndValidateArtefact(mergeMap, configData)
                                        echo "❌ Discarded ❌ : ${invalidArtifacts}"
                                        if (result != []) artifacts.add(result)
                                    }

                                    echo "🏆 Artefacts 🏆 : ${artifacts}"

                                    parallelSteps[environment] = {
                                        try {
                                            artifacts.each { artifact ->
                                                try {
                                                    stage(artifact.service_name) {                                                       
                                                        Map deployment
                                                        try {
                                                            deployment = envService.deploy(environment, artifact.service_name, [artifact], configData)
                                                            deployResultList.add(deployment?.success)
                                                        } catch (Exception e) {
                                                            echo "--❌️-- Error detected during deployment of ${artifact.name} for deployment package ${artifact.service_name}"
                                                            deployResultList.add(false)
                                                            Utils.throwExceptionInstance("Failure during deployment ${artifact.name}")
                                                        }

                                                        if (ValuesUtils.getVariable(configData, 'skipPostDeployTest', "${environment}") == false) {
                                                            def testResult = false
                                                            if (deployment?.success) {                                                                                                                                        
                                                                pushUtils.prepareParamsTesting(environment, configData)
                                                                testResult = PullPipelineUtils.runTest(environment, environment, artifact, environment, 'custom', configData)
                                                                testResultList.add(testResult)                                                                    
                                                            } else {
                                                                echo '-- ⏭️-- Skipping testing due to deployment error'
                                                            }

                                                            if (!deployment?.success || !testResult) {
                                                                echo "--❌️-- Error detected during deployment / testing of ${artifact.name} for deployment package ${artifact.service_name}"
                                                                Utils.throwExceptionInstance("Failure during deployment and testing of ${artifact.name}")
                                                            } else {
                                                                echo "--🥳-- ${artifact.name} successfully deployed and tested"
                                                            }
                                                        }
                                                    }
                                                } catch (Exception e) {
                                                    echo "-- ❌ -- Failure during Deploy and Test of ${artifact.name} to environment ${targetEnv}"
                                                    deployResultList.add(false)
                                                }
                                            }
                                        } catch (Exception e) {
                                            error("-- ❌ -- Error during deployment to ${targetEnv}. Please check logs. Error: ${e}")                                                
                                        }
                                    }
                                }
                                parallel parallelSteps
                                
                            }
                            else {
                                echo 'Custom deploy is disabled'
                            }
                            pushUtils.executePostStageFunction(stagesMap, 'deploy')
                        }
                    }
                }
            }
            stage('Update artifact properties') {
                when { expression { return ((pushUtils.notSkipStage(stagesMap, 'update')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                steps {
                    script {
                        def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'update')
                        if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                        def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'update')
                        ArtifactoryApi artfApi = new ArtifactoryApi(artifactoryURL, cred)

                        def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'update')
                        def props = ValuesUtils.getVariableArrayList(configData, 'artifactProperties', 'update')
                        def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'update')
                        artfApi.updateArtifactProperties(repo, artifactPath, artifactName + '.zip', props)

                        pushUtils.executePostStageFunction(stagesMap, 'update')
                    }
                }
            }
            stage('Generate Release Notes') { 
                // This stage generates release notes in github using the github api
                when {
                    expression { return (pushUtils.notSkipStage(stagesMap, 'releaseNotes') && (BRANCH_NAME == 'master') && (configData.releaseNotes == true)) }
                }
                steps {
                    script {
                            pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)

                            pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                    }
                }
            }
            stage('Branch protection') {
                /* This stage updates the branch protection rule for BRANCH releases */
                when{
                    expression { return ((pushUtils.notSkipStage(stagesMap, 'protection')) && (BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/)) }
                }
                steps {
                    script {
                        def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                        pushUtils.branchProtect(repoName, gitHubCred)
                    }
                }
            }
        }
        post {
            always {
                script {
                    postActions(configData)
                    //if (currentBuild.description == 'Code quality process failed') currentBuild.result = 'UNSTABLE'
                    if (deployResultList.any{element -> element == false}) currentBuild.result = 'FAILURE'
                    if (testResultList.any{element -> element == false}) currentBuild.result = 'FAILURE'
                    if (composableArtifactList.any{element -> element == false}) currentBuild.result = 'FAILURE'
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}