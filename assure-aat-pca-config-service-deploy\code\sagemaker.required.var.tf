variable "aws_account_id" {
  description = "aws account id"
  type        = string
  default     = "************"
}

variable "s3_sagemaker_model_gzip_bucket_name" {
  description = "sagemaker s3 bucket name"
  type        = string
  default     = "sagemaker-model-gzip-storage-s3"
}

variable "sagemaker_role" {
  description = "sagemaker role"
  type        = string
  default     = "Sagemaker_test_role"
}

variable "sagemaker_image_aws_region" {
  description = "region of sagemaker image in aws"
  type        = string
  default     = "us-west-2"
}

variable "sagemaker_program" {
  description = "sagemaker program entry"
  type        = string
  default     = "inference"
}

variable "sagemaker_container_log_level" {
  description = "sagemaker container log level"
  type        = string
  default     = "20"
  validation {
    condition     = contains(["10", "20", "30", "40", "50"], var.sagemaker_container_log_level)
    error_message = "Log level must be one of: DEBUG, INFO, WARNING, ERROR, CRITICAL"
  }
}