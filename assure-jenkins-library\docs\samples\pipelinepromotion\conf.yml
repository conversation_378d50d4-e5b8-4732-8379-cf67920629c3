#### Name of current repository for validation. It has to match the name of the repository where this file is.
repositoryName: "my-repository-name"

###### AQL query parameters
artifactory:
  repositories:
    - repository: "diaas-generic"
      type: "generic"
      paths:        
        - path: "assure-platform/my-service"
          branches:            
            - "feature/promotion-pipeline"                      
      status:
        - "ready"               
  deployment_in_progress: "false"

###### Environment Status Maping
status_stage_mapping:
  - status: "ready"
    stage: "development"

###### Microsoft Teams Notifications Webhook
msTeamsWebhook: 'https://cscportal.webhook.office.com/webhookb2/1234-1234@1234-1234/IncomingWebhook/abc-1234/abc-1234'

###### DXC Assure Pull Pipeline Stages Definition
stages:
  - name: "development"
    type: "development"
    status_success: "stable"
    status_error: "in-error"
    customer_name: "dxcassure"
    account_name: "Assure-DEVELOPMENT"
    resource_name: "test-1234-Assure-DEVELOPMENT"
    endpoint: "test-1234.hub-1.development.assure.dxc.com"
    arn: "arn:aws:iam::************:role/role"
    region: "us-east-1"
    environment_type: "test-1234"
    test_token_url: "https://www.test-1234.hub-1.development.assure.dxc.com/api/auth-management/realms"
