#!/usr/bin/env groovy

import org.pdxc.rest.GitApi
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.pdxc.util.Utils
import org.assure.util.WarningUtils
import org.assure.envservice.EnvServiceApi
import org.assure.pushpipeline.PushPipelineUtils
import org.assure.pushpipeline.RustPipelineUtils

/**
 * Pipeline creation and execution.
 *
 * @param stagesMap Specific data for each stage.
 * @param dockerPath Full path and name of a dockerFile to be used on the pipeline. If not provided, default is used.
 * @return void
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'multiRust.Dockerfile') {

    String pipelineName = 'multiRust'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the artifacts generated
    def artifactNames
    def artifactPaths
    // Calculated new version
    String newVersion
    // Name of the dockerFile
    String dockerName
    // Current repository name
    def repoName
    // Multiple Architectures
    def multipleArchitectures
    // File Extension for custom upload
    def fileExtension

    def buildDockerRegistryUrl
    def buildDockerRegistryCreds

    PushPipelineUtils   pushUtils
    RustPipelineUtils   rustUtils

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 1, unit: 'HOURS')
        }

        stages {
            stage('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        configData = pushUtils.pipelineInfoSteps(pipelineName)
                        rustUtils = new RustPipelineUtils()
                        warningUtils = new WarningUtils()
                        prBranch = "${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/
                    }
                }
            }
            stage('Pipeline info') {
                steps {
                    script {
                        dockerName = "${pipelineName}.Dockerfile"
                        rustUtils.setRustDockerAgent(dockerPath, dockerName)
                        (buildDockerRegistryUrl, buildDockerRegistryCreds) = pushUtils.getDockerRegistryUrlAndCreds(configData, dockerName, pipelineName)
                        pushUtils.executePostStageFunction(stagesMap, 'info')
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo("Configured repository name matches current repository: ${repoName}")

                        pushUtils.executePostStageFunction(stagesMap, 'validate')
                    }
                }
            }
            stage('Artifact & Deploy') {
                agent {
                    dockerfile {
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        filename "${pipelineName}.Dockerfile"
                        reuseNode true
                        registryCredentialsId buildDockerRegistryCreds
                        registryUrl buildDockerRegistryUrl
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('Set up') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'setup') } }
                        steps {
                            script {
                                def targetRepoInfo
                                (newVersion, currentVersion, fileExtension, targetRepoInfo, multipleArchitectures) = rustUtils.multiRustSetupSteps(configData, dockerName)
                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Test') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'test') } }
                        steps {
                            script {
                                def clippyExecution = ValuesUtils.getVariable(configData, 'clippyExecution', 'test')

                                rustUtils.rustTestScan(configData, repoName, newVersion)

                                if (clippyExecution != true) {
                                    echo('-- ⚠️ -- Clippy scan was disabled')
                                } else {
                                    rustUtils.rustClippyScan(configData, repoName, newVersion)
                                }

                                pushUtils.executePostStageFunction(stagesMap, 'test')
                            }
                        }
                    }
                    stage('Audit') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'audit') } }
                        steps {
                            script {
                                rustUtils.rustAuditScan(configData, repoName, newVersion)

                                pushUtils.executePostStageFunction(stagesMap, 'audit')
                            }
                        }
                    }
                    stage('Build') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'build') } }
                        steps {
                            script {
                                rustUtils.buildMultipleRustProject(configData, multipleArchitectures)

                                pushUtils.executePostStageFunction(stagesMap, 'build')
                            }
                        }
                    }
                    stage('Zipping artifact') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'zip') } }
                        steps {
                            script {
                                if (fileExtension != '.zip') {
                                    echo('-- ⚠️ -- Desired executable is not on .zip format. Skipping compression...')
                                    artifactNames = rustUtils.getArtifactNames(configData, multipleArchitectures)
                                } else {
                                    artifactNames = rustUtils.compressMultipleArtifacts(configData, newVersion, multipleArchitectures)
                                }

                                pushUtils.executePostStageFunction(stagesMap, 'zip')
                            }
                        }
                    }
                    stage('Upload artifact') {
                        when {
                            expression { pushUtils.notSkipStage(stagesMap, 'upload') }
                            expression { !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/) }
                            expression { artifactNames.size() > 0 }
                        }
                        steps {
                            script {
                                if (fileExtension != '.zip') {
                                    echo('-- 🏭 -- Uploading custom artifact/s.')
                                    artifactPaths = rustUtils.uploadCustomArtifacts(configData, artifactNames, fileExtension, newVersion)
                                } else {
                                    artifactPaths = rustUtils.uploadMultipleArtifacts(configData, artifactNames, fileExtension)
                                }
                                pushUtils.executePostStageFunction(stagesMap, 'upload')
                            }
                        }
                    }
                    stage('Update artifact properties') {
                        when {
                            expression { pushUtils.notSkipStage(stagesMap, 'update') }
                            expression { !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/) }
                            expression { artifactPaths.size() > 0 }
                        }
                        steps {
                            script {
                                rustUtils.updateArtifactProperties(configData, artifactPaths)
                                pushUtils.executePostStageFunction(stagesMap, 'update')
                            }
                        }
                    }
                }
            }
            stage('Generate Release Notes') {
                // This stage generates release notes in github using the github api
                when {
                    expression { return (pushUtils.notSkipStage(stagesMap, 'releaseNotes') && (BRANCH_NAME == 'master') && (configData.releaseNotes == true)) }
                }
                steps {
                    script {
                            pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)

                            pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                    }
                }
            }
            stage('Branch protection') {
                when{
                    expression { return ((pushUtils.notSkipStage(stagesMap, 'protection')) && (BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/)) }
                }
                steps {
                    script {
                        def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                        pushUtils.branchProtect(repoName, gitHubCred)
                    }
                }
            } 
        }
        post {
            always {
                script {
                    postActions(configData)
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}
