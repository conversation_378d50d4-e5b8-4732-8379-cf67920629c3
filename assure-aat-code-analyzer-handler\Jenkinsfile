@Library(['pdxc-pipeline-lib@assure', 'assure-jenkins-library@master']) _ 

import org.pdxc.util.ValuesUtils
import org.pdxc.util.DefaultConfiguration
import org.pdxc.rest.ArtifactoryApi

def functions = [:]

def generateLambdaZips() {
    stage ('Lambda Zips & Upload') {
        def configData = readYaml file: 'conf.yml'
        
        // Common configuration
        def sourceFolder = ValuesUtils.getVariable(configData, 'zipSourceFolder', 'zip')
        def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
        def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'upload')
        if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
        def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'upload')
        def localPath = ValuesUtils.getVariable(configData, 'artifactLocalPath', 'upload')
        def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
        def currentVersion = ValuesUtils.getVariable(configData, 'version', 'setup')
        def newVersion = currentVersion + "+${env.BUILD_NUMBER}"
        ArtifactoryApi artfApi = new ArtifactoryApi(artifactoryURL, cred)
        def props = ValuesUtils.getVariableArrayList(configData, 'artifactProperties', 'update')
         
        println '--ℹ️-- NOTICE: The BUNDLE_VERSION of the lambdas created will be: ' + newVersion
        println '--ℹ️-- NOTICE: The BUNDLE_PATH: ' + "/${repo}/${artifactPath}"

        // Define Lambda components manually based on the repository structure
        def lambdaComponents = [
            [name: "Ingestion", path: "${sourceFolder}/Ingestion/"],
            [name: "LLM", path: "${sourceFolder}/LLM/"],
            [name: "Database_table_project", path: "${sourceFolder}/Database/table_project/"],
            [name: "Database_table_prompt", path: "${sourceFolder}/Database/table_prompt/"]
        ]
        
        // Process each Lambda component
        lambdaComponents.each { component ->
            // Check if the directory exists
            if (fileExists(component.path)) {
                // Create a temporary directory for this Lambda
                sh "mkdir -p temp/${component.name}"
                
                // Copy Lambda files to temp directory
                sh "cp -r ${component.path}* temp/${component.name}/"
                
                // Zipping
                zip zipFile: "${component.name}.${newVersion}.zip", dir: "temp/${component.name}"
                
                // Uploading to artifactory       
                functiongroup_artifactory.uploadGenericArtifact(cred, repo, artifactPath,
                                            component.name + ".${newVersion}" + '.zip', localPath, artifactoryURL)
                artfApi.updateArtifactProperties(repo, artifactPath, component.name + ".${newVersion}" + '.zip', props)
                println '--ℹ️-- UPLOADED: ' + component.name + ".${newVersion}" + '.zip'
            } else {
                println '--⚠️-- WARNING: Directory not found: ' + component.path
            }
        }
        
        // Clean up temp directory
        sh "rm -rf temp"
    }
}

// Define custom function for setup stage
functions['setup'] = ["skip": false, "func": this.&generateLambdaZips]

// Skip all standard stages that we don't need
functions['install'] = ['skip': false]
functions['test'] = ['skip': true]
functions['zip'] = ['skip': true]
functions['upload'] = ['skip': true]
functions['update'] = ['skip': true]

// Call the pipelinePython function with our configuration
pipelinePython(functions)
