#!/usr/bin/env groovy

import org.pdxc.rest.GitApi
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.util.DefaultConfiguration
import org.pdxc.util.FileUtils
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.pdxc.util.Utils
import org.assure.util.AssureUtils
import org.assure.util.WarningUtils
import org.assure.envservice.EnvServiceApi
import org.assure.pullpipeline.PullPipelineUtils
import org.assure.pushpipeline.PushPipelineUtils
import groovy.json.JsonOutput

import java.text.SimpleDateFormat


boolean validDeployCustom(String branch, Map configData) {

    def isValid = (ValuesUtils.getVariable(configData, 'deployment', 'custom') == true) && 
            (branch ==~ /(^feature\/{1}+.*)/ ||
            branch ==~ /(^fix\/{1}+.*)/ ||
            branch ==~ /(^prepare\/{1}+.*)/ ||
            branch == 'development'
            ) || (("${branch}" == 'master') && (ValuesUtils.getVariable(configData, 'deployment_from_master', 'custom') == true))

    return isValid
}

/**
 * Pipeline creation and execution.
 *
 * @param stagesMap Specific data for each stage.
 * @param dockerPath Full path and name of a dockerFile to be used on the pipeline. If not provided, default is used.
 * @return void
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'lambda.Dockerfile') {
    String pipelineName = 'lambda'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the artifact generated
    String artifactName
    // Calculated new version
    String newVersion
    // Name of the dockerFile
    String dockerName
    // Current repository name
    def repoName
    // QA vars
    def sonarStatusCode = 0
    def checkovStatusCode = 0

    // def testResults = true
    def deployResultList = []
    def testResultList = []
    def composableArtifactList = []

    PushPipelineUtils   pushUtils
    EnvServiceApi       envService

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 1, unit: 'HOURS')
        }

        stages {
            stage ('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        warningUtils = new WarningUtils()
                        dockerName = "${pipelineName}.Dockerfile"
                    }
                }
            }
            stage('Pipeline info') {
                steps {
                    script {
                       def conf = libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
                        writeFile file: "${pipelineName}-conf.yml", text: conf
                        def defaultConfigYml = readYaml file: "${pipelineName}-conf.yml"
                        def repoData = readYaml file: 'conf.yml'

                        configData = defaultConfigYml + repoData
                        configData = pushUtils.writeConfigData(configData, 'generic')

                        println 'Loaded configuration values: \n\n' + JsonOutput.prettyPrint(JsonOutput.toJson(configData))

                        writeYaml file: 'conf.yml', data: configData, overwrite: true

                        pushUtils.setDockerAgentForCustomNodeAndNpmVersion(dockerPath, dockerName)
                        (buildDockerRegistryUrl, buildDockerRegistryCreds) = pushUtils.getDockerRegistryUrlAndCreds(configData, dockerName, pipelineName)

                        pushUtils.executePostStageFunction(stagesMap, 'info')
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo "Configured repository name matches current repository: ${repoName}"

                        pushUtils.executePostStageFunction(stagesMap, 'validate')
                    }
                }
            }
            stage ('Artifact & Deploy') {
                agent {
                    dockerfile {
                        args '-u root:root'
                        filename 'lambda.Dockerfile'
                        reuseNode true
                        registryCredentialsId buildDockerRegistryCreds
                        registryUrl buildDockerRegistryUrl
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('Set up') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'setup') } }
                        steps {
                            script {
                                // Configure Git global data
                                def cred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'setup')
                                def mail = ValuesUtils.getVariable(configData, 'gitEmail', 'setup')
                                def user = ValuesUtils.getVariable(configData, 'gitUsername', 'setup')
                                def url = ValuesUtils.getVariable(configData, 'gitHubUrl', 'setup')
                                functiongroup_git.setup(cred, mail, user, url)

                                // Configure npm rc data
                                cred = ValuesUtils.getVariable(configData, 'npmrcCredential', 'setup')
                                functiongroup_npm.setup(cred)

                                def (npmAuthToken, uuid) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.assure_creds_file, "creds/assureCustomCreds.yml.gpg")
                                                                                             
                                wrap([$class: 'MaskPasswordsBuildWrapper', varPasswordPairs: [[password: npmAuthToken.npm_auth_token]]]) {
                                    withEnv(["NPM_AUTH_TOKEN=${npmAuthToken.npm_auth_token}"]) {   
                                        if (configData.npmVersion >= 8) {
                                            sh script: 'sed -i "/^_auth/d" ~/.npmrc', label: "Remove auth line"
                                        }                                                                         
                                        sh script: 'echo "//artifactory.csc.com/artifactory/api/npm/diaas-npm-local/:_authToken=$NPM_AUTH_TOKEN" >> ~/.npmrc', label: 'Set up token NPMRC file'                                        
                                    }
                                }

                                // Check if package-lock.json exists
                                def packageLockPath = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'setup')                               
                                packageLockPath = ValuesUtils.removeStartEndChars(packageLockPath, '.', true, true)                               
                                packageLockPath = ValuesUtils.removeStartEndChars(packageLockPath, '/', true, true)                               
                                if (packageLockPath != "") packageLockPath += '/'                                
                                def localPackageLockPath = "${WORKSPACE}/" + packageLockPath + 'package-lock.json'                                
                                def exists = fileExists localPackageLockPath                                
                                if (exists) {                                                                                                               
                                    sh script: """
                                                    set +x
                                                    echo '${npmAuthToken.npm_creds}' >> ~/.npmrc                                                    
                                                    set -x                                                    
                                            """, label: 'Set up creds NPMRC file'                                    
                                }

                                // Calculate and set new version to be built
                                echo 'Calculate and set new version:'
                                // Mover a utils
                                def file = ValuesUtils.getVariable(configData, 'dependenciesPackageFile', 'setup')
                                def attr = ValuesUtils.getVariable(configData, 'dependenciesPackageAttribute', 'setup')
                                def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'setup')
                                def currentVersion = FileUtils.getAttributeFromJsonFile(file, attr, path)
                                newVersion = currentVersion + "+${env.BUILD_NUMBER}"
                                FileUtils.setValueOnJSONFileContent(file, attr, newVersion, path)
                                echo "Current Version: ${currentVersion} --- New Version: ${newVersion}"

                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Checkov Scan') { //! TMP: Move to Quality Gate Stage after Checkov is fully implemented
                        when { expression { pushUtils.notSkipStage(stagesMap, 'checkov') } }
                        steps {
                            script {

                                def checkovQualityGate = ValuesUtils.getVariable(configData, 'checkovQualityGate', 'checkov')
                                def (checkovData, uuidCheckov) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.checkov_conf_file, "checkov/checkovConfiguration.yml.gpg")
                                def scriptPath = "checkov/checkov.sh"

                                pushUtils.checkovScan('nodejs', repoName, repoName, scriptPath, uuidCheckov, dockerName)

                                //! Removed for temp fix
                                // def checkName = 'Vulnerabilities'                                                
                                // withChecks("${checkName}") {
                                //     junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: "**/results*.xml")
                                // }
                                // pushUtils.markGitHubCheckNeutral(configData, checkName)

                                sh script: "rm -rf ./results", label: 'Remove results folder' //! TMP: Remove after Checkov is fully implemented

                                checkovStatusCode = pushUtils.checkovErrorCheck(repoName, uuidCheckov)
                                
                                if(checkovStatusCode != 0 && checkovQualityGate){
                                    currentBuild.result = 'ABORTED'
                                    error ("-- ❌ -- Pipeline ABORTED ❗❗ Checkov Scan 🛸 status: FAIL (checkovQualityGate is ${checkovQualityGate})")
                                    return
                                }
                                
                                catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                    if(checkovStatusCode != 0){
                                        error("-- ⚠️ -- Checkov Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                                        return
                                    }
                                }
                                pushUtils.executePostStageFunction(stagesMap, 'checkov')
                                
                            }
                        }
                    }
                    stage('Test') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'test') } }
                        steps {
                            script {
                                def commands = ValuesUtils.getVariableArrayList(configData, 'testCommands', 'test')

                                def dependenciesPath = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'test')
                                dependenciesPath = (dependenciesPath == null || dependenciesPath == '') ? '.' : dependenciesPath
                                def dependenciesPathModified = ValuesUtils.removeStartEndChars(dependenciesPath, '/', true, true)

                                commands.each { command -> 
                                    sh script: """  
                                                    cd ${dependenciesPathModified}
                                                    ${command}""", 
                                        label: "Execute command: ${command}"
                                }

                                sh """
                                        cd ${dependenciesPathModified}
                                        rm -rf node_modules
                                   """
                            }
                        }
                        post {
                            always {
                                script {
                                    junit(allowEmptyResults: true, testResults: '**/*.xml')
                                }
                            }
                            success {
                                script {
                                    pushUtils.executePostStageFunction(stagesMap, 'test')
                                }
                            }
                        }
                    }
                    stage('Install') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'install') } }
                        steps {
                            script {
                                def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'install')
                                def scriptName = ValuesUtils.getVariable(configData, 'scriptName', 'install')
                                def scriptParams = ValuesUtils.getVariable(configData, 'scriptParams', 'install')
                                functiongroup_npm.executeScript(path, scriptName, scriptParams)

                                pushUtils.executePostStageFunction(stagesMap, 'install')
                            }
                        }
                    }
                    stage('Zipping artifact') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'zip') } }
                        steps {
                            script {
                                def zipScript = ValuesUtils.getVariable(configData, 'zipScript', 'zip')
                                def zipInclude = ValuesUtils.getVariable(configData, 'zipInclude', 'zip')
                                if (zipInclude == null) zipInclude = ''
                                def sourceFolder = ValuesUtils.getVariable(configData, 'zipSourceFolder', 'zip')
                                artifactName = ValuesUtils.getVariable(configData, 'targetZipName', 'zip') + ".${newVersion}"
                                //Delete if file already exist
                                sh script: "rm -rf ${artifactName}.zip", label: 'Delete old version of zip file'
                                if (zipScript != "" && zipScript != null) {
                                    sh script: "${zipScript} ${artifactName}.zip ${sourceFolder} ${zipInclude}", label: 'Zip file using custom script'
                                } else {
                                    zip glob: "${zipInclude}", zipFile: "${artifactName}.zip", dir: "${sourceFolder}"
                                }
                                pushUtils.executePostStageFunction(stagesMap, 'zip')
                            }
                        }
                    }
                    stage('Code quality'){
                        when { expression { pushUtils.notSkipStage(stagesMap, 'codequality') } }
                        parallel {
                            stage('OSS Governance') {
                                when { expression { pushUtils.notSkipStage(stagesMap, 'ossgovernance') } }                                
                                steps {
                                    script {    
                                        catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                            try{ 
                                                def nodeVersion = ValuesUtils.getVariable(configData, 'nodeVersion')
                                                if (nodeVersion.toInteger() < 20) {
                                                    echo "-- ℹ️ -- Node version not supported (requires 20+), skipping OSS Governance"
                                                } else {
                                                    def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'install')
                                                    String pathModified = ValuesUtils.removeStartEndChars(path, '/', true, true)
                                                    pathModified = pathModified.replace('./', '')
                                                    pathModified = pathModified.replace('.', '')

                                                    sh ( script: "npm install -g license-report", label: "Installing license report package")

                                                    def reportCss = libraryResource(resource: "styles/license-report.css")
                                                    writeFile(file: "${WORKSPACE}/license-report.css", text: reportCss)
                                                    
                                                    sh script: """
                                                                cd ./${pathModified}
                                                                license-report --output=html --html.cssFile=${WORKSPACE}/license-report.css --csvHeaders --fields=name --fields=licenseType --fields=installedVersion --fields=link > ${WORKSPACE}/ossGovernanceInfo.html
                                                            """,                                                
                                                    returnStdout: true,
                                                    label: 'Executing oss governance script'

                                                    // Add title to report
                                                    sh script: """
                                                        sed -i 's|<body>|<body><h1 style="text-align:center;margin:30px 0 10px;font-size:22px;">OSS Governance License Report</h1>|' ossGovernanceInfo.html
                                                    """, label: 'Inject title into report'

                                                    if (fileExists("${WORKSPACE}/ossGovernanceInfo.html")) {
                                                        archiveArtifacts artifacts: "ossGovernanceInfo.html"
                                                        sh script: "rm -f ${WORKSPACE}/ossGovernanceInfo.html", label: "Removing ossGovernanceInfo.html"
                                                    }     

                                                    sh script: "rm -f ${WORKSPACE}/license-report.css", label: "Removing license-report.css"
                                                }
                                            } catch (e) {
                                                echo "--❌️-- Error detected during OSS Governance process ${e.getMessage()}"
                                                throw e 
                                            }
                                        }
                                        pushUtils.executePostStageFunction(stagesMap, 'ossgovernance')                    
                                    }
                                }
                            }
                            stage ('NPM Audit') {
                                steps {
                                    catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                        script {
                                            try {
                                                def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'install')
                                                String pathModified = ValuesUtils.removeStartEndChars(path, '/', true, true)
                                                pathModified = pathModified.replace('./', '')
                                                pathModified = pathModified.replace('.', '')

                                                def npmAuditResult = sh(
                                                    script: """
                                                        cd ./${pathModified}
                                                        npm audit --no-unicode --no-color || true
                                                    """, 
                                                    returnStdout: true,
                                                    label: "Executing npm audit for ${repoName}").trim()
                                                
                                                if (npmAuditResult != null || npmAuditResult != '') {
                                                    writeFile file: "${repoName}-${newVersion}_NPM-audit.log", text: npmAuditResult
                                                }

                                                if ("${BRANCH_NAME}" == 'master') {                                                

                                                    def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential')
                                                    GitApi gitApi = GitApi.getInstance(DefaultConfiguration.PDXC_GITHUB_URL, gitHubCred)
                                                    
                                                    def dependaBotUrl = "${DefaultConfiguration.PDXC_GITHUB_URL}/api/v3/repos/assure/${repoName}/dependabot/alerts?state=open"
                                                    def response = gitApi.get(dependaBotUrl, [200])

                                                    def (advisories, metadata) = AssureUtils.dependaBotParse(response)

                                                    def sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mmXXX").format(new Date())
                                                                                                        
                                                    def npmAuditMap = [:]
                                                    npmAuditMap.put("bundle_name", "${artifactName}.zip")
                                                        npmAuditMap.put("github_repository", repoName)
                                                        npmAuditMap.put("date", sdf)                                                                                                                                                                                               
                                                        npmAuditMap.put("metadata", metadata)                                                
                                                        npmAuditMap.put("vulnerabilities_list", advisories)

                                                    def preparednpmAuditJson = JsonOutput.toJson(npmAuditMap)
                                                    writeJSON file: "./vulnerabilities-audit-${repoName}.json", json: preparednpmAuditJson

                                                    archiveArtifacts artifacts: "**/vulnerabilities-audit-${repoName}.json"

                                                    def (customCreds, uuid) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.assure_creds_file, "creds/assureCustomCreds.yml.gpg")

                                                    def convertJsonFile = libraryResource(resource: "pythonScripts/jsontodynamorecord.py")
                                                    writeFile(file: "jsontodynamorecord.py", text: convertJsonFile)

                                                    sh script: """
                                                        pip install dynamodb-json
                                                        python3 jsontodynamorecord.py --path ./ --name vulnerabilities-audit-${repoName}.json
                                                        rm -rf jsontodynamorecord.py
                                                    """, returnStatus: true, label: '-- ⚙️-- Convert json to dynamo record'

                                                    withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: 'ASSURE-AWS-CLI-RW', accessKeyVariable: 'AWS_ACCESS_KEY_ID', secretKeyVariable: 'AWS_SECRET_ACCESS_KEY']]) {
                                                        withAWS(role: customCreds.roleAWS) {
                                                            def tableName = 'vulnerabilities-dependabot'
                                                            sh """
                                                                aws --version                                                            
                                                                aws dynamodb put-item --table-name ${tableName} --item file://./vulnerabilities-audit-${repoName}.json --region us-east-2
                                                            """                                                        
                                                        }
                                                    }
                                                }

                                                def npmLogExists = fileExists("${repoName}-${newVersion}_NPM-audit.log")

                                                if (npmLogExists) {
                                                    archiveArtifacts artifacts: "${repoName}-${newVersion}_NPM-audit.log"
                                                    echo '-- 📰 -- NPM Audit log saved'
                                                    sh "rm -rf ${repoName}_npmAudit"
                                                }

                                                catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                    if(npmLogExists != true){
                                                        error("-- ⚠️ -- NPM Audit log creation error")
                                                        return
                                                    }
                                                }
                                            } catch (Exception e) {
                                                echo "--❌️-- Error detected during npm audit process ${e}"                                                
                                            }
                                        }
                                    }
                                }
                            }
                            stage('SonarQube Scan') {
                                when { expression { pushUtils.notSkipStage(stagesMap, 'sonar') } }
                                    steps {
                                        script {
                                            withCredentials([string(credentialsId:'ASSURE-SONAR-HOST', variable:'SONARHOST')]) {
                                                withCredentials([string(credentialsId:'ASSURE-SONAR-TOKEN', variable:'SONARTOKEN')]) {
                                                    sonarStatusCode = pushUtils.sonarScan(SONARHOST, SONARTOKEN, configData, true, true, dockerName)
                                                }
                                            }
                                        }
                                    }
                            }
                            // stage('Checkov Scan') { //! TMP: Restore after Checkov is fully implemented
                            //     when { expression { pushUtils.notSkipStage(stagesMap, 'checkov') } }
                            //     steps {
                            //         script {

                            //             def checkovQualityGate = ValuesUtils.getVariable(configData, 'checkovQualityGate', 'checkov')
                            //             def (checkovData, uuidCheckov) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.checkov_conf_file, "checkov/checkovConfiguration.yml.gpg")
                            //             def scriptPath = "checkov/checkov.sh"

                            //             pushUtils.checkovScan('generic', repoName, repoName, scriptPath, uuidCheckov, dockerName)

                            //             def checkName = 'Vulnerabilities'                                                
                            //             withChecks("${checkName}") {
                            //                 junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: "**/results*.xml")
                            //             }
                            //             pushUtils.markGitHubCheckNeutral(configData, checkName)

                            //             checkovStatusCode = pushUtils.checkovErrorCheck(repoName, uuidCheckov)
                                        
                            //             if(checkovStatusCode != 0 && checkovQualityGate){
                            //                 currentBuild.result = 'ABORTED'
                            //                 error ("-- ❌ -- Pipeline ABORTED ❗❗ Checkov Scan 🛸 status: FAIL (checkovQualityGate is ${checkovQualityGate})")
                            //                 return
                            //             }
                                        
                            //             catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                            //                 if(checkovStatusCode != 0){
                            //                     error("-- ⚠️ -- Checkov Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                            //                     return
                            //                 }
                            //             }
                            //             pushUtils.executePostStageFunction(stagesMap, 'checkov')
                                        
                            //         }
                            //     }
                            // }
                        }
                        post {
                            success {
                                script {
                                    pushUtils.executePostStageFunction(stagesMap, 'codequality')
                                }
                            }
                        }
                    }
                    stage('Upload artifact') {
                        when { expression { return ((pushUtils.notSkipStage(stagesMap, 'upload')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                        steps {
                            script {
                                def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
                                def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'upload')
                                if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                                def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'upload')
                                def localPath = ValuesUtils.getVariable(configData, 'artifactLocalPath', 'upload')
                                def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
                                functiongroup_artifactory.uploadGenericArtifact(cred, repo, artifactPath,
                                        artifactName + '.zip', localPath, artifactoryURL)

                                pushUtils.executePostStageFunction(stagesMap, 'upload')
                            }
                        }
                    }
                    stage('Update artifact properties') {
                        when { expression { return ((pushUtils.notSkipStage(stagesMap, 'update')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                        steps {
                            script {
                                    def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'update')
                                    if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                                    def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'update')
                                    ArtifactoryApi artfApi = new ArtifactoryApi(artifactoryURL, cred)

                                    def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'update')
                                    def props = ValuesUtils.getVariableArrayList(configData, 'artifactProperties', 'update')
                                    def notSkipQa     = pushUtils.notSkipStage(stagesMap, 'codequality')
                                    def notSkipCheckov  = pushUtils.notSkipStage(stagesMap, 'checkov')
                                    def notSkipSonar  = pushUtils.notSkipStage(stagesMap, 'sonar')

                                    if(!notSkipQa || !notSkipCheckov  || !notSkipSonar) {
                                        configData.artifactProperties.add([prop: 'qa', value: 'skip'])  
                                    }
                                    else if (checkovStatusCode  != 0 || sonarStatusCode != 0 ){
                                            configData.artifactProperties.add([prop: 'qa', value: 'fail'])
                                    }
                                    else
                                        configData.artifactProperties.add([prop: 'qa', value: 'pass'])
                                                                
                                    def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'update')
                                    artfApi.updateArtifactProperties(repo, artifactPath, artifactName + '.zip', props)

                                    pushUtils.executePostStageFunction(stagesMap, 'update')
                            }
                        }
                    }
                }
            }
            stage('Deploy custom') {
                when { expression { return ((pushUtils.notSkipStage(stagesMap, 'deploy') && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/))) } }
                steps {
                    catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
                        script {
                            if (validDeployCustom("${BRANCH_NAME}", configData) == true) {
                                                                
                                def envServFile = libraryResource(resource: "${configData.env_service_conf_file}.gpg", encoding: "Base64")
                                writeFile(file: "${configData.env_service_conf_file}.gpg", text: envServFile, encoding: "Base64")

                                def envServData = AssureUtils.decryptYmlFile(configData.passphrase_id, configData.env_service_conf_file, '.')

                                configData.put('version', "${newVersion}")
                                configData.put('extension', 'zip')
                                configData = envServData + configData

                                def artefact = PullPipelineUtils.composeArtifact(configData)

                                envService = new EnvServiceApi(configData.environment_service_url, configData.environment_service_credential, configData.oauth_host, configData.env_api_key)
                                def deploymentPackagesResponse = envService.getWithCustomAuth(configData.environment_service_url + '/deployment-packages?artefact=' + artefact.uniquename)
                                envService.setToken()

                                def targetEnvs
                                def artifacts = []

                                targetEnvs = configData.findAll { k, v -> k.endsWith("_environment_resource_name") }
                                def tempTargets = targetEnvs.findAll{ it.key != "sdlc-environment_environment_resource_name" }
                                targetEnvs = tempTargets

                                // Check if environment is locked
                                def environments = envService.getWithCustomAuth(configData.environment_service_url + "/environments")._links.item

                                environments.findAll { item ->                                                    
                                    targetEnvs.each { k,v ->                                                            
                                            if (item.summary.resource_name == v && item.summary.is_locked == true) {
                                                targetEnvs.remove(k)
                                                echo "-- ⚠️🚮 -- Your environment ${v} is locked"                                                                
                                            }
                                    }
                                }

                                def parallelStages = []
                                def stageNumber = 0
                                parallelStages[stageNumber] = [:]
                                targetEnvs.keySet().eachWithIndex { targetEnv, targetEnvIndex ->
                                    if (targetEnvIndex % 10 == 0){
                                        stageNumber += 1
                                        parallelStages[stageNumber] = [:]
                                    }

                                    artefact = PullPipelineUtils.composeArtifact(configData)

                                    def artifactUniqueName = AssureUtils.getFromRegEx(artefact.name, configData.regex_artifact_uniquename)
                                    def environment = ValuesUtils.removeStartEndChars(targetEnv, '_environment_resource_name', false, true)
                                    def urlPath = envService.composeURLPath(configData, environment, true)

                                    deploymentPackagesResponse._links.item.each { deploymentPackage ->
                                        def mergeMap = [:]
                                        mergeMap.put('uniquename', artifactUniqueName)
                                        mergeMap.putAll(deploymentPackage.summary)
                                        mergeMap.putAll(artefact)
                                        def (result, invalidArtifacts) = PullPipelineUtils.composeAndValidateArtefact(mergeMap, configData)
                                        echo "❌ Discarded ❌ : ${invalidArtifacts}"
                                        if (result != []) artifacts.add(result)
                                    }

                                    echo "🏆 Artefacts 🏆 : ${artifacts}"

                                    parallelStages[stageNumber].put(
                                        "${environment}",
                                        {
                                            stage("Env ${environment}"){
                                                catchError(
                                                        buildResult: 'SUCCESS',
                                                        stageResult: 'FAILURE'
                                                    ) {
                                                        script {
                                                            try {
                                                                artifacts.each { artifact ->
                                                                    try {
                                                                        stage(artifact.service_name) {
                                                                            catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
                                                                                script {
                                                                                    Map deployment = [:]
                                                                                    def dpNotFound = false
                                                                                    try {
                                                                                        def env = ValuesUtils.getVariable(configData, 'environment_resource_name', environment)
                                                                                        String urlLastDeploy = "deployment-packages/${artifact.service_name}/deployments/${artifact.service_name}-${env}"
                                                                                        def path = envService.composeURLPath(configData, environment, true)
                                                                                        def responseLastDeploymentData = envService.getWithCustomAuthNotFound(configData.environment_service_url + '/' + path + '/' + urlLastDeploy)  

                                                                                        if (responseLastDeploymentData?.messages != null && responseLastDeploymentData?.messages[0]?.message.contains('not found')) {
                                                                                            echo "-- ⚠️🚮 -- Artifact ${artifact.name} will not be processed. Deployment package ${artifact.service_name} is not available in target environment ${env}"
                                                                                            dpNotFound = true
                                                                                            deployment.put('success', true)
                                                                                            testResult = true
                                                                                        }
                                                                                        else {
                                                                                            deployment = envService.deploy(environment, artifact.service_name, [artifact], configData)
                                                                                            deployResultList.add(deployment?.success)
                                                                                        }
                                                                                    } catch (Exception e) {
                                                                                        echo "--❌️-- Error detected during deployment of ${artifact.name} for deployment package ${artifact.service_name}"
                                                                                        if (dpNotFound == false) {
                                                                                            deployResultList.add(false)
                                                                                            Utils.throwExceptionInstance("Failure during deployment ${artifact.name}")
                                                                                        }
                                                                                    }

                                                                                    if (dpNotFound == false) {
                                                                                        if (ValuesUtils.getVariable(configData, 'skipPostDeployTest', "${environment}") == false) {
                                                                                            def testResult = false
                                                                                            if (deployment?.success) {                                                                                                                                        
                                                                                                pushUtils.prepareParamsTesting(environment, configData)
                                                                                                testResult = PullPipelineUtils.runTest(environment, environment, artifact, environment, 'custom', configData)
                                                                                                testResultList.add(testResult)                                                                    
                                                                                            } else {
                                                                                                echo '-- ⏭️-- Skipping testing due to deployment error'
                                                                                            }

                                                                                            if (!deployment?.success || !testResult) {
                                                                                                echo "--❌️-- Error detected during deployment / testing of ${artifact.name} for deployment package ${artifact.service_name}"
                                                                                                Utils.throwExceptionInstance("Failure during deployment and testing of ${artifact.name}")
                                                                                            } else {
                                                                                                echo "--🥳-- ${artifact.name} successfully deployed and tested"
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    } catch (Exception e) {
                                                                        echo "-- ❌ -- Failure during Deploy and Test of ${artifact.name} to environment ${targetEnv}"
                                                                        deployResultList.add(false)
                                                                    }
                                                                }
                                                            } catch (Exception e) {
                                                                error("-- ❌ -- Error during deployment to ${targetEnv}. Please check logs. Error: ${e}")                                                
                                                            }
                                                        }
                                                    }
                                            }
                                        }
                                    )
                                }
                                parallelStages.each { stage -> 
                                    parallel stage
                                }
                            }
                            else {
                                echo 'Custom deploy is disabled'
                            }

                            pushUtils.executePostStageFunction(stagesMap, 'deploy')
                        }
                    }
                }
            }     
            stage('Generate Release Notes') { 
                // This stage generates release notes in github using the github api
                when {
                    expression { return (pushUtils.notSkipStage(stagesMap, 'releaseNotes') && (BRANCH_NAME == 'master') && (configData.releaseNotes == true)) }
                }
                steps {
                    script {
                            pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)

                            pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                    }
                }
            }
            stage('Branch protection') {
                /* This stage updates the branch protection rule for BRANCH releases */
                when{
                    expression { return ((pushUtils.notSkipStage(stagesMap, 'protection')) && (BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/)) }
                }
                steps {
                    script {
                        def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                        pushUtils.branchProtect(repoName, gitHubCred)
                    }
                }
            }
        }
        post {
            always {
                script {
                    postActions(configData)
                    if (currentBuild.description == 'Code quality process failed') currentBuild.result = 'UNSTABLE'
                    if (deployResultList.any{element -> element == false}) currentBuild.result = 'FAILURE'
                    if (testResultList.any{element -> element == false}) currentBuild.result = 'FAILURE'
                    if (composableArtifactList.any{element -> element == false}) currentBuild.result = 'FAILURE'
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}