#!/usr/bin/env groovy

import org.pdxc.rest.GitApi
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.DefaultConfiguration
import org.pdxc.util.ValuesUtils
import org.pdxc.util.Utils
import org.pdxc.util.FileUtils
import org.assure.util.WarningUtils
import org.assure.envservice.EnvServiceApi
import org.assure.pushpipeline.PushPipelineUtils
import groovy.json.JsonOutput


/**
 * Pipeline creation and execution.
 *
 * @param stagesMap Specific data for each stage.
 * @param dockerPath Full path and name of a dockerFile to be used on the pipeline. If not provided, default is used.
 * @return void
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'javadocker.Dockerfile') {
    String pipelineName = 'javadocker'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the artifact generated
    String artifactName
    // Calculated new version
    String newVersion
    // Current repository name
    def repoName
    // Dockerfile name
    def dockerName
       
    PushPipelineUtils   pushUtils
    EnvServiceApi       envService

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 2, unit: 'HOURS')
        }

        stages {
            stage ('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        warningUtils = new WarningUtils()
                        dockerName = "${pipelineName}.Dockerfile"
                    }
                }
            }
            stage('Pipeline info') {
                steps {
                    script {
                       def conf = libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
                        writeFile file: "${pipelineName}-conf.yml", text: conf
                        def defaultConfigYml = readYaml file: "${pipelineName}-conf.yml"
                        def repoData = readYaml file: 'conf.yml'

                        configData = defaultConfigYml + repoData
                        configData = pushUtils.writeConfigData(configData, 'docker')

                        println 'Loaded configuration values: \n\n' + JsonOutput.prettyPrint(JsonOutput.toJson(configData))

                        writeYaml file: 'conf.yml', data: configData, overwrite: true

                        pushUtils.setDockerAgentJava(dockerPath, dockerName)
                        (buildDockerRegistryUrl, buildDockerRegistryCreds) = pushUtils.getDockerRegistryUrlAndCreds(configData, dockerName, pipelineName)

                        pushUtils.executePostStageFunction(stagesMap, 'info')
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo "Configured repository name matches current repository: ${repoName}"

                        pushUtils.executePostStageFunction(stagesMap, 'validate')
                    }
                }
            }
            stage ('Artifact & Deploy') {
                agent {
                    dockerfile {
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        filename 'javadocker.Dockerfile'
                        reuseNode true
                        registryCredentialsId buildDockerRegistryCreds
                        registryUrl buildDockerRegistryUrl
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('Set up') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'setup') } }
                        steps {
                            script {
                                 // Configure Git global data
                                def cred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'setup')
                                def mail = ValuesUtils.getVariable(configData, 'gitEmail', 'setup')
                                def user = ValuesUtils.getVariable(configData, 'gitUsername', 'setup')
                                def url = ValuesUtils.getVariable(configData, 'gitHubUrl', 'setup')
                                functiongroup_git.setup(cred, mail, user, url)

                                // Calculate and set new version to be built
                                echo 'Calculate and set new version:'
                                def file = ValuesUtils.getVariable(configData, 'dependenciesPackageFile', 'setup')
                                def attr = ValuesUtils.getVariable(configData, 'dependenciesPackageAttribute', 'setup')
                                def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'setup')
                                path = (path == null || path == '') ? '.' : path
                                
                                def mavenPom = readMavenPom file: "${path}/pom.xml"
                                def currentVersion = mavenPom.getVersion()

                                newVersion = currentVersion + "-${env.BUILD_NUMBER}"
                                sh "sed -i 's#<${attr}>${currentVersion}</${attr}>#<${attr}>${newVersion}</${attr}>#' ${path}/pom.xml"
                                                                
                                echo "Current Version: ${currentVersion} --- New Version: ${newVersion}"
                                configData.put('artifactoryTag', "${newVersion}")
                                writeYaml file: 'conf.yml', data: configData, overwrite: true
                                
                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Test') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'test') } }
                        steps {
                            script {
                                def commands = ValuesUtils.getVariableArrayList(configData, 'testCommands', 'test')

                                def dependenciesPath = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'test')
                                dependenciesPath = (dependenciesPath == null || dependenciesPath == '') ? '.' : dependenciesPath
                                def dependenciesPathModified = ValuesUtils.removeStartEndChars(dependenciesPath, '/', true, true)

                                commands.each { command -> 
                                    sh script: """  
                                                    cd ${dependenciesPathModified}
                                                    ${command}""", 
                                        label: "Execute command: ${command}"
                                }
                            }
                        }
                        post {
                            always {
                                script {
                                    junit(allowEmptyResults: true, testResults: '**/*.xml')
                                }
                            }
                            success {
                                script {
                                    pushUtils.executePostStageFunction(stagesMap, 'test')
                                }
                            }
                        }
                    }
                    stage('Install') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'install') } }
                        steps {
                            script {
                                def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'install')
                                def scriptName = ValuesUtils.getVariable(configData, 'scriptName', 'install')
                                def scriptParams = ValuesUtils.getVariable(configData, 'scriptParams', 'install')

                                pushUtils.executeScript(path, scriptName, scriptParams)

                                pushUtils.executePostStageFunction(stagesMap, 'install')
                            }
                        }
                    }
                    stage('Code quality'){
                        when { expression { pushUtils.notSkipStage(stagesMap, 'codequality') } }
                        parallel{
                            stage('SonarQube Scan') {
                                when { expression { pushUtils.notSkipStage(stagesMap, 'sonar') } }
                                    steps {
                                        script {
                                            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                def javaVersion = ValuesUtils.getVariable(configData, 'javaVersion', 'sonar')

                                                if (javaVersion != 8) { //! Temporary warn until JDK 8 is deprecated from our pipelines.
                                                    withCredentials([string(credentialsId:'ASSURE-SONAR-HOST', variable:'SONARHOST')]) {
                                                        withCredentials([string(credentialsId:'ASSURE-SONAR-TOKEN', variable:'SONARTOKEN')]) {
                                                            pushUtils.sonarJavaScan(SONARHOST, SONARTOKEN, configData, false, false, dockerName)
                                                        }
                                                    }
                                                } else {
                                                    error("-- ⚠️ -- SonarQube is not supported for JDK version 8 and will be deprecated soon. Please check the documentation and update your JDK to a newer and more stable version.")
                                                }
                                            }
                                        }
                                    }
                            }
                        }
                        post {
                            success {
                                script {
                                    pushUtils.executePostStageFunction(stagesMap, 'codequality')
                                }
                            }
                        }
                    }
                    stage('Build Docker Image') {                        
                        when { expression { return ((pushUtils.notSkipStage(stagesMap, 'upload')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                        steps {
                            script {
                                def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
                                def path = ValuesUtils.getVariable(configData, 'artifactPath', 'upload').toLowerCase()
                                def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'upload')
                                artifactName = ValuesUtils.getVariable(configData, 'artifactoryFileName', 'upload')
                                def tag = "${newVersion}"
                                def dockerFile = ValuesUtils.getVariable(configData, 'buildFromDockerFile', 'upload')
                                def url = ValuesUtils.getVariable(configData, 'artifactoryUrl', 'upload')
                                def targetPath = ValuesUtils.getVariable(configData, 'buildTarget', 'upload')
                                
                                targetPath = (targetPath != null) ? targetPath : '.'

                                def skipCheckov = (pushUtils.notSkipStage(stagesMap, 'checkov') == true) ? configData.put('skip', false) : configData.put('skip', true)

                                def (dockerJsonUrl, checkovStatusCode) = pushUtils.uploadDockerArtifact(cred, repo + '/' + path, artifactName, tag, dockerFile, targetPath, url, configData, dockerName)
                                skipCheckov = ValuesUtils.getVariable(configData, 'skip', 'checkov')

                                // Add 'qa' property
                                if(skipCheckov == true)
                                    configData.artifactProperties.add([prop: 'qa', value: 'skip'])
                                else if (checkovStatusCode >= 1)
                                    configData.artifactProperties.add([prop: 'qa', value: 'fail'])
                                else
                                    configData.artifactProperties.add([prop: 'qa', value: 'pass'])
                                // Add 'docker_inspect' property
                                def props = ValuesUtils.getVariableArrayList(configData, 'artifactProperties')
                                props.add([prop: 'docker_inspect', value: "${dockerJsonUrl}"])

                                pushUtils.executePostStageFunction(stagesMap, 'upload')
                            }
                        }
                    }
                }
            }
            stage('Update artifact properties') {
               when { expression { return ((pushUtils.notSkipStage(stagesMap, 'update')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                steps {
                    script {
                        def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'update')
                        if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                        def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'update')
                        ArtifactoryApi artfApi = new ArtifactoryApi(artifactoryURL, cred)

                        def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'update')
                        def props = ValuesUtils.getVariableArrayList(configData, 'artifactProperties', 'update')                                
                        
                        def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'update').toLowerCase()
                        artfApi.updateArtifactProperties(repo, artifactPath, artifactName+"/"+"${newVersion}", props)
                        //Props to latest
                        props.add([prop: 'image_version_tag', value: "${newVersion}"]) // added property image_version_tag
                        props = props.findAll {p -> p.prop != 'status' } // remove 'status' property from props                  
                        artfApi.updateArtifactProperties(repo, artifactPath, artifactName+"/latest", props) // update latest

                        pushUtils.executePostStageFunction(stagesMap, 'update')
                    }
                }
            }
            stage('Generate Release Notes') { 
                // This stage generates release notes in github using the github api
                when {
                    expression { return (pushUtils.notSkipStage(stagesMap, 'releaseNotes') && (BRANCH_NAME == 'master') && (configData.releaseNotes == true)) }
                }
                steps {
                    script {
                            pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)

                            pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                    }
                }
            }
            stage('Branch protection') {
                /* This stage updates the branch protection rule for BRANCH releases */
                when{
                    expression { return ((pushUtils.notSkipStage(stagesMap, 'protection')) && (BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/)) }
                }
                steps {
                    script {
                        def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                        pushUtils.branchProtect(repoName, gitHubCred)
                    }
                }
            }
        }
        post {
            always {
                script {
                    postActions(configData)
                    if (currentBuild.description == 'Code quality process failed') currentBuild.result = 'UNSTABLE'
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)  
                }
            }
        }
    }
}