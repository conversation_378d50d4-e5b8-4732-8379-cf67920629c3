Please read carefully below as there are some changes on your side that are required to continue working:

There was a security breach detected this weekend that affected to one of the service accounts (diaas-ro) that are used to connect to Artifactory. To resolve it, user had to be reset and password modified. However, given that this user was shared by many different teams and users and credentials were too much distributed, now the user is getting locked constantly. The following actions are required:

IN PIPELINES:

1.	Stop using diaas-ro
2.	In your pipelines start referencing this new credential ID: svc-assure-custompipeline-ro. It contains the new user/password combination to be used.
3.	Remove the .npmrc files that are in your GitHub repositories (they contain credentials that should not be made public)
4.	Instead of the local .npmrc, create or consume a .npmrc file that is available as credential in Jenkins

Credential ID: npmrc-config-svc-assure-custompipeline-ro
Usage sample:

withCredentials([file(credentialsId: 'npmrc-config-svc-assure-custompipeline-ro', variable: 'CONFIG')]) {
sh '''
  cat ${CONFIG} > ~/.npmrc
'''
}

5.	If you have other scenarios, please let me know.

LOCAL DEVELOPMENT

1.	Stop using diaas-ro
2.	Set up your npm configuration (.npmrc) to use your own artifactory user (universal id). If you don’t have access to the repositories, please let me know.


As a final note, please remember that under any circumstance, it is forbidden to upload any secret to GitHub.


How to get your artifactory information:

1. Login to Artifactory and go to your profile
2. Copy the API Key or generate and copy if not available yet
3. Go to an online Base64 encoder and encode it!
4. You will need to enconde the API Key alone for the password value and username:APIKey for the auth value
