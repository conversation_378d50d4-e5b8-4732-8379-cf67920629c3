#
# Run gpp on the current folder
#
# We find .tf files with '#if' and run gpp on them after backing them up
#
# The only arguments are the values to use the -D option with
#
params=( $(printf -- "-D%s " "$@") )
#echo ${params[@]}

while read -r -a pp_user; do
	echo "Preprocessing $(realpath $pp_user)" >&2
	mv "$pp_user" "${pp_user}.gpp"
	gpp -o "$pp_user" "${params[@]}" "${pp_user}.gpp" || { echo "Failed to preprocess $(realpath $pp_user)"  >&2; exit 1; }
done < <( grep -lr '^[[:blank:]]*#if' . | grep "^\.[^:]*\.tf" )

