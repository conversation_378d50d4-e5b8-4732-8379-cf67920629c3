# Code Analyzer - Main Terraform configuration

# DynamoDB Tables
module "code_analyzer_project_dynamodb" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//assure-standard-dynamodb/code"

  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region           = var.aws_region
  resource_tags        = local.resource_tags

  service_name         = var.code_analyzer_service_name
  table_name           = var.code_analyzer_project_table_name
  billing_mode         = var.billing_mode
  hash_key             = var.code_analyzer_hash_key
  attribute_list       = var.attribute_list
  non_key_attributes_list = var.non_key_attributes_list
}

module "code_analyzer_prompt_dynamodb" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//assure-standard-dynamodb/code"

  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region           = var.aws_region
  resource_tags        = local.resource_tags

  service_name         = var.code_analyzer_service_name
  table_name           = var.code_analyzer_prompt_table_name
  billing_mode         = var.billing_mode
  hash_key             = var.code_analyzer_hash_key
  attribute_list       = var.attribute_list
  non_key_attributes_list = var.non_key_attributes_list
}

# S3 Bucket
module "code_analyzer_bucket" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-create-s3-bucket/code"

  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region           = var.aws_region
  resource_tags        = local.resource_tags

  bucket_name = var.code_analyzer_service_name
}

# Lambda Functions
module "code_analyzer_project_lambda" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-create-simple-lambda/code"

  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region           = var.aws_region
  resource_tags        = local.resource_tags

  service_name  = var.code_analyzer_service_name
  function_name = var.code_analyzer_project_function_name
  function_full_name      = "${local.environment_name}-${var.code_analyzer_service_name}-${var.code_analyzer_project_function_name}-handler"

  bundle_path    = "${var.bundle_path_project_lambda}/${var.bundle_branch_project_lambda}"
  bundle_name    = var.bundle_name_project_lambda
  bundle_type    = var.bundle_type_project_lambda
  bundle_version = var.bundle_version_project_lambda

  allow_apigw_invocation = "true"
  register_lambda        = "true"
  add_lambda_policy      = "true"
  lambda_timeout         = var.lambda_timeout
  lambda_use_shared_sg   = "true"
  lambda_handler         = "lambda_function.lambda_handler"
  lambda_runtime         = var.lambda_runtime

  # lambda_environment = {
  #   project_table = module.code_analyzer_project_dynamodb.table_name
  #   prompt_table  = module.code_analyzer_prompt_dynamodb.table_name
  #   source_bucket = module.code_analyzer_bucket.bucket_name
  #   INGESTION_API_URL = "${module.code_analyzer_service_api.api_url}/ingestion"
  # }

  lambda_environment = {
    S3_BUCKET_NAME                  = var.project_lambda_s3_bucket_name
    APPLICATION_CONFIGURATION_FILE  = var.project_lambda_app_config_file
    DYNAMODB_TABLE                  = var.project_lambda_dynamodb_table
  }

  lambda_layers = var.lambda_layers
  lambda_policy = data.template_file.code_analyzer_policy.rendered
}

module "code_analyzer_prompt_lambda" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-create-simple-lambda/code"

  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region           = var.aws_region
  resource_tags        = local.resource_tags

  service_name  = var.code_analyzer_service_name
  function_name = var.code_analyzer_prompt_function_name
  function_full_name      = "${local.environment_name}-${var.code_analyzer_service_name}-${var.code_analyzer_prompt_function_name}-handler"
  
  bundle_path    = "${var.bundle_path_prompt_lambda}/${var.bundle_branch_prompt_lambda}"
  bundle_name    = var.bundle_name_prompt_lambda
  bundle_type    = var.bundle_type_prompt_lambda
  bundle_version = var.bundle_version_prompt_lambda

  allow_apigw_invocation = "false"
  register_lambda        = "true"
  add_lambda_policy      = "true"
  lambda_timeout         = var.lambda_timeout
  lambda_use_shared_sg   = "true"
  lambda_handler         = "lambda_function.lambda_handler"
  lambda_runtime         = var.lambda_runtime

  # lambda_environment = {
  #   project_table = module.code_analyzer_project_dynamodb.table_name
  #   prompt_table  = module.code_analyzer_prompt_dynamodb.table_name
  #   source_bucket = module.code_analyzer_bucket.bucket_name
  # }
  lambda_layers = var.lambda_layers
  lambda_policy = data.template_file.code_analyzer_policy.rendered
}

module "code_analyzer_ingestion_lambda" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-create-simple-lambda/code"

  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region           = var.aws_region
  resource_tags        = local.resource_tags

  service_name  = var.code_analyzer_service_name
  function_name = var.code_analyzer_ingestion_function_name
  function_full_name      = "${local.environment_name}-${var.code_analyzer_service_name}-${var.code_analyzer_ingestion_function_name}-handler"

  bundle_path    = "${var.bundle_path_ingestion_lambda}/${var.bundle_branch_ingestion_lambda}"
  bundle_name    = var.bundle_name_ingestion_lambda
  bundle_type    = var.bundle_type_ingestion_lambda
  bundle_version = var.bundle_version_ingestion_lambda

  allow_apigw_invocation = "false"
  register_lambda        = "true"
  add_lambda_policy      = "true"
  lambda_timeout         = var.lambda_timeout
  lambda_use_shared_sg   = "true"
  lambda_handler         = "lambda_function.lambda_handler"
  lambda_runtime         = var.lambda_runtime

  lambda_environment = {
    project_table = module.code_analyzer_project_dynamodb.table_name
    prompt_table  = module.code_analyzer_prompt_dynamodb.table_name
    source_bucket = module.code_analyzer_bucket.bucket_name
    S3_BUCKET_NAME = module.code_analyzer_bucket.bucket_name
    APPLICATION_CONFIGURATION_FILE = "app.yaml"
    GIT_USERNAME = var.git_username
    GIT_TOKEN = var.git_token
  }
  lambda_policy = data.template_file.code_analyzer_policy.rendered
}

module "code_analyzer_llm_lambda" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//platform-create-simple-lambda/code"

  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region           = var.aws_region
  resource_tags        = local.resource_tags

  service_name  = var.code_analyzer_service_name
  function_name = var.code_analyzer_llm_function_name
  function_full_name      = "${local.environment_name}-${var.code_analyzer_service_name}-${var.code_analyzer_llm_function_name}-handler"

  bundle_path    = "${var.bundle_path_llm_lambda}/${var.bundle_branch_llm_lambda}"
  bundle_name    = var.bundle_name_llm_lambda
  bundle_type    = var.bundle_type_llm_lambda
  bundle_version = var.bundle_version_llm_lambda

  allow_apigw_invocation = "false"
  register_lambda        = "true"
  add_lambda_policy      = "true"
  lambda_timeout         = var.lambda_timeout
  lambda_use_shared_sg   = "true"
  lambda_handler         = "lambda_function.lambda_handler"
  lambda_runtime         = var.lambda_runtime

  lambda_environment = {
    S3_BUCKET_NAME = module.code_analyzer_bucket.bucket_name
    APPLICATION_CONFIGURATION_FILE = "app.yaml"
    PROMPT_DB_API_URL = "${module.code_analyzer_service_api.api_url}/prompt"
    USER_CONFIG_DB_API_URL = "${module.code_analyzer_service_api.api_url}/config"
    project_table = module.code_analyzer_project_dynamodb.table_name
    prompt_table = module.code_analyzer_prompt_dynamodb.table_name
    source_bucket = module.code_analyzer_bucket.bucket_name
  }
  lambda_policy = data.template_file.code_analyzer_policy.rendered
}

# API Gateway
module "code_analyzer_service_api" {
  source = "git::https://github.dxc.com/assure/assure-platform-terraform-modules.git//assure-standard-api/code"

  client_name          = local.client_short_name
  environment_name     = local.environment_name
  artifact_branch_name = var.artifact_branch_name
  aws_account          = local.aws_target_account
  aws_region           = var.aws_region

  service_name  = var.code_analyzer_service_name
  api_name      = var.code_analyzer_api_name
  api_base_path = var.code_analyzer_api_base_path
  service_url   = "lambda:${module.code_analyzer_project_lambda.lambda_name}"
}