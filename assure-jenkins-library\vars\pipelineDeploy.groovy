#!/usr/bin/env groovy

/**
 * Definition of the all the pipeline stages to be executed for a Assure Platform Terraform Deploy project.
 */

import org.pdxc.rest.GitApi
import org.pdxc.util.DefaultConfiguration
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.pdxc.util.FileUtils
import groovy.json.JsonOutput
import org.assure.util.WarningUtils
import org.pdxc.notification.TeamsTemplateNotification
import org.assure.pushpipeline.PushPipelineUtils

/* For TF12 Compatibility Stage */
import org.pdxc.notification.MailNotification

/**
 * Silver Image only has to be created from a specific set of branches. This method returns True when branch is correct.
 * @param branch Name of branch.
 * @return true when the branch name allows the creation of a Silver Image.
 */
boolean validSilverImageBranch(String branch) {
    return (branch == 'master' ||
            branch ==~ /(^r{1}\d+\d+\.\d+.*)/ ||
            branch ==~ /(^feature\/{1}+.*)/ ||
            branch ==~ /(^fix\/{1}+.*)/ ||
            branch == 'development' ||
            branch ==~ /(^release\/{1}+\d+\d+\.\d+.*)/)
}

/* START TF 0.12 Compatibility helper functions */

/**
 *  This method sends an email to inform of compatibility issues
 *  @param reportFile Detailed report file, that will be attached.
 */
void sendCompatibilityEmail(String emailTo = '',
                            String emailFrom = '<EMAIL>',
                            String reportFile = '',
                            String link='') {
    env.GIT_COMMIT_MESSAGE = sh script: "git log --oneline -1 ${GIT_COMMIT}",
            returnStdout: true,
            label: 'Get commit message'
    env.GIT_AUTHOR = sh script: "git --no-pager show -s --format='%ae'",
            returnStdout: true
    label: 'Get committer data'

    def body = """
                <p>During the job '${env.JOB_NAME} [${env.BUILD_NUMBER}]', I have detected some TF compatibility issues.</p>
                <p>Please find the attached report for details</p>
                <p>Also, please find the <a href="${link}">report in the corresponding SI</a>.</p>
                <br/>
                <p>GitHub details:</p>
                <ul>
                    <li>Repository: ${GIT_URL}</li>
                    <li>Branch: ${GIT_BRANCH}</li>
                    <li>Commit: ${GIT_COMMIT}</li>
                    <li>Commit message: ${env.GIT_COMMIT_MESSAGE}</li>
                    <li>Committer: ${env.GIT_AUTHOR}</li>
                </ul>
                <br/>
                <p>Check console output at &QUOT;<a href='${env.BUILD_URL}'>${env.JOB_NAME} [${env.BUILD_NUMBER}]</a>&QUOT;</p>
            """

    def subject = "TF 0.12 compatibility issues ${env.JOB_NAME} [${env.BUILD_NUMBER}]"

    MailNotification mail = new MailNotification()

    mail.configureMail(emailTo, emailFrom, subject, body, reportFile)

    mail.send()
}

/* END TF 0.12 Compatibility helper functions */

/**
 * Executor method to run the pipeline stages.
 * @param configData Map with the custom values to be used during the execution.
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'deploy.Dockerfile') {
    String pipelineName = 'deploy'
    def reportFile // Only needed if "Test Terraform 0.12 Compatibility" exists.
    def configData
    def gitHubCredential
    def currentRepoName
    def targetRepoName
    def silverImageOrg
    def createSilverImage = false
    def tf_version
    def userInput // recreate release input
    def changes = 0
    def org
    // Name of the dockerFile
    String dockerName
    String awsProvider

    def checkovStatusCode = 0

    PushPipelineUtils   pushUtils
    WarningUtils warningUtils

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {            
            timeout(time: 1, unit: 'HOURS')
        }

        stages {
            stage ('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()        
                        warningUtils = new WarningUtils()                
                        dockerName = "${pipelineName}.Dockerfile"
                        pushUtils.setDockerAgentTF(dockerPath, dockerName, stagesMap)
                    }
                }
            }
            stage ('Start SI creation') {
                agent {
                    dockerfile {
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        filename "${pipelineName}.Dockerfile"
                        reuseNode true
                        registryCredentialsId 'assure-docker'
                    }
                }
                stages {
                    stage('Pipeline info') {
                        steps {
                            script {
                                pushUtils.checkVersionFormat() // Check if version file is in the correct format

                                def conf = libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
                                writeFile file: "${pipelineName}-conf.yml", text: conf
                                def defaultConfigYml = readYaml file: "${pipelineName}-conf.yml"
                                def repoData = readYaml file: 'conf.yml'

                                configData = defaultConfigYml + repoData
                                writeYaml file: 'conf.yml', data: configData, overwrite: true
                                
                                println 'Loaded configuration values: \n\n' + JsonOutput.prettyPrint(JsonOutput.toJson(configData))
                                tf_version = sh (
                                    script: "echo \"\$TERRAFORM_VERSION\"", 
                                        returnStdout: true, 
                                        label: 'Get TF Version').trim()
                                echo "Installed Terraform Version is $tf_version"
                            }
                        }
                    }
                    stage('Validate pipeline') {
                        steps {
                            script {
                                currentRepoName = GitApi.getInstance().getCurrentRepositoryName()
                                if (currentRepoName != configData.repositoryName) {
                                    error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                                }
                                echo "Configured repository name matches current repository: ${currentRepoName}"

                                /*
                                 * THIS IS A "SECRET" OPTION - DO NOT PUBLICIZE
                                 */
                                if (stagesMap.silverImage?.force == true) {
                                    createSilverImage = true
                                    echo 'Silver image creation imposed, preparing Silver Image...'
                                } else if (!validSilverImageBranch("${BRANCH_NAME}")) {
                                    echo 'Silver Image will not be created as this is a non-SI branch'
                                    currentBuild.description = 'Non-SI branch'
                                } else {
                                    createSilverImage = true
                                    echo 'Branch name is valid, preparing Silver Image...'
                                }
                            }
                        }
                    }
                    stage('Git setup') {
                        when { expression { return createSilverImage } }
                        steps {
                            script {
                                gitHubCredential = ValuesUtils.getVariable(configData, 'gitHubCredential', 'setup')
                                def mail = ValuesUtils.getVariable(configData, 'gitEmail', 'setup')
                                def user = ValuesUtils.getVariable(configData, 'gitUsername', 'setup')
                                def url = ValuesUtils.getVariable(configData, 'gitHubUrl', 'setup')
                                functiongroup_git.setup(gitHubCredential, mail, user, url)

                                org = ("${env.BUILD_URL}" =~ /(?:[job])(\/.*?\/)(?:[job])/)[0][1]
                                org = org.replaceAll('/', '')

                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Tag Version in GitHub') {
                        when { expression { return createSilverImage && (BRANCH_NAME == 'master') } }
                        steps {
                            script {
                                env.VERSION = FileUtils.getAttributeFromJsonFile('version.json', 'version')

                                env.VERSION = "${env.VERSION}+${env.BUILD_NUMBER}"

                                echo "Tagging current repository with version ${env.VERSION}"
                               
                                functiongroup_git.tagCurrentRepo(env.VERSION, GIT_COMMIT)
                            }
                        }
                    }
                    stage('Create Silver Image repository') {
                        /*  A TF Silver Image is a copy of this deploy project but with all the necessary changes to make it
                        compliant with PDXC TF API. It basically consists on the same TF code but with external dependencies
                        resolved and other changes. Although the Silver Image is a copy of this one, the PUSH pipeline logic is
                        different. Hence, during the copy process, we need to overlay the target Jenkins file with a new one,
                        which can be found in this repository under the /custom/deploy-project/tfapi folder.
                        Silver Images are stored in a separate GitHub organization called 'dxc-assure-tfapi'. This stage creates
                        the repo on that organization by means of the GitHub API */
                        when { expression { return createSilverImage } }
                        steps {
                            script {
                                silverImageOrg = ValuesUtils.getVariable(configData, 'silverImageOrganization', 'createRepo')
                                def branchName = BRANCH_NAME.replaceAll('/', '-')
                                targetRepoName = "${currentRepoName}_${branchName}"

                                GitApi gitApi = GitApi.getInstance(DefaultConfiguration.PDXC_GITHUB_URL, gitHubCredential)
                                // boolean created = gitApi.createGitHubRepo(silverImageOrg, targetRepoName)

                                boolean created = pushUtils.createGitHubRepository(silverImageOrg, targetRepoName, configData)

                                if (!created) {
                                    if (BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/) {
                                        def url = "${DefaultConfiguration.PDXC_GITHUB_URL}/api/v3/repos/${silverImageOrg}/${targetRepoName}/commits"
                                        def response = gitApi.get(url, [200, 409])
                                        if ('Git Repository is empty.' == response.message) {
                                            echo 'Repository already exists but it is empty. Continuing...'
                                        } else {                                           
                                            try {
                                                timeout(time: 3, unit: 'MINUTES') {
                                                    userInput = input(
                                                    id: 'Proceed1', message: 'Do you want to recreate the release?', submitterParameter: 'submitter', parameters: [
                                                    [$class: 'BooleanParameterDefinition', defaultValue: true, description: 'Please confirm you agree with this', name: 'Agree']
                                                    ])
                                                }
                                            } catch(err) {                                                
                                                currentBuild.description = "Silver Image Release already exists"
                                                error "It is not possible to overwrite an existing Release Silver Image"                                              
                                            }
                                        }
                                    }
                                    echo 'Repository already exists. Continuing...'
                                }
                            }
                        }
                    }
                    stage('Copy files from tfapi folder') {
                        /* Time to overwrite Silver Image Jenkinsfile and any other relevant file with the ones under
                        custom/deploy-project/tfapi so we can execute a proper (and different) PUSH pipeline for the Silver Image
                        itself */
                        when { expression { return createSilverImage } }
                        steps {
                            script {
                                /*
                                 * Only done for TF 0.11 and 0.12
                                 */
                                if (tf_version == "0.11" || tf_version == "0.12") {
                                    def dataFile = readFile file:'code/aws.tf'                                
                                    def (newAwsTfFile, sendEmail, originVersionAwsProvider) = pushUtils.getAWSVersion(dataFile, tf_version)
                                    awsProvider = originVersionAwsProvider
                                    if (newAwsTfFile != "") {
                                        sh "rm -rf code/aws.tf"
                                        writeFile file: "code/aws.tf", text: newAwsTfFile
                                    }
                                    
                                    if (sendEmail) {
                                        GitApi gitApi = GitApi.getInstance(DefaultConfiguration.PDXC_GITHUB_URL, gitHubCredential)
                                        def gitResponse = gitApi.getResponse('repos/'+"${org}"+'/'+"${currentRepoName}"+'/contributors')

                                        String contributors = ''

                                        gitResponse.findAll { item ->
                                            contributors = contributors + item.login + '@dxc.com,'
                                        }
                                        
                                        def committer = sh(
                                                script: "git --no-pager show -s --format='%ae'",
                                                returnStdout: true
                                        ).trim()

                                        contributors = contributors + committer

                                        contributors = contributors.replaceAll('@csc.com', '@dxc.com')
                                        
                                        MailNotification mail = new MailNotification()
                                        def subject = "IMMEDIATE ACTION REQUIRED - ${currentRepoName} is missing an AWS PROVIDER version"

                                        def gitUrlLink = ValuesUtils.removeStartEndChars("${GIT_URL}", '.git', false, true)

                                        def body = """
                                            <p>You are receiving this email because you are contributor of this <a href='${GIT_URL}'>repository</a>.</p>
                                            <p>Due to recent Terraform update, there are problems when running code without specifying which AWS Provider must be used. We have detected that the aws provider version is missing in your repository.</p>
                                            <p><b>Please make sure that you modify your Deploy Unit ${currentRepoName} code ASAP by adding the AWS provider correct version to prevent further issues.</b></p>
                                            <p>Link to the <a href='${gitUrlLink}/blob/${GIT_BRANCH}/code/aws.tf'>aws.tf file</a> where version is needed</p>                                        
                                            <br/>
                                        """

                                        mail.configureMail("${contributors}"+',<EMAIL>', '<EMAIL>', subject, body, '')
                                        mail.send()
                                    }
                                }

                                def isADU = false
                                // if adu cp jenkins adu, if not jenkins
                                configData.artifactProperties.findAll { property ->
                                                if (property.prop == 'type') {
                                                    typeProp = true
                                                    if (property.value == 'adu') isADU = true
                                                }
                                            }

                                if (isADU == true) {
                                    def jenkins = libraryResource "custom/deploy-project/tfapi/Jenkinsfile.adu"
                                    writeFile file: "Jenkinsfile", text: jenkins
                                }
                                else {
                                    def jenkins = libraryResource "custom/deploy-project/tfapi/Jenkinsfile"
                                    writeFile file: "Jenkinsfile", text: jenkins
                                }

                                def docker = libraryResource "custom/deploy-project/tfapi/Dockerfile"
                                writeFile file: "Dockerfile", text: docker

                                def config = libraryResource "custom/deploy-project/tfapi/configData.yml"
                                writeFile file: "config.yml", text: config

                                def script = libraryResource "custom/deploy-project/tfapi/resolve.sh"
                                if ("${tf_version}" != '0.11') {
                                    script = libraryResource "custom/deploy-project/tfapi/resolve-tf12.sh"
                                }
                                writeFile file: "resolve.sh", text: script

                                def json = libraryResource "custom/deploy-project/msteams_notification.json"
                                writeFile file: "msteams_notification.json", text: json

                                sh """#!/bin/bash
                                        cat ./conf.yml
                                        cp ./conf.yml ./configData.yml
                                        mv ./resolve.sh ./code
                                    """
                            }
                        }
                    }                    
                    stage ('Checkov Scan') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'checkov') } }
                        steps {
                            script {
                                def checkovQualityGate = ValuesUtils.getVariable(configData, 'checkovQualityGate', 'checkov')
                                def (checkovData, uuidCheckov) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.checkov_conf_file, "checkov/checkovConfiguration.yml.gpg")
                                def scriptPath = "checkov/checkov.sh"

                                pushUtils.checkovScan('deploy', currentRepoName, currentRepoName, scriptPath, uuidCheckov, dockerName)

                                def checkName = 'Vulnerabilities'
                                withChecks("${checkName}") {
                                    junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: "**/results*.xml")
                                }
                                pushUtils.markGitHubCheckNeutral(configData, checkName)

                                checkovStatusCode = pushUtils.checkovErrorCheck(currentRepoName, uuidCheckov)

                                // Remove script
                                sh(script: """ 
                                    rm -rf ./checkov-scan-results-${currentRepoName}_${uuidCheckov}.log
                                    rm -rf ./results
                                    rm -rf ./checkovConfiguration.yml*
                                    rm -rf ./passphrase.txt
                                    """, label: "Remove checkov log")

                                if(checkovStatusCode != 0 && checkovQualityGate){
                                    currentBuild.result = 'ABORTED'
                                    error ("-- ❌ -- Pipeline ABORTED ❗❗ Checkov Scan 🛸 status: FAIL (checkovQualityGate is ${checkovQualityGate})")
                                    return
                                }

                                catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                    if(checkovStatusCode != 0){
                                        error("-- ⚠️ -- Checkov Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                                        return
                                    }
                                }
                                pushUtils.executePostStageFunction(stagesMap, 'checkov')
                            }
                        }
                    }
                    stage('Resolve external dependencies') {
                        /* This stage executes the bash script 'resolve.sh' which is in charge of resolving the external
                        dependencies so the Silver Image is PDXC-compliant */
                        when { expression { return createSilverImage } }
                        steps {
                            script {
                                def mode = 'failsafe'
                                def modulesBranch = BRANCH_NAME
                                def branchModulesVersion = ValuesUtils.getVariable(configData, 'modulesReleaseVersion', 'releaseModules')
                                
                                if (branchModulesVersion != "") {
                                    mode = 'fixedversion'
                                    modulesBranch = branchModulesVersion
                                } else if (configData.terraform?.assureModulesVersion != null) {
                                    /*
                                     * The "new" way to specify modules versions
                                     *
                                     * This is NOT to be publicized yet, but it allows TF provider-like
                                     * version resolution.
                                     *
                                     * At the very least it provides a very specific and convenient way to 
                                     * test the release resolution logic in the resolve.sh scripting - you 
                                     * don't need to create a release branch of your DU to do this now.
                                     *
                                     * Note that the syntax does NOT include the "release/" prefix, it just
                                     * uses the release NUMBER. if that is prefixed with "~>" (as we do for
                                     * Terraform providers) then we use branch mode of "release", otherwise
                                     * we use a branch mode of "fixedversion"
                                     *
                                     * Again, this is consistent with Terraform's own provider version syntax.
                                     */
                                    def assureModulesVersion = configData.terraform?.assureModulesVersion
                                    /*
                                     * If the string starts with "~>" then we use release semantics, otherwise
                                     * fixedversion semantics
                                     */
                                    if (assureModulesVersion ==~ /^ *~> *[0-9.]+ *$/) {
                                        assureModulesVersion = assureModulesVersion.replaceFirst(/^ *~> */, "")
                                        mode = 'release'
                                    } else {
                                        mode = 'fixedversion'
                                    }
                                    modulesBranch = "release/${assureModulesVersion}"
                                } else {
                                    if (BRANCH_NAME ==~ /(^release\/{1}+\d+\.\d+.*)/) {
                                        mode = 'release'
                                    }
                                    if ("${tf_version}" == '0.11') {
                                        mode = 'release'
                                        modulesBranch = 'release/22.2.0'
                                    } else if ("${tf_version}" == '0.12' && awsProvider == '2.0') {
                                        modulesBranch = 'release/22.2.0'
                                        mode = 'release'
                                    }                                    
                                } 
                               
                                def resolveStatusCode = sh (
                                                script: """
                                                            cd code
                                                            chmod +x resolve.sh
                                                            ./resolve.sh ${modulesBranch} ${mode}
                                                        """,
                                                label: "-- ⚙️ -- Resolving Terraform modules Branch: ${modulesBranch} with Mode: ${mode}",
                                                returnStatus: true)
                                
                                sh script: "rm -rf code/resolve.sh ./silver-image-scripts;"
                        
                                if (resolveStatusCode != 0) {                                    
                                    error ("-- ❌ -- Pipeline ABORTED ❗❗ Cannot resolve the Terraform modules")                                    
                                }
                            }
                        }
                    }
                    stage('TF code quality') {
                        /*
                        * This stage aims to validate Terraform version
                        */
                        when { expression { return createSilverImage } }
                        parallel {
                            stage ('TF Validation + Compatibility') {
                                steps {
                                    script {
                                        /* The actual name of the report file */
                                        reportFile = 'hints012migration.md'
                                                                                                                        
                                        echo "-- ℹ️ -- Terraform version is ${tf_version}"

                                        /* Now init terraform, TF validate & create the reportfile */
                                        catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                            sh  """#!/bin/bash

                                                 cd code
                                                terraform init -input=false
                                                if [ ${tf_version} != '0.11' ]; then
                                                    export AWS_DEFAULT_REGION="us-east-1"
                                                    terraform validate
                                                else
                                                    terraform validate -check-variables=false
                                                    echo Creating the report file to send
                                                    if ! terraform 0.12checklist -input=false &> ../${reportFile} ; then
                                                        echo Report file created, to be included in the silver image
                                                    fi
                                                fi
                                            """
                                        }

                                        if (fileExists(reportFile)) {
                                            archiveArtifacts artifacts: "${reportFile}"
                                        }

                                        // Now calculating if we should send the notication

                                        // By default, don't send any notification
                                        changesInReport = false

                                        // Get the old report file
                                        functiongroup_git.clone(silverImageOrg, targetRepoName)
                                        def oldReportFile = targetRepoName + '/' + reportFile

                                        if (fileExists(reportFile)) {
                                            if (fileExists(oldReportFile)) {
                                                // Send notification only if old report is different from the new one
                                                changesInReport = ( sh (script: "diff ${reportFile} ${oldReportFile} &> /dev/null", returnStatus: true) != 0 )
                                            } else {
                                                // if this is the first report, send notification
                                                changesInReport = true
                                            }
                                        }
                                        // Cleaning up
                                        sh "rm -rf ./${targetRepoName}"

                                        /* Now, if there are changes, send emails */
                                        if (changesInReport && 'true' == ValuesUtils.getVariable(configData, 'sendMail', 'TF12Hints')) {
                                            def mailTo = ValuesUtils.getVariable(configData, 'emailTo', 'TF12Hints')
                                            def requester = sh script: "git --no-pager show -s --format='%ae'", returnStdout: true
                                            if (requester != null) {
                                                mailTo = requester + ', ' + mailTo
                                            }
                                            def mailFrom = ValuesUtils.getVariable(configData, 'emailFrom', 'TF12Hints')

                                            def link = 'https://github.dxc.com/dxc-assure-tfapi/' + targetRepoName + '/blob/master/' + reportFile

                                            // As an attachment
                                            sendCompatibilityEmail(mailTo, mailFrom, reportFile, link)
                                        }

                                        // Cleaning up avoiding unnecessary files
                                        sh 'rm -rf ./code/.terraform'
                                    }
                                }
                            }
                        }
                    }
                    stage('Get artefact data') {
                        when { expression { return createSilverImage } }
                        steps {
                            script {
                                def dataFile = ''
                                def allVarsFile = 'all_vars.txt'

                                def constantsFile = findFiles(glob: 'code/constants.tf')
                                if (constantsFile != null && constantsFile != [] && constantsFile.size() > 0) {
                                    dataFile += readFile("${constantsFile[0].toString()}") + '\n'
                                }

                                def constantsMultipleFile = findFiles(glob: 'code/constants.*.tf')
                                if (constantsMultipleFile != null && constantsMultipleFile != [] && constantsMultipleFile.size() > 0) {
                                    dataFile += readFile("${constantsMultipleFile[0].toString()}") + '\n'
                                }
                                
                                def files = findFiles(glob: 'code/*.vars.tf')
                                if (files != null && files != [] && files.size() > 0) {
                                    files.each { file ->
                                        dataFile += readFile("${file.toString()}") + '\n'
                                    }
                                }

                                writeFile(file: "./${allVarsFile}", text: "${dataFile}")

                                def dataJson = ''
                                def allFiles = findFiles(glob: "${allVarsFile}")
                                if (allFiles != null && allFiles != [] && allFiles.size() > 0) {
                                    dataFile = readFile("${allFiles[0].toString()}")
                                    dataJson += ((dataJson != '') ? ',' : '') + pushUtils.getAllArtefactData(dataFile)
                                }

                                dataJson = '{ "artefacts": [' + dataJson + ']}'

                                writeJSON file: 'artefacts.json', json: dataJson
                                sh script: 'cat artefacts.json', label: 'Reading artefacts.json'
                                sh script: "rm ./${allVarsFile}", label: 'Delete temporal files'                                
                            }
                        }
                    }
                    stage('Get parameters data') {
                        when { expression { return createSilverImage } }
                        steps {
                            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                script {
                                    if ("${tf_version}" != '0.11') {
                                        def getDuParametersFile = libraryResource(resource: "pythonScripts/get-du-parameters.py")
                                        writeFile(file: "code/get-du-parameters.py", text: getDuParametersFile)

                                        def scanPyFile = libraryResource(resource: "pythonScripts/scan_du_dependency.py")
                                        writeFile(file: "code/scan_du_dependency.py", text: scanPyFile)


                                        // python-hcl2 ~= 4.3.1

                                        sh script: """
                                            pip install python-hcl2 genson
                                            cd code
                                            python3 get-du-parameters.py
                                            mv parameters.json ./../parameters.json
                                            echo "start dependency scanning"
                                            python3 scan_du_dependency.py
                                            mv dependency.json ./../dependency.json
                                        """, returnStatus: true, label: '-- ⚙️-- Get parameters.json'

                                        sh script: "rm -rf ./code/get-du-parameters.py  ./code/scan_du_dependency.py", label: 'Delete temporal parameters script'
                                    }
                                    else {                                        
                                        echo("-- ⚠️ -- This option is not available for TF ${tf_version} version")
                                    }
                                }
                            }
                        }
                    }
                    stage('Commit Silver Image') {
                        /* Finally, we copy all the source code in this repository into the Silver Image and commit the change.
                        Please note that we are excluding .git file, /tfapi folder and the cloned folder in the copy (obviously) */
                        when { expression { return createSilverImage } }
                        steps {
                            script {
                                functiongroup_git.clone(silverImageOrg, targetRepoName)
                                def siConfigFile = "./${targetRepoName}/configData.yml"
                                sh """
                                    rsync -av --delete --progress ./ ./${targetRepoName} \
                                            --exclude '.git' \
                                            --exclude '.github' \
                                            --exclude 'deploy.Dockerfile' \
                                            --exclude 'deploy-12.Dockerfile' \
                                            --exclude 'conf.yml' \
                                            --exclude 'config.yml' \
                                            --exclude 'deploy-conf.yml' \
                                            --exclude 'msteams_notification.json' \
                                            --exclude '${reportFile}' \
                                            --exclude 'version.json' \
                                            --exclude '${targetRepoName}/' \
                                            --exclude 'tfapi/' \
                                            --exclude 'assure-platform-jenkins-projects-library/' \
                                            --exclude 'code/checkov_results.xml' \
                                            --exclude 'dependency.json'

                                    echo "\nartifactoryBranch: \"${BRANCH_NAME}\"\n" >> ${siConfigFile}
                                    cat ./config.yml >> ${siConfigFile}
                                """

                                // Calculate and set the committer in the configData.yml file. This provides the committer to the
                                // tfapi pipeline and allows to save the committer property in artifactory.
                                def realCommitter = sh (script: "git --no-pager show -s --format='%ae'", 
                                                        label: "-- 👤 -- Getting committer", 
                                                        returnStdout: true).trim()
                                def configContent =  readFile "./${targetRepoName}/configData.yml"
                                writeFile file: "./${targetRepoName}/configData.yml", text: configContent + "\r\ncommitter: ${realCommitter}"

                                def originOrganization = ("${env.BUILD_URL}" =~ /(?:[job])(\/.*?\/)(?:[job])/)[0][1]
                                originOrganization = originOrganization.replaceAll('/', '')
                                def configContentOriginal =  readFile "./${targetRepoName}/configData.yml"
                                writeFile file: "./${targetRepoName}/configData.yml", text: configContentOriginal + "\r\norigin_organization: ${originOrganization}"

                                // Only if "Test Terraform 0.12 Compatibility" exists.
                                if (!fileExists(reportFile)) {
                                    // If there is no reportFile we should delete the old reportFile from the silverImage
                                    sh "rm -f ./${targetRepoName}/${reportFile}"
                                }

                                // Check if there were changes compared with already existing Silver Image. If so, nothing
                                // will need to be committed and that step will be skipped.                                
                                changes = sh script: """
                                        cd ${targetRepoName}
                                        if [ -d 'assure-platform-jenkins-projects-library' ]; then rm -rf assure-platform-jenkins-projects-library ; fi
                                        git status
                                        git add -A
                                        git status
                                        git diff-index --quiet HEAD --
                                    """, returnStatus: true, label: 'Look for changes in the silver image'
                                echo "Changes found: ${changes}. If value is not 0, changes are present and have to be commited."
                                if (changes != 0) {
                                    // Calculate and set new version in version.json file. This has to be done here because
                                    // earlier it would mean that there are change in all job executions as the file is
                                    // updated with the new version value.
                                    echo 'Calculate and set new version:'
                                    def currentVersion = FileUtils.getAttributeFromJsonFile('version.json', 'version')
                                    newVersion = currentVersion + "+${BUILD_NUMBER}"
                                    FileUtils.setValueOnJSONFileContent('version.json', 'version', newVersion)
                                    sh script: "cp ./version.json ./${targetRepoName}", label: 'Copy version file to new path'
                                    echo "Current Version: ${currentVersion} --- New Version: ${newVersion}"

                                    // Dependency file                                    
                                    def dependencyFile = findFiles(glob: './dependency.json')
                                    if (dependencyFile != null && dependencyFile != [] && dependencyFile.size() > 0) {
                                        sh script: "cp ./dependency.json ./${targetRepoName}", label: 'Copy dependency file to new path'
                                    }

                                    msg = sh script: "git log --oneline -1 ${GIT_COMMIT}", returnStdout: true,
                                    label: 'Get commit message'
                                    functiongroup_git.addCommitAndPush(silverImageOrg, targetRepoName, 'master', "${msg}", targetRepoName)
                                } else {
                                    echo 'No changes found in the repository. No commit or push will be done to the Silver Image repo'
                                    currentBuild.description = 'No changes in Silver Image'
                                }
                                pushUtils.executePostStageFunction(stagesMap, 'commitSI')
                            }
                        }
                    }
                    stage('Generate Release Notes') { 
                        // This stage generates release notes in github using the github api
                        when {
                            expression { return (pushUtils.notSkipStage(stagesMap, 'releaseNotes') && (BRANCH_NAME == 'master') && (configData.releaseNotes == true)) }
                        }
                        steps {
                            script {
                                    pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)

                                    pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                            }
                        }
                    }
                    stage('Branch protection') {
                        /* This stage updates the branch protection rule for BRANCH releases */
                        when{
                            expression { BRANCH_NAME ==~ /(^release\/{1}+\d+\.\d+.*)/ }
                        }
                        steps {
                            script {
                                def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                                pushUtils.branchProtect(currentRepoName, gitHubCred)
                            }
                        }
                    }
                }
            }
        }
        post {
            always {
                script {
                    echo "-- ℹ️-- Send notifications with execution result"
                    def status = "${currentBuild.currentResult}"
                    
                    /* Send notification containing reportfile if TF11 */
                    // if ("${tf_version}" == '0.11') {
                    //     if (validSilverImageBranch("${BRANCH_NAME}")) {
                    //         def templateJSON = readJSON file: 'msteams_notification.json'
                    //         def template = JsonOutput.toJson(templateJSON)
                    //         def notificationValues = [:]
                    //         notificationValues.put('status', "${currentBuild.currentResult}")
                    //         notificationValues.put('summary', "${configData.repositoryName}")
                    //         notificationValues.put('title', 'TF 0.12 compatibility')
                    //         notificationValues.put('themeColor', (status == 'SUCCESS') ? '#00FF00' : '#ff0000')
                    //         notificationValues.put('activityTitle', "***${configData.repositoryName} report***")
                    //         notificationValues.put('activitySubtitle', '')
                    //         notificationValues.put('activityImage', '')
                    //         notificationValues.put('repositoryURL', "${configData.repositoryName}")
                    //         notificationValues.put('branch', "${BRANCH_NAME}")

                    //         def reportContent = sh (
                    //             script: "cat ${reportFile}",
                    //             returnStdout: true
                    //         ).trim()

                    //         reportContent = reportContent.replaceAll('(\n|\r)', ' ')
                    //         if (!reportContent.contains('Looks good!')) {
                    //             notificationValues.put('status', '***NO compatible with TF 0.12***')
                    //             notificationValues.put('themeColor', '#ff0000')
                    //             reportContent = reportContent.replaceAll('"', '')
                    //             reportContent = reportContent.replaceFirst ('Terraform v0.12:', 'Terraform v0.12:<br><br>')
                    //             reportContent = reportContent.replaceAll('- \\[ \\]', '</li><li>')
                    //             reportContent = reportContent.replaceAll('# Module', '<br># Module')
                    //             reportContent = reportContent.replaceAll('Taking these steps', '<br>Taking these steps')
                    //             reportContent = '<ul>' + reportContent + '</li></ul>'
                    //         }

                    //         notificationValues.put('text', "${reportContent}")

                    //         def organization = ("${env.BUILD_URL}" =~ /(?:[job])(\/.*?\/)(?:[job])/)[0][1]
                    //         organization = organization.replaceAll('/', '')

                    //         def blueoceanURL = "https://jenkins.dxc.com/blue/organizations/jenkins/${organization}%2F${configData.repositoryName}/detail/"

                    //         def branchURL = "${env.GIT_BRANCH}".replaceAll('/', '%2F')

                    //         def viewJobBlueoceanUri = blueoceanURL + "${branchURL}/${env.BUILD_NUMBER}/pipeline"
                    //         notificationValues.put('viewJobUri', viewJobBlueoceanUri)

                    //         TeamsTemplateNotification teamsNotification = new TeamsTemplateNotification()
                    //         teamsNotification.setMessageFromTemplate(template, notificationValues, false)
                    //         teamsNotification.setChannel("${configData.TEAMS_WEBHOOK}")
                    //         teamsNotification.send()
                    //     }
                    // }

                    if (userInput?.'Agree' == true && changes != 0) currentBuild.description = "Silver Image Release overwritten by ${userInput.submitter}"

                    postActions(configData)
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}
