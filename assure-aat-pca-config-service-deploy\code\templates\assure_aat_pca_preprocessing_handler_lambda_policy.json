{"Version": "2012-10-17", "Statement": [{"Sid": "VisualEditor0", "Effect": "Allow", "Action": ["dynamodb:GetItem", "dynamodb:UpdateItem", "dynamodb:BatchWriteItem"], "Resource": ["${training_status_table_arn}", "${training_status_table_arn}/index/*", "${reports_table_arn}", "${reports_table_arn}/index/*"]}, {"Sid": "VisualEditor1", "Effect": "Allow", "Action": ["s3:PutObject", "s3:GetObject", "s3:ListBucket", "s3:GetObjectVersion"], "Resource": ["${s3_bucket_arn}", "${s3_bucket_arn}/*"]}, {"Sid": "VisualEditor2", "Effect": "Allow", "Action": ["sagemaker:InvokeEndpointAsync", "sagemaker:InvokeEndpointWithResponseStream", "sagemaker:InvokeEndpoint"], "Resource": ["arn:aws:sagemaker:${sagemaker_region}:${sagemaker_account_number}:inference-component/*", "arn:aws:sagemaker:${sagemaker_region}:${sagemaker_account_number}:endpoint/*", "arn:aws:sagemaker:${aws_region}:${aws_account}:endpoint/${Sagemaker_endpoint_name}"]}]}