// constant vars for api
variable "service_name" {
  description = "generic name of this service"
  default     = "assure-aat-pca-config-service"
}

variable "local_BusinessServiceCI" {
  description = "Name of the service"
  type        = string
  default     = "Assure_AAT_PCA_Configuration_Service"
}

variable "api_name" {
  description = "The API name"
  default     = "assure-aat-pca-config-service"
}

variable "api_base_path" {
  description = "The API name exposure basepath"
  default     = "assure-aat-pca-config-service"
}

// constant vars for aat pca config service lambda
variable "assure_aat_pca_config_service_function_name" {
  description = "The lambda name"
  default     = "assure-aat-pca-config-service"
}

variable "bundle_type_assure_aat_pca_config_service_lambda" {
  default = "zip"
}
variable "bundle_path_assure_aat_pca_config_service_lambda" {
  default = "/assure-generic/assure-platform/assure-aat-pca-config-service/lambda/bundle"
}

variable "bundle_name_assure_aat_pca_config_service_lambda" {
  default = "assure-aat-pca-config-service-lambda-bundle"
}

variable "bundle_branch_assure_aat_pca_config_service_lambda" {
  description = "The name of the branch from which to obtain artifacts"
  type        = string
  default     = "master"
}

variable "endpoint_name" {
  default = "assure-aat-pca-config-service-endpoint"
}

// constant vars for assure answers config event handler lambda
variable "assure_aat_pca_config_event_handler_function_name" {
  description = "The lambda name"
  default     = "assure-aat-pca-config-event"
}

variable "bundle_type_assure_aat_pca_config_event_service_lambda" {
  default = "zip"
}
variable "bundle_path_assure_aat_pca_config_event_service_lambda" {
  default = "/assure-generic/assure-platform/assure-aat-pca-config-event-handler/lambda/bundle"
}

variable "bundle_name_assure_aat_pca_config_event_service_lambda" {
  default = "assure-aat-pca-config-event-handler-lambda-bundle"
}

variable "bundle_branch_assure_aat_pca_config_event_service_lambda" {
  description = "The name of the branch from which to obtain artifacts"
  type        = string
  default     = "master"
}


// constant vars for assure aat pca preprocessing handler lambda
variable "assure_aat_pca_preprocessing_handler_lambda_function_name" {
  description = "The lambda name"
  default     = "assure-aat-pca-preprocessing"
}

variable "bundle_type_assure_aat_pca_preprocessing_handler_lambda" {
  default = "zip"
}
variable "bundle_path_assure_aat_pca_preprocessing_handler_lambda" {
  default = "/assure-generic/assure-platform/applied-intelligence/pca/handler-lambda"
}

variable "bundle_name_assure_aat_pca_preprocessing_handler_lambda" {
  default = "handler-lambda-bundle"
}

variable "bundle_branch_assure_aat_pca_preprocessing_handler_lambda" {
  description = "The name of the branch from which to obtain artifacts"
  type        = string
  default     = "master"
}


# ? Pca service_name
variable "pca_service_name" {
  type = string
  description = "project genius name of this service"
  default = "pca"
}

# Pca model

variable "pca_model" {
  type = string
  description = "pca model name of this service"
  default = ""
}