#!/usr/bin/env groovy

/**
 * npm pipeline template: definition of a generic pipeline to perform the build for npm artifacts.
 */

import org.pdxc.rest.GitApi
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.util.DefaultConfiguration
import org.pdxc.util.FileUtils
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.assure.util.WarningUtils
import static groovy.json.JsonOutput.prettyPrint
import static groovy.json.JsonOutput.toJson
import org.assure.pushpipeline.PushPipelineUtils

boolean validBranch(String branch) {
   return (branch ==~ /(^release\/{1}+\d+\.\d+.*)/ ||
            branch ==~ /(^feature\/{1}+.*)/ ||
            branch ==~ /(^fix\/{1}+.*)/ ||
            branch == 'development' ||
            branch == 'master')
}

def call(LinkedHashMap stagesMap, String dockerPath = 'npm.Dockerfile') {
    String pipelineName = 'npm'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Calculated new version
    String newVersion
    // Name of the dockerFile
    String dockerName
    // Current repository name
    def repoName
    // QA vars
    def sonarStatusCode = 0
    def checkovStatusCode = 0

    PushPipelineUtils pushUtils
       
    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 1, unit: 'HOURS')
        }

        stages {
            stage ('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        warningUtils = new WarningUtils()
                        dockerName = "${pipelineName}.Dockerfile"
                    }
                }
            }
            stage('Pipeline info') {
                steps {
                    script {
                        def conf = libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
                        writeFile file: "${pipelineName}-conf.yml", text: conf
                        def defaultConfigYml = readYaml file: "${pipelineName}-conf.yml"
                        def repoData = readYaml file: 'conf.yml'
                        configData = defaultConfigYml + repoData
                        
                        println 'Loaded configuration values: \n\n' + prettyPrint(toJson(configData))

                        writeYaml file: 'conf.yml', data: configData, overwrite: true

                        pushUtils.setDockerAgentForCustomNodeAndNpmVersion(dockerPath, dockerName)
                        (buildDockerRegistryUrl, buildDockerRegistryCreds) = pushUtils.getDockerRegistryUrlAndCreds(configData, dockerName, pipelineName)
                        

                        pushUtils.executePostStageFunction(stagesMap, 'info')
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo "Configured repository name matches current repository: ${repoName}"

                        pushUtils.executePostStageFunction(stagesMap, 'validate')
                    }
                }
            }
            stage ('Artifact & Deploy') {
                agent {
                    dockerfile {
                        args '-u root:root'
                        filename 'npm.Dockerfile'
                        reuseNode true
                        registryCredentialsId buildDockerRegistryCreds
                        registryUrl buildDockerRegistryUrl 
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('Set up') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'setup') } }
                        steps {
                            script {
                                // Configure Git global data
                                def cred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'setup')
                                def mail = ValuesUtils.getVariable(configData, 'gitEmail', 'setup')
                                def user = ValuesUtils.getVariable(configData, 'gitUsername', 'setup')
                                def url = ValuesUtils.getVariable(configData, 'gitHubUrl', 'setup')
                                functiongroup_git.setup(cred, mail, user, url)

                                // Configure npm rc data
                                cred = ValuesUtils.getVariable(configData, 'npmrcCredential', 'setup')
                                functiongroup_npm.setup(cred)
                                sh "echo '//registry.npmjs.org/:always-auth=false' >> ~/.npmrc"

                                def (npmAuthToken, uuid) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.assure_creds_file, "creds/assureCustomCreds.yml.gpg")
                                                                                             
                                wrap([$class: 'MaskPasswordsBuildWrapper', varPasswordPairs: [[password: npmAuthToken.npm_auth_token]]]) {
                                    withEnv(["NPM_AUTH_TOKEN=${npmAuthToken.npm_auth_token}"]) {          
                                        if (configData.npmVersion >= 8) {
                                            sh script: 'sed -i "/^_auth/d" ~/.npmrc', label: "Remove auth line"
                                        }                                                                  
                                        sh script: 'echo "//artifactory.csc.com/artifactory/api/npm/diaas-npm-local/:_authToken=$NPM_AUTH_TOKEN" >> ~/.npmrc', label: 'Set up token NPMRC file'                                        
                                    }
                                }

                                // Check if package-lock.json exists
                                def packageLockPath = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'setup')                               
                                packageLockPath = ValuesUtils.removeStartEndChars(packageLockPath, '.', true, true)                               
                                packageLockPath = ValuesUtils.removeStartEndChars(packageLockPath, '/', true, true)                               
                                if (packageLockPath != "") packageLockPath += '/'                                
                                def localPackageLockPath = "${WORKSPACE}/" + packageLockPath + 'package-lock.json'                                
                                def exists = fileExists localPackageLockPath                                
                                if (exists) {
                                    sh script: """
                                                    set +x
                                                    echo '${npmAuthToken.npm_creds}' >> ~/.npmrc                                                    
                                                    set -x                                                    
                                            """, label: 'Set up creds NPMRC file'                                    
                                }

                                // Calculate and set new version to be built
                                echo 'Calculate and set new version:'
                                // Mover a utils
                                def file = ValuesUtils.getVariable(configData, 'dependenciesPackageFile', 'setup')
                                def attr = ValuesUtils.getVariable(configData, 'dependenciesPackageAttribute', 'setup')
                                def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'setup')
                                def currentVersion = FileUtils.getAttributeFromJsonFile(file, attr, path)                                                                
                                // newVersion = ("${BRANCH_NAME}" == 'master') ? currentVersion + "-beta.${env.BUILD_NUMBER}" : ("${BRANCH_NAME}" ==~ /(^release\/{1}+\d+\d+\.\d+.*)/) ? currentVersion : currentVersion + "-alpha.${env.BUILD_NUMBER}"
                                if ("${BRANCH_NAME}" == 'master') {                                    
                                    newVersion = currentVersion + "-beta.${env.BUILD_NUMBER}"
                                } else if ("${BRANCH_NAME}" ==~ /(^release\/{1}+\d+\.\d+.*)/) {
                                    newVersion = currentVersion
                                }
                                else {
                                    newVersion = currentVersion + "-alpha.${env.BUILD_NUMBER}"
                                }
                                
                                // newVersion = currentVersion + "+${env.BUILD_NUMBER}"
                                FileUtils.setValueOnJSONFileContent(file, attr, newVersion, path)
                                echo "Current Version: ${currentVersion} --- New Version: ${newVersion}"

                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Test') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'test') } }
                        steps {
                            script {
                                def commands = ValuesUtils.getVariableArrayList(configData, 'testCommands', 'test')

                                def dependenciesPath = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'test')
                                dependenciesPath = (dependenciesPath == null || dependenciesPath == '') ? '.' : dependenciesPath
                                def dependenciesPathModified = ValuesUtils.removeStartEndChars(dependenciesPath, '/', true, true)

                                commands.each { command -> 
                                    sh script: """  
                                                    cd ${dependenciesPathModified}
                                                    ${command}""", 
                                        label: "Execute command: ${command}"
                                }

                                sh """
                                        cd ${dependenciesPathModified}
                                        rm -rf node_modules
                                   """

                                junit(allowEmptyResults: true, testResults: '**/*.xml')

                                pushUtils.executePostStageFunction(stagesMap, 'test')
                            }
                        }
                    }
                    stage('Install') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'install') } }
                        steps {
                            script {
                                def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'install')
                                def scriptName = ValuesUtils.getVariable(configData, 'scriptName', 'install')
                                def scriptParams = ValuesUtils.getVariable(configData, 'scriptParams', 'install')
                                functiongroup_npm.executeScript(path, scriptName, scriptParams)

                                pushUtils.executePostStageFunction(stagesMap, 'install')
                            }
                        }
                    }
                    stage('Build') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'build') } }
                        steps {
                            script {
                                def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'build')
                                def scriptName = ValuesUtils.getVariable(configData, 'scriptName', 'build')
                                def scriptParams = ValuesUtils.getVariable(configData, 'scriptParams', 'build')
                                functiongroup_npm.executeScript(path, scriptName, scriptParams)

                                pushUtils.executePostStageFunction(stagesMap, 'build')
                            }
                        }
                    }
                    stage('Code quality'){
                        when { expression { pushUtils.notSkipStage(stagesMap, 'codequality') } }
                        parallel{
                                                        stage('OSS Governance') {
                                when { expression { pushUtils.notSkipStage(stagesMap, 'ossgovernance') } }                                
                                steps {
                                    script {    
                                        catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                            try{ 
                                                def nodeVersion = ValuesUtils.getVariable(configData, 'nodeVersion')
                                                if (nodeVersion.toInteger() < 20) {
                                                    echo "-- ℹ️ -- Node version not supported (requires 20+), skipping OSS Governance"
                                                } else {
                                                    def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'install')
                                                    String pathModified = ValuesUtils.removeStartEndChars(path, '/', true, true)
                                                    pathModified = pathModified.replace('./', '')
                                                    pathModified = pathModified.replace('.', '')

                                                    sh ( script: "npm install -g license-report", label: "Installing license report package")

                                                    def reportCss = libraryResource(resource: "styles/license-report.css")
                                                    writeFile(file: "${WORKSPACE}/license-report.css", text: reportCss)
                                                    
                                                    sh script: """
                                                                cd ./${pathModified}
                                                                license-report --output=html --html.cssFile=${WORKSPACE}/license-report.css --csvHeaders --fields=name --fields=licenseType --fields=installedVersion --fields=link > ${WORKSPACE}/ossGovernanceInfo.html
                                                            """,                                                
                                                    returnStdout: true,
                                                    label: 'Executing oss governance script'

                                                    // Add title to report
                                                    sh script: """
                                                        sed -i 's|<body>|<body><h1 style="text-align:center;margin:30px 0 10px;font-size:22px;">OSS Governance License Report</h1>|' ossGovernanceInfo.html
                                                    """, label: 'Inject title into report'

                                                    if (fileExists("${WORKSPACE}/ossGovernanceInfo.html")) {
                                                        archiveArtifacts artifacts: "ossGovernanceInfo.html"
                                                        sh script: "rm -f ${WORKSPACE}/ossGovernanceInfo.html", label: "Removing ossGovernanceInfo.html"
                                                    }     

                                                    sh script: "rm -f ${WORKSPACE}/license-report.css", label: "Removing license-report.css"
                                                }
                                            } catch (e) {
                                                echo "--❌️-- Error detected during OSS Governance process ${e.getMessage()}"
                                                throw e 
                                            }
                                        }
                                        pushUtils.executePostStageFunction(stagesMap, 'ossgovernance')                    
                                    }
                                }
                            }
                            stage('SonarQube scan') {
                                when { expression { pushUtils.notSkipStage(stagesMap, 'sonar') } }
                                    steps {
                                        script {
                                            withCredentials([string(credentialsId:'ASSURE-SONAR-HOST', variable:'SONARHOST')]) {
                                                withCredentials([string(credentialsId:'ASSURE-SONAR-TOKEN', variable:'SONARTOKEN')]) {
                                                    sonarStatusCode = pushUtils.sonarScan(SONARHOST, SONARTOKEN, configData, false, true, dockerName)
                                                }
                                            }
                                        }
                                    }
                            }
                            stage('Checkov Scan') {
                                    when { expression { pushUtils.notSkipStage(stagesMap, 'checkov') } }
                                    steps {
                                        script {
                                            
                                            def checkovQualityGate = ValuesUtils.getVariable(configData, 'checkovQualityGate', 'checkov')
                                            def (checkovData, uuidCheckov) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.checkov_conf_file, "checkov/checkovConfiguration.yml.gpg")
                                            def scriptPath = "checkov/checkov.sh"

                                            pushUtils.checkovScan('generic', repoName, repoName, scriptPath, uuidCheckov, dockerName)

                                            def checkName = 'Vulnerabilities'
                                            withChecks("${checkName}") {
                                                junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: "**/results*.xml")
                                            }
                                            pushUtils.markGitHubCheckNeutral(configData, checkName)

                                            checkovStatusCode = pushUtils.checkovErrorCheck(repoName, uuidCheckov)
                                            
                                            if(checkovStatusCode != 0 && checkovQualityGate){
                                                currentBuild.result = 'ABORTED'
                                                error ("-- ❌ -- Pipeline ABORTED ❗❗ Checkov Scan 🛸 status: FAIL (checkovQualityGate is ${checkovQualityGate})")
                                                return
                                            }
                                            
                                            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                if(checkovStatusCode != 0){
                                                    error("-- ⚠️ -- Checkov Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                                                    return
                                                }
                                            }
                                            pushUtils.executePostStageFunction(stagesMap, 'checkov')
                                            
                                        }
                                    }
                                }
                        }
                        post {
                            success {
                                script {
                                    pushUtils.executePostStageFunction(stagesMap, 'codequality')
                                }
                            }
                        }
                    }                 
                    stage('Upload artifact') {                        
                        when { expression { return ((pushUtils.notSkipStage(stagesMap, 'upload')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                        steps {
                            script {
                                if (!validBranch("${BRANCH_NAME}")) {
                                    echo 'Npm library will not be created as this is not a valid branch'
                                    currentBuild.description = 'Invalid branch'
                                } else {                                    
                                    echo 'Branch name is valid, preparing Npm library...'                                    
                                    
                                    def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'upload')                                    
                                    if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                                    
                                    def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'upload')
                                    def tag
                                    def branch = "${BRANCH_NAME}"

                                    if (branch == 'master') {
                                        tag = ValuesUtils.getVariable(configData, 'masterTag', 'upload')
                                    } else if (branch ==~ /(^feature\/{1}+.*)/ || branch ==~ /(^fix\/{1}+.*)/ || branch == 'development') {
                                        tag = ValuesUtils.getVariable(configData, 'betaTag', 'upload')
                                    } else if (branch ==~ /(^release\/{1}+\d+\.\d+.*)/) {
                                        tag = ValuesUtils.getVariable(configData, 'releaseTag', 'upload')
                                    } else {
                                        error('-- ❌ -- Something went wrong selecting the tag for the npm library')
                                    }

                                    def path = ValuesUtils.getVariable(configData, 'dependenciesPackagePath', 'upload')

                                    def publishPath = ValuesUtils.getVariable(configData, 'publishPath', 'upload')                                    
                                    if (publishPath != null) {
                                        publishPath = ValuesUtils.removeStartEndChars(publishPath, '/', true, false)
                                        path = path + '/' + publishPath
                                    }
                                    
                                    pushUtils.uploadNpmArtifact(path, repo, tag, artifactoryURL)                                     
                                }                                

                                pushUtils.executePostStageFunction(stagesMap, 'upload')
                            }
                        }
                    }
                }
            }
            stage('Generate Release Notes') { 
                // This stage generates release notes in github using the github api
                when {
                    expression { return (pushUtils.notSkipStage(stagesMap, 'releaseNotes') && (BRANCH_NAME == 'master') && (configData.releaseNotes == true)) }
                }
                steps {
                    script {
                            pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)

                            pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                    }
                }
            }            
            stage('Branch protection') {
                /* This stage updates the branch protection rule for BRANCH releases */
                when{
                    expression { return ((pushUtils.notSkipStage(stagesMap, 'protection')) && (BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/)) }
                }
                steps {
                    script {
                        def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                        pushUtils.branchProtect(repoName, gitHubCred)
                    }
                }
            }
        }
        post {
            always {
                script {
                    postActions(configData)
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}
