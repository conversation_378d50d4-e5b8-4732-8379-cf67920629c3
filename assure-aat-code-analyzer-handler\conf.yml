# Name of current repository for validation
repositoryName: "assure-aat-code-analyzer-handler"

# Artifactory configuration
artifactRepository: "assure-generic"
artifactPath: "assure-platform/code-analyzer/lambdas"

# Source folder containing the Lambda code
zipSourceFolder: "code"

##### GitHub data #####
gitHubCredential: "assure-github"
gitEmail: "jen<PERSON>@dxc.com"
gitUsername: "Jenkins User"

##### Notification (post)
sendMail: "false"
emailFrom: "<EMAIL>"
emailTo: "<EMAIL>"
attachmentFileEmail: ""

#### STATE THE RUNTIMES AND ARCHITECTURE FOR THIS LAMBDA FUNCTION
artifactProperties:
  - prop: "aws_lambda_runtimes"
    value: "python3.11"
  - prop: "aws_lambda_architectures"
    value: "x86_64"
  - prop: "status"
    value: "ready"
  - prop: "type"
    value: "generic"