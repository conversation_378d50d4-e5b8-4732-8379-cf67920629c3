/* groovylint-disable ThrowException */
package org.assure.util

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.DefaultConfiguration
import org.pdxc.util.Utils
import org.pdxc.exception.ContextNotSetException
import groovy.json.JsonOutput


/**
* Utility class
*/
class AssureUtils {

    private AssureUtils() { }

    // fileName should have extension
    static def decryptYmlFile(String credentials, String fileName, String targetPath='.') {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }
        def fileData
        
        context.withCredentials([context.string(credentialsId:credentials, variable:'PASSPHRASEVAR')]) {
            // context.writeFile file: "${targetPath}/passphrase.txt", text: "${context.env.PASSPHRASEVAR}"
            // context.sh "cd ${targetPath} && gpg --pinentry-mode loopback --passphrase-file=passphrase.txt --decrypt-files ${fileName}.gpg"
            context.sh "cd ${targetPath} && gpg --batch --passphrase ${context.env.PASSPHRASEVAR} ${fileName}.gpg"            
            fileData = context.readYaml file: "${targetPath}/${fileName}"
        }
        return fileData
    }

    static String getFromRegEx(def value, String regex) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }
        def result = null
        try {
            def matcher = (value =~ /${regex}/)
            if (matcher != null && matcher[0] != null) {
                if (matcher[0] instanceof String) {
                    result = matcher[0]
                } else {
                    result = matcher[0][0]
                }
            }
        } catch (Exception e) {
            context.echo "-- ❌ -- Unable to extract matching value from ${value} using regex ${regex}"            
        }
        return result
    }

    static def echoBanner(def msgs) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        if (msgs != null) {
            context.echo """
            ℹ️ 
            ===========================================

            ${msgs.join("\n    ")}

            ===========================================
            """
        }
    }

    // static def echoBanner(def msgs) {
    //     def context = JenkinsContext.getContext()
    //     if (context == null) {
    //         throw new ContextNotSetException()
    //     }
    //     context.echo createBanner(msgs)
    // }

    // static def errorBanner(def msgs) {
    //     def context = JenkinsContext.getContext()
    //     if (context == null) {
    //         throw new ContextNotSetException()
    //     }
    //     context.error(createBanner(msgs))
    // }

    // static def createBanner(def msgs) {
    //     return """
    // ℹ️ 
    // ===========================================

    // ${msgFlatten(null, msgs).join("\n    ")}

    // ===========================================
    //     """
    // }

    // static def createBanner(def ... msgs) {
    //     return """
    // ℹ️ 
    // ===========================================

    // ${msgFlatten(null, msgs).join("\n    ")}

    // ===========================================
    //     """
    // }

    // flatten function hack included in case Jenkins security
    // is set to preclude calling Groovy flatten() static method
    // NOTE: works well on all nested collections except a Map
    // static def msgFlatten(def list, def msgs) {
    //     list = list ?: []
    //     if (!(msgs instanceof String) && !(msgs instanceof GString)) {
    //         msgs.each { msg ->
    //             list = msgFlatten(list, msg)
    //         }
    //     }
    //     else {
    //         list += msgs
    //     }

    //     return  list
    // }

    static def printPrettyMessage(def message, String label, def prettyJson = true) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }
        
        String msg = (prettyJson) ? JsonOutput.prettyPrint(JsonOutput.toJson(message)) : message
        context.sh script: "{ echo \"${msg}\"; } 2> /dev/null", label: "${label}"
    }

    @NonCPS
    static List uniqueDuNameNonCPS(List<Map> list) {        
        return list.unique { a, b ->
           ((a.duName == b.duName) && (a.version == b.version)) ? 0 : 1
        }
    }

    @NonCPS
    static List uniqueServiceNameNonCPS(List<Map> list) {        
        return list.unique { a, b ->
           ((a.summary.service_name == b.summary.service_name) && (a.version == b.version)) ? 0 : 1
        }
    }

    static Map flattenHierarchyYml(Map conf) {   
        //regex    
        conf.regex.each { type ->    
            type.collect { typeItem,typeValues ->
                typeValues.collect { k,v -> 
                    conf.put('regex_' + typeItem + '_' + k, "${v}")
                }
            }    
        }
        
        //stages
        def stagesConf = []    
        conf.stages.findAll { item -> 
            stagesConf.add(item.name)
            conf.put('stages_' + item.name + '_type', "${item.type}")
            conf.put('environment_' + item.name + '_status_success', "${item.status_success}")
            conf.put('environment_' + item.name + '_status_failure', "${item.status_error}")
            conf.put(item.name + '_customerName', "${item.customer_name}")
            conf.put(item.name + '_accountName', "${item.account_name}")
            conf.put(item.name + '_environment_resource_name', "${item.resource_name}")
            conf.put(item.name + '_environment_endpoint', "${item.endpoint}")
            conf.put(item.name + '_environment_arn', "${item.arn}")
            conf.put(item.name + '_environment_region', "${item.region}")
            conf.put(item.name + '_environmentType', "${item.environment_type}")
            conf.put(item.name + '_testTokenUrl', "${item.test_token_url}")
            conf.put(item.name + '_rollback', "${item.rollback}")
        }

        conf.put('stages', stagesConf)

        //artifactoryaql        
        def artRepos = []
        def artPaths = []
        conf.artifactory.repositories.findAll { item ->
            artRepos.add(item.repository)
            conf.put('artifactory_' + item.repository +'_repotype', item.type)
            item.paths.findAll { itemPath ->                
                artPaths.add(itemPath.path)                
                conf.put('artifactory_' + item.repository + '_' + "${itemPath.path.replaceAll('/', '-')}" +'_branches', itemPath.branches)
            }
            conf.put('artifactory_' + item.repository +'_paths', artPaths)
            conf.put('artifactory_' + item.repository +'_status', item.status)
            artPaths = []
        }

        conf.put('artifactory_repos', artRepos)

        conf.put('artifactory_nodeployprop', conf.artifactory.deployment_in_progress)

        //status_stage_mapping
        def statusStageMapping = []
        conf.status_stage_mapping.findAll { item ->
            statusStageMapping.add([item.status, item.stage])
        }
        conf.put('environment_status_mapping', statusStageMapping)

        //e2e
        conf.e2eTest.findAll { item ->
            conf.put('e2eTest_repository', item.repository)
            conf.put('e2eTest_branch', item.branch)
            conf.put('e2eTest_org', item.organization)
        }

        // remove old keys
        def removeKeys = ['status_stage_mapping', 'artifactory', 'regex']
        removeKeys.each { k ->
            conf.remove(k)
        }

        return conf
    }

    static String setCypressVersion(LinkedHashMap data) {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }
        
        def projectType = 'promotion'        
        def versionImageResource = context.libraryResource(resource: "custom/${projectType}-project/cypress-docker.yml") 
        context.writeFile file: "cypress-docker.yml", text: versionImageResource
        def versionImageYml = context.readYaml file: "cypress-docker.yml"         
        def mayorVersion = (data.cypressVersion == null) ? '8' : data.cypressVersion        
        def dockerBaseVersion = versionImageYml."cypress-${mayorVersion}"   

        context.echo "🌲 cypress version: ${dockerBaseVersion}"

        if (dockerBaseVersion == null) context.error "The cypress version ${data.cypressVersion} is not supported"

        return dockerBaseVersion        
    }

    /**
    * This method Compose the Blue Ocean Jenkins Url using the environment variables in the pipeline.
    * @param env Environment variables to be invoked.
    * @return blueOceanUri Blue Ocean Jenkins Url.
    */
    static def getBlueOceanUri (def env) {
        def organization = ("${env.BUILD_URL}" =~ /(?:[job])(\/.*?\/)(?:[job])/)[0][1]
        organization = organization.replaceAll('/', '')

        def jobRepoName = AssureUtils.getFromRegEx(env.JOB_NAME, "/[^/]*/")
        jobRepoName = jobRepoName.replaceAll('/', '')

        def blueOceanURL = "https://jenkins.dxc.com/blue/organizations/jenkins/${organization}%2F${jobRepoName}/detail/"
        def branchURL = "${env.GIT_BRANCH}".replaceAll('/', '%2F')
        def blueOceanUri = blueOceanURL + "${branchURL}/${env.BUILD_NUMBER}/pipeline"
     
        return blueOceanUri
    }

    static def gitSetupCred(String credentials, String email = '<EMAIL>', String username = 'Jenkins User', String url = '') {
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        if (credentials == null || credentials == '') {            
            Utils.throwExceptionInstance('IllegalArgumentException', 'Missing required input parameters')
        }

        url = (url == null || url == '') ? DefaultConfiguration.PDXC_GITHUB_HOST : url

        context.withCredentials([context.usernamePassword(credentialsId: credentials, passwordVariable: 'GIT_PASSWORD', usernameVariable: 'GIT_USER')]) {
            context.sh script: """
                touch ~/.netrc
                echo 'machine ${url}' >> ~/.netrc
                echo 'login ${context.env.GIT_USER}' >> ~/.netrc
                echo 'password ${context.env.GIT_PASSWORD}' >> ~/.netrc
                git config --global --replace-all user.email '${context.env.GIT_USER}@dxc.com'
                git config --global --replace-all user.name '${context.env.GIT_USER}'           
                """, label: 'Set up Git credentials'
        }        
    }

    static def dependaBotParse(def advisories) {

        def advisoriesList = []
        def metaDataList = []
        def metaDataMap = [:]
        
        metaDataMap.put('critical', (advisories.size() == 0) ? 0 : advisories?.security_vulnerability?.severity?.count('critical'))
        metaDataMap.put('high', (advisories.size() == 0) ? 0 : advisories?.security_vulnerability?.severity?.count('high'))
        metaDataMap.put('medium', (advisories.size() == 0) ? 0 : advisories?.security_vulnerability?.severity?.count('medium'))
        metaDataMap.put('low', (advisories.size() == 0) ? 0 : advisories?.security_vulnerability?.severity?.count('low'))
        metaDataMap.put('info', (advisories.size() == 0 ) ? 0 : advisories?.security_vulnerability?.severity?.count('info'))

        def vulnerabilityMetaDataMap = [:]

        vulnerabilityMetaDataMap.put('vulnerabilities', metaDataMap)
        metaDataList.add(vulnerabilityMetaDataMap)

        if (advisories.size() != 0 ) {
            
            advisories.each { k ->

                def advisoriesMap = [:]

                def installedVersion = k?.findings?.version

                advisoriesMap.put('package', k?.dependency.package.name)            
                advisoriesMap.put('installed_version', k?.security_vulnerability?.vulnerable_version_range)
                advisoriesMap.put('vulnerable_versions', k?.security_vulnerability?.vulnerable_version_range)
                advisoriesMap.put('patched_versions', k?.security_advisory?.vulnerabilities?.first_patched_version?.identifier)            
                advisoriesMap.put('severity', k?.security_advisory?.severity)
                advisoriesMap.put('title', k?.security_advisory?.summary)
                advisoriesMap.put('recommendation', k?.security_advisory?.vulnerabilities?.first_patched_version?.identifier)
                advisoriesMap.put('cves', k?.security_advisory?.cve_id)

                advisoriesList.add(advisoriesMap)
            }
        }        

        return [advisoriesList , metaDataList]
    }
}