## Upgrading Terraform version from v0.13 to v1.4
terraform {
  required_version = ">= 0.13"
  required_providers {
    external = {
      source = "hashicorp/external"
	  version = "~> 2.2.3"
    }
    aws = {
      source = "hashicorp/aws"
	  version = "~> 5.0"
    }
    template = {
      source = "hashicorp/template"
	  version = "~> 2.2.0"
    }
    null = {
      source = "hashicorp/null"
	  version = "~> 3.2.0"
    }
    local = {
      source = "hashicorp/local"
      version = ">= 1.8.0"
    }
  }
}