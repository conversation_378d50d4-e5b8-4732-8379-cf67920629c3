data "template_file" "sagemaker_assume_role_policy_template" {
  template = file("${path.module}/templates/sagemaker_assume_role_policy.json")
}

data "template_file" "amazon_bedrock_policy_template" {
  template = file("${path.module}/templates/amazon_bedrock_policy.json")
  vars = {
     aws_region = var.aws_region
     aws_account_number = local.aws_target_account
  }
}

data "aws_sagemaker_prebuilt_ecr_image" "sagemaker_prebuilt_ecr_image" {
  repository_name = var.sagemaker_prebuilt_ecr_image_repository_name
  image_tag       = var.sagemaker_prebuilt_ecr_image_tag
}