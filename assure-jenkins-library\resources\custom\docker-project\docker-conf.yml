##### Notification (post)
sendMail: "false"
emailFrom: "<EMAIL>"
emailTo: ""
attachmentFileEmail: ""
#teamsSecretHookId: "TEAMS_PUSH_WEBHOOK"

##### Artifactory
artifactoryUrl: "docker.dxc.com"
artifactoryCredentials: "diaas-rw"
artifactRepository: "diaas-docker"
artifactProperties:
  - prop: "status"
    value: "ready"
  - prop: "type"
    value: "docker"

# Docker tag: this version will be composed by artifactoryTag + build_number
artifactoryTag: "0.0.0"

##### Checkov
checkov_conf_file: "checkovConfiguration.yml"
passphrase_id: "PASSPHRASE_ID"
checkovQualityGate: false

### Prisma
prisma_conf_file: "prismaConfiguration.yml"
prismaQualityGate: false

###### Environment service configuration
env_service_conf_file: "envServData.yml"

###### Environment Status Maping
environment_status_mapping: [
["ready", "sdlc-environment"]
]

###### Docker inspect
artifactRepositoryJson: "assure-generic"

###### Testing
testResultsPath: "**/*.xml"

###### Release Notes Auto-Generation
releaseNotes: false

###### Node version
nodeVersion: 18

###### Npm version
npmVersion: 9
