############################################################################################################
####################################### MODIFICATION AREA ###################################################
######################## Modify these values according to your project needs.  ##############################
### Rest of customizable values are listed in README.md section 2.1.3 Common custom configuration values  ###
############################## Only modify them is special behaviour is needed ##############################
#############################################################################################################

#### Name of current repository for validation. It has to match the name of the repository where this file is.
repositoryName: "name-of-current-repository-validation"

###### .Net version
dotnetVersion: 7

#### Artifactory
artifactPath: "assure-platform/artifactpath"  #i.e: "assure-document/document-management/library-service/bundle"

##### Zip (zip)
targetZipName: "name-of-the-targetZipName"    #i.e: "DMS.Permissions.API"
zipSourceFolder: "ArtifactFolder"             #i.e: "./", "./code"

#dotNet
csProjectPath: "../../."        #i.e: "src/Services/SecurityData/SecurityData.Api.Host.Lambda/SecurityData.Api.Host.Lambda.csproj"
sProjectTestPath: "../../."     #i.e: "Services/SecurityData/SecurityData.Api.Host.UnitTests/SecurityData.Api.Host.UnitTests.csproj"
pack_commandParams: "src/.."    #i.e: "src/DMS.ACL.API/DMS.ACL.API.csproj -c Release --no-restore -o ArtifactFolder"

#############################################################################################################
################################### END OF MODIFICATION AREA ################################################
#############################################################################################################

##### GitHub data #####
gitHubCredential: "pdxc-jenkins" # for assure, assure-external or assure-delivery org the value is: "assure-github"
gitEmail: "<EMAIL>"
gitUsername: "Jenkins User"

#dotNet
restore_commandName: "restore"
restore_commandParams: "*.sln --configfile ~/.config/NuGet/NuGet.Config"
build_commandName: "build"
build_commandParams: "*.sln"
pack_commandName: "publish"