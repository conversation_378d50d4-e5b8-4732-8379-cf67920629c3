FROM mcr.microsoft.com/dotnet/sdk:6.0
RUN apt-get update
# Nuget
RUN apt-get -y install nuget
# Mono-complete for certmgr
RUN apt-get -y install mono-complete
# Install certificate for Artifactory
RUN yes | certmgr -ssl https://artifactory.platformdxc-mg.com

#Checkov
RUN wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
RUN chmod a+x /usr/local/bin/yq

RUN apt-get install -y python3-pip \
    && pip3 install --upgrade pip setuptools \
    && pip3 install -U checkov