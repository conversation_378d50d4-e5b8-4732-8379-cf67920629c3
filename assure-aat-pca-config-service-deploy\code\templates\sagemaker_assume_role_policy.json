{"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Principal": {"Service": ["sagemaker.amazonaws.com", "apigateway.amazonaws.com", "batch.amazonaws.com", "s3.amazonaws.com", "secretsmanager.amazonaws.com", "states.amazonaws.com", "textract.amazonaws.com", "sts.amazonaws.com", "iam.amazonaws.com", "bedrock.amazonaws.com", "ec2.amazonaws.com", "ec2.application-autoscaling.amazonaws.com", "ec2fleet.amazonaws.com", "ec2scheduled.amazonaws.com", "ecr.amazonaws.com", "ecs-tasks.amazonaws.com", "ecs.amazonaws.com", "ecs.application-autoscaling.amazonaws.com"]}, "Effect": "Allow"}]}