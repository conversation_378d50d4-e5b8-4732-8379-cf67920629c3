_MYHOME="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
##
## RESOLVE ALL MODULE REFERENCES IN THE DU IN THE CWD TO LOCAL COPIES OF THE REPO,
## IN THE PROCESS PASS ALL MODULE SOURCES THROUGH THE gpp PREPROCESSOR.
##
## The script is called with the CWD being the root code directory of the DU Terraform
## scripting.
##
## The following is then executed:
##
##   * Run the gpp preprocessor on the DU TF code
##   * Find all unique "repos" referenced in the DU TF code "source" statements.
##     Repos are identified by the source string starting with "git::..." or, in
##     testing situations, "/..."
##     "Uniquity" is established as the URL plus any "?ref=" suffix being unique
##   * For each unique repo (eliminating dynamically added duplicates):
##     * Clone/Copy the repo into a unique directory created within any subdirectory
##       passed as an argument to the script, the default being the CWD at start.
##     * Record a "sed template" that can be used to change refrences to that repo
##       into references to the target directory of the clone/copy.
##     * Find all unique repos referenced within the source statements of the repo's
##       own TF code and add to the list of repos to be handled by the loop (hence 
##       the need for the FOR loop to eliminate dynamically added duplicates).
##     * Run the preprocessor on the repo TF code
##
##   * For the CWD and each clone/copy target directory that is not in the CWD tree
##     * For each TF code file that contains repo references (files sorted by location)
##       * For each "sed template"
##         * Adjust the sed template to provide the correct relative directory prefix
##           for the file (rationalized so that this only needs doing when the current
##           file's directory is different from that of the previous file's)
##         * Run sed on the file using the hydrated template
##
## The result is each git and "/" repo being internalized and preprocessed, and all TF
## code repo references modified to reference the target directory of the clone/copy.
##
## Quirks:
##
##   When the repo is identified as "/assure/assure-platform-terraform-modules" and 
##   no "?ref=" suffix is present on the source reference, the script can apply a default
##   default branch, and can adjust that branch if told to do so and the semantics are 
##   correct.
##
##   In effect, if the default branch is of the format release/nn.nn.nn and the "branch 
##   mode" is set to "version", then the script will change that branch name name to
##   "release/nn.nn.latest-nn".
##
##   The script can ALSO fall back to "master" if the default branch does not exist
##   ("branch mode" is set to "failsafe".
##
##   At some point in the future this will change.
##
. $_MYHOME/tf-repo-functions.sh

#
# If the script home isn't at the front of the PATH, make it so
#
[[ "$PATH" == *${_MYHOME} ]] || PATH="$_MYHOME:$PATH"

##
## FOR NOW FANCY VERSION CHECKING IS ONLY DONE FOR THE STANDARD ASSURE MODULES
##
enhanced_version_handling="/assure/assure-platform-terraform-modules"

#
# This is an independent implementation for getting TF details and
# AWS provider version. In the TF service these items will already be
# present in the environment. In the pipelines, they won't be. So
# we get them again, in a more introspective manner
#
function get_versions {
	local awsProviderVersion
	#
	# Set the Terraform stuff using Terraform's own env file
	#
	local tf_exec_dir=$(dirname $(which terraform)) || { echo "Internal error: terraform executable not found"; exit 1; }
	[[ -f "$tf_exec_dir/tf-env.sh" ]] || { echo "Internal error: tf-env.sh not found in $tf_exec_dir"; exit 1; }
	. "$tf_exec_dir/tf-env.sh"

	#
	# Slightly trickier with AWS provider version - we have to get it from a file
	#
	if [[ -f "./versions.tf.json" ]]; then
		awsProviderVersion=$(jq -r '.terraform[] | select (.required_providers  != null) | .required_providers[] | select (.aws != null) | select (.aws.version != null) | .aws.version' "./versions.tf.json")
	fi	
	if [[ -z "$awsProviderVersion" && -f "./versions.tf" ]]; then
		awsProviderVersion=$(hcl2json "./versions.tf" | jq -r '.terraform[] | select (.required_providers  != null) | .required_providers[] | select (.aws != null) | select (.aws.version != null) | .aws.version')
	fi
	if [[ -z "$awsProviderVersion" && -f "./aws.tf.json" ]]; then
		awsProviderVersion=$(jq -r '.provider.aws[] | select (.version != null) | .version' "./aws.tf.json")
	fi
	if [[ -z "$awsProviderVersion" && -f "./aws.tf" ]]; then
		awsProviderVersion=$(hcl2json "./aws.tf" |jq -r '.provider.aws[] | select (.version != null) | .version')
	fi
	
	[[ "$awsProviderVersion" ]] ||	awsProviderVersion="~> 3.0"
	
	awsProviderVersion=$(echo $awsProviderVersion|tr -d '>~ ')
	export AWS_PROVIDER_MAJOR_VERSION=${awsProviderVersion%%.*}
}

#
# Get all the uncommented TF source statements that refer to GIT or local absolute
# paths (for testing executions only) and retrieve the repository URLs, normalized 
# to not have ".git" suffix
#
# Called as a sub process with the response being the output of the pipelines
#
function get_sources {
	grep -r '^[[:blank:]]*source[[:blank:]]*=[[:blank:]]*"git::.*"' . | grep  '^\.[^:]*\.tf:[[:blank:]]*source' | sed 's|^.*"git::\(.*\)//[^?]*\(?.*\)\?"|\1\2|' | sed 's/\.git//' | sort -u
	grep -r '^[[:blank:]]*source[[:blank:]]*=[[:blank:]]*"/.*"' . | grep  '^\.[^:]*\.tf:[[:blank:]]*source' | sed 's|^.*"\(/.*\)//[^?]*\(?.*\)\?"|\1\2|' | sort -u
}

#
# Composes functions from tf-repo-functions.sh into the behaviour we need
# in order to stage a TF modules repo
#
function stage_source_repo {
	local url="$1" 
	local target="$2"
	local ref="$3"
	local default_branch="$4"
	local branch_mode="$5"

	echo "Fetching source repo $url (URL reference: $ref)" >&2
	echo "   into $target" >&2

	fetch_repo "$url" "$target" || { echo "Failed to fetch $url into $target" >&2; return 1; }
	if [[ "$_REPO_TYPE" == "remote" ]]; then
		if [[ "${url,,}" == *"$enhanced_version_handling" ]]; then
			establish_branch_name "$target" "${ref:-$default_branch}" "$branch_mode" || { echo "Failed to find matching branch for $ref in repo in $target. branch_mode: $branch_mode" >&2; return 1; }
		else
			_GIT_BRANCH_NAME="${ref:-master}"
		fi
		checkout_branch "$target" "$_GIT_BRANCH_NAME" || { echo "Failed to checkout branch $_GIT_BRANCH_NAME in repo in $target. branch_mode: $branch_mode" >&2; return 1; }
		cleanup_git_repo "$target"
	fi
}

#
# Echo all dirs that are not subdirectories of the first dir in
# the argument list. The first dir is always echoed.
#
function get_independent_dirs {
	reference_dir="$1"
	shift

	echo "$reference_dir"
	for target_dir in "$@"; do
		[[ "$target_dir" == "${reference_dir}/"* ]] || echo "$target_dir"
	done
}

function resolve_modules {
	local repo_home="$1"
	local default_branch="$2"
	local branch_mode="$3"
	local my_cwd=$(pwd)

	echo "Staging modules referenced in $my_cwd" >&2
	echo "into $repo_home" >&2
	echo "  default branch: $default_branch" >&2
	echo "  branch mode: $branch_mode"  >&2
	echo "  Terraform version: $TERRAFORM_VERSION"  >&2
	echo "  Terraform version number: $TERRAFORM_MAJOR_VERSION_NUMBER"  >&2
	echo "  AWS provider version: $AWS_PROVIDER_MAJOR_VERSION" >&2

	local ref url dirname

	#
	# An associative array "target-dir->repo"
	#
	declare -A sed_command_templates

	#
	# Get the referenced source repos from the current directory
	#
	local sources=( $(get_sources) )

	#
	# Loop through the source repos
	# sources() gets appended to in this operation this form of for
	# loop allows for that 
	#
	for (( i=0; i<${#sources[@]}; i++ )); do
		src=${sources[$i]}

		#
		# Get any ?ref= suffix
		#
		ref=${src##*\?ref=}
		[[ "$ref" == $src ]] && ref=""

		#
		# Get the URL without any ?ref= suffix
		#
		url=${src%%\?ref=*}

		#
		# Derive the subdirectory of the passed-n repo home directory
		# into which the current source repo should staged.
		#
		dirname="$repo_home/${url//\//_}${ref:+_${ref//\//_}}"

		#
		# If that does NOT already exist (i.e. we haven't already handled it)...
		#
		if [[ ! -e "$dirname" ]]; then
			#
			# Fetch the repo into the derived target directory
			#
			stage_source_repo "$url" "$dirname" "$ref" "$default_branch" "$branch_mode" || exit 1

			#
			# Create template sed command to transform references to that repo, and key it by the
			# target directory path
			#
			[[ "$_REPO_TYPE" == "remote" ]] && git_prefix="git::" || git_prefix=""
			sed_command_templates[$dirname]="s%^\s*source\s*=\s*\"${git_prefix}${url}\(.git\)\?//\\([^\"?]*\\)${ref:+?ref=$ref}\"%  source = \"__PREFIX__//\\2\"%"
	
			#
			# CD into the target directory
			#
			cd $dirname
			#
			# Preprocess new repo (we have to do this here so that even "source" declarations can be surrounded
			# preprocessor code).
			#
			run-preprocessor.sh "_GPP_TERRAFORM_VERSION=$TERRAFORM_VERSION" "_GPP_TERRAFORM_VERSION_NUMBER=$TERRAFORM_MAJOR_VERSION_NUMBER" "_GPP_TERRAFORM_AWS_PROVIDER_VERSION=$AWS_PROVIDER_MAJOR_VERSION"
			#
			# Append any referenced source repos from the repos own directory to the list of repos
			#
			# This can produce duplicate entries, but they are ignored and then
			# filtered out by the if statement this runs in
			#
			sources+=( $(get_sources) )
			cd $my_cwd
		fi
	done

	#
	# We now have all referenced repos staged locally and preprocessed
	# Now run the SED commands on all files that have source statements
	#

	#
	# Determine a list "independent" target directorys - directorys that aren't
	# subdirectorys of the cwd (where we started the process from)
	#
	# Technically this allows the caller to place refferred-to repos outside of
	# this directory.
	#
	transform_dirs=( $(get_independent_dirs "$my_cwd" "${!sed_command_templates[@]}") )

	#
	# A cache to avoid hydrating the template SED commands for every file. Instead
	# we sort the input file list and hydrate the templates only when we detect
	# a directory change
	#
	local current_sed_program_dir=""
	local current_sed_program=()

	for transform_dir in "${transform_dirs[@]}"; do
		echo "Rewriting external module references in $transform_dir" >&2
		cd "$transform_dir"
		while read -r -a module_caller; do
			module_caller_dir=$(dirname $(realpath "$module_caller"))

			#
			# If the current module caller is in a different directory to the
			# previous one, rehydrate the SED commands to insert the relative
			# path to the staged module repo.
			#
			if [[ "$module_caller_dir" != "$current_sed_program_dir" ]]; then
				current_sed_program_dir="$module_caller_dir"
				current_sed_program=()
				for sed_command_templates_dir in "${!sed_command_templates[@]}"; do
					sed_command_templates_PREFIX="$(realpath "--relative-to=$module_caller_dir" "$sed_command_templates_dir")"
					current_sed_program+=( "${sed_command_templates[$sed_command_templates_dir]//__PREFIX__/./$sed_command_templates_PREFIX}" ) 
				done
			fi

			#
			# Apply the SED commands to the current module caller
			#
			sed -i "$(printf "%s\n" "${current_sed_program[@]}")" "$module_caller"
		done < <( grep -lr '^[[:blank:]]*source[[:blank:]]*=[[:blank:]]*"\(\(git::\)\|\(/\)\)' | grep "\.tf[[:blank:]]*$" | sort )
		cd $my_cwd
	done
}

###
### THE MAIN PROGRAM
###
### CWD is expected to be the home code directory of the DU that
### is being resolved
###
### Arguments are:
###
###  The default branch to use - defaults to whatever the GIT 
###    default branch is master
###  The branch mode - fixed-version, failsafe, release
###    defaults to failsafe
###  The terraform major version NUMBER to pass to the preprocessor
###    defaults to 12 
###  The AWS provider version to pass to the preprocessor
###    defaults to 3
###  The target directory for staged repos 
###    defaults to the CWD
###
default_branch=${1:-master}
branch_mode=${2:-failsafe}
target_directory=$(realpath ${3:-.})

[[ -d "$target_directory" ]] || mkdir -p "$target_directory" || { echo "Failed to create $target_directory" >&2; exit 1; }

#
# Sort of various version settings
#
get_versions

#
# Preprocess the initial sources (we have to do this here so that even "source" declarations can be surrounded
# preprocessor code).
#
run-preprocessor.sh "_GPP_TERRAFORM_VERSION=$TERRAFORM_VERSION" "_GPP_TERRAFORM_VERSION_NUMBER=$TERRAFORM_MAJOR_VERSION_NUMBER" "_GPP_TERRAFORM_AWS_PROVIDER_VERSION=$AWS_PROVIDER_MAJOR_VERSION"
resolve_modules "$target_directory" "$default_branch" "$branch_mode"

if [[ "${DO_STAGE_SELF_CHECK,,}" == "yes" ]]; then
	echo "Performing self check with terraform get" >&2
	terraform get || { echo "Terraform get failed for DU in $(pwd)" >&2; exit 1; }
	rm -rf .terraform
fi
