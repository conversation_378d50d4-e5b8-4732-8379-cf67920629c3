/* groovylint-disable SpaceAfterOpeningBrace, TrailingWhitespace */
package org.assure.pushpipeline

import org.pdxc.util.DefaultConfiguration
import org.pdxc.util.Utils
import org.pdxc.util.FileUtils
import org.pdxc.util.ValuesUtils
import org.pdxc.rest.ArtifactoryApi
import org.assure.util.AssureUtils
import org.assure.pushpipeline.PushPipelineUtils
import org.pdxc.jenkins.JenkinsContext

/**
 * Utility class used to define a set of functions that can be reused across the different Assure pipelines but that
 * are too specific to be moved to a Global Jenkins Shared Library.
 */

class RustPipelineUtils {

    private RustPipelineUtils() { }

    def context = JenkinsContext.getContext()
    PushPipelineUtils pushUtils = new PushPipelineUtils()

    def targetRepositoryType = null
    def targetRepository = null
    def targetRepositoryCargoId = null
    def artifactoryURL = null
    def buildSources = ''

    // Isolate the "stage warning" hack
    def stageWarning(def message, def buildStatus = 'SUCCESS') {
        context.catchError(buildResult: buildStatus, stageResult: 'UNSTABLE') {
            context.error(message)
        }
    }

    /*
     * Within the container, set up the necessary configuration context to allow the
     * Rust project to access any Artifactory-hosted crates registries that it needs.
     *
     * Both the git and sparse protocols are supported.
     *
     * The project itself is responsible for declaring the  index URLs of the registries 
     * that it will be using (including those that its DEPENDENCIES use) in the usual way
     * (i.e. via a .cargo/config.toml entry at the top level of the project).
     *
     * If the Cargo token for a registry is stored in a Jenkins credential with the same ID as the
     * Artifactory registry name, then the project can refer to that registry by whatever internal 
     * ID it wants and the scripting here will find it (and impose the normal Cargo configuration
     * hierarchy in doing so).
     *
     * However, the project has full control over the registry IDs and credential names to use
     * via the conf.yml file, where the following structure will override any introspected 
     * information derived by the scripts:
     *
     *     artifactoryCrateRegistries:
     *         - id: <the internal name used for the registry>
     *           credentials: <the id of the Jenkins string credentials to use>
     *         ...
     */
    def setArtifactoryRegistryCredentials() {
        def credentials = [[$class: 'UsernamePasswordBinding', credentialsId: 'diaas-rw', variable: 'ARTIFACTORY_CREDS']]
        def registries = []

        def filetext = context.libraryResource(resource: 'custom/rust-project/scripts/findArtifactoryRegistries.sh')
        context.writeFile(file: 'findArtifactoryRegistries.sh', text: filetext)

        context.sh(
            /*
             * This script obtains the registry IDs and credential IDs from the project configuration files
             */
            script: "bash ./findArtifactoryRegistries.sh ${buildSources}",
            returnStdout: true,
            encoding: 'UTF-8',
            label: 'Discovering required Artifactory-hosted crate registries'
            /*
             * We then build a list of credential objects and a corresponding list of registry IDs
             */
        ).readLines().eachWithIndex() { registry, index ->
            def (registryId, credentialsId) = registry.trim().split(':')
            registries.add(registryId)
            credentials.add([$class: 'StringBinding', credentialsId: credentialsId, variable: "REGISTRY_TOKEN_${index}"])
        }

        filetext = context.libraryResource(resource: 'custom/rust-project/scripts/setArtifactoryRegistryCredentials.sh')
        context.writeFile(file: 'setArtifactoryRegistryCredentials.sh', text: filetext)

        /*
         * We then create a credentials context with all the discovered credentials and execute a
         * script that sets up the GIT and Cargo configuration structures necessary to access the
         * Artifactory-hosted crate registries
         */
        context.withCredentials(credentials) {
            context.sh(script: "bash ./setArtifactoryRegistryCredentials.sh ${registries.join(' ')}")
        }
    }

    def setTargetRepositoryDetails(def configData) {
        targetRepository = ValuesUtils.getVariable(configData, 'artifactRepository')
        if (targetRepository != null && targetRepository != '') {
            targetRepositoryType = context.sh(
                script: """
                    #!/bin/bash
                    set +x
                    export CI=true
                    jf rt curl api/repositories/${targetRepository} | jq -r '.packageType' | sed 's/null//'
                """,
                returnStdout: true,
                encoding: 'UTF-8',
                label: 'Discover artifact target repository type'
            ).trim()
            if (targetRepositoryType == 'cargo') {
                targetRepositoryCargoId = context.sh(
                    script: """
                        #!/bin/bash
                        set +x
                        if [[ -f "./registry-ids" ]]; then
                            grep "^${targetRepository}" ./registry-ids | sed 's/^.*=//' | head -1
                        fi
                    """,
                    returnStdout: true,
                    encoding: 'UTF-8',
                    label: 'Discover target crates registry Cargo ID'
                ).trim()
                if (targetRepositoryCargoId == '') {
                    targetRepositoryCargoId = null
                }
            }
        }
    }

    def commonRustSetupSteps(def configData) {
        buildSources = ValuesUtils.getVariable(configData, 'dependenciesPackagePath')
        buildSources = ValuesUtils.removeStartEndChars(buildSources, '/', true, false)
        if (buildSources != '' && buildSources[0] == '/') {
            Utils.throwExceptionInstance('IllegalArgumentException', "Invalid path provided: ${buildSources}")
        }
        
        artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'upload')
        if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL

        def cred = ValuesUtils.getVariable(configData, 'gitHubCredential')
        def mail = ValuesUtils.getVariable(configData, 'gitEmail')
        def user = ValuesUtils.getVariable(configData, 'gitUsername')
        def url = ValuesUtils.getVariable(configData, 'gitHubUrl')

        AssureUtils.gitSetupCred(cred, mail, user, url)

        // Calculate and set new version to be built
        context.echo('Calculate and set new version:')
        def file = ValuesUtils.getVariable(configData, 'dependenciesPackageFile', 'setup')
        def attr = ValuesUtils.getVariable(configData, 'dependenciesPackageAttribute', 'setup')
        def fileExtension = ValuesUtils.getVariable(configData, 'fileExtension', 'setup')

        def props = context.readProperties file: "./${buildSources}/${file}"

        def currentVersion = props[attr]
        currentVersion = ValuesUtils.removeStartEndChars(currentVersion, '"', true, true)

        def newVersion = currentVersion + "+${context.env.BUILD_NUMBER}"

        context.echo("Current Version: ${currentVersion} --- New Version: ${newVersion}")

        setArtifactoryRegistryCredentials()
        setTargetRepositoryDetails(configData)

        return [newVersion, currentVersion, fileExtension, [name:targetRepository, type:targetRepositoryType, cargoId:targetRepositoryCargoId]]
    }

    def multiRustSetupSteps(def configData, def dockerTargetName) {
        def newVersion
        def currentVersion
        def fileExtension
        def targetRepoInfo

        (newVersion, currentVersion, fileExtension, targetRepoInfo) = commonRustSetupSteps(configData) 

        def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)

        // Read the architectures file, and get the architectures to build for the project.
        def archs = context.readYaml(text: context.libraryResource(resource: "custom/${projectType}-project/${projectType}-archs.yml"))

        def finalArchitectures = []
        ValuesUtils.getVariableArrayList(configData, 'targetArchitectures', 'build').each {architecture ->
            def arch = archs[architecture]
            if (arch != null) {
                finalArchitectures.add(arch)
            } else {
                context.echo("--⚠️-- Architecture ${architecture} not found in the default architecture list. 🏗️ Loading ${architecture} as custom architecture...")
                finalArchitectures.add(architecture)
            }
        }

        if (finalArchitectures.size() == 0) {
            context.error('--❌️-- No valid architectures found. Please check the configuration file.')
        }

        // Print the selected architectures
        context.echo("-- 🏗️ -- Target Architectures selected: ${finalArchitectures}")
        
        return [newVersion, currentVersion, fileExtension, targetRepoInfo, finalArchitectures]
    }

    def rustSetupSteps(def configData) {
        def newVersion
        def currentVersion
        def fileExtension
        def targetRepoInfo
        (newVersion, currentVersion, fileExtension, targetRepoInfo) = commonRustSetupSteps(configData) 

        def targetArch = ValuesUtils.getVariable(configData, 'targetArchitecture', 'build')
        if (targetArch != null || targetArch != '') {
            context.echo("-- 🏗️ -- Custom Target Architecture selected: ${targetArch}")
        } else {
            context.echo('-- 🏗️ -- No custom Target Architecture selected. Using default architecture.')
        }

        return [newVersion, currentVersion, fileExtension, targetRepoInfo, targetArch]
    }

    def rustTestScan(def configData, def repoName, def newVersion) {
        def nextestArgs = ValuesUtils.getVariableArrayList(configData, 'nextestArgs', 'test')
        def customTestArgs = ValuesUtils.getVariable(configData, 'customTestArgs', 'test')
        def coverage = ValuesUtils.getVariable(configData, 'coverage', 'test')
        def coverageArgs = ValuesUtils.getVariable(configData, 'coverageArgs', 'test')

        try {
            def rustConf = context.libraryResource(resource: 'custom/rust-project/nextest.toml')
            context.writeFile(file: 'nextest.toml', text: rustConf)

            def tstlog = "${repoName}-${newVersion}_Tests.log"
            def covlog = "${repoName}-${newVersion}_Coverage.log"

            def testRC = context.sh(
                script: """ 
                    #!/bin/bash
                    cd ./${buildSources}
                    if [[ "${coverage}" == "true" ]]; then
                        cargo llvm-cov --output-path "${context.WORKSPACE}/${covlog}" ${coverageArgs} nextest ${nextestArgs} ${customTestArgs} --config-file ${context.WORKSPACE}/nextest.toml
                    else 
                        cargo nextest run ${nextestArgs} ${customTestArgs} --config-file ${context.WORKSPACE}/nextest.toml
                    fi 2>&1 | sed -e '/Download/d' -e '/Compiling/d' > "${context.WORKSPACE}/${tstlog}"
                    """,
                returnStatus: true,
                label: 'Executing Tests')

            def testError = (testRC != 0)

            if (testError && !context.fileExists(tstlog)) {
                throw new Exception('test execution failed with not log produced')
            }

            context.archiveArtifacts(artifacts: tstlog)
            context.echo('-- 📰 -- Test scan log saved')
            if (coverage == true) {
                context.archiveArtifacts(artifacts: covlog)
                context.echo('-- 📰 -- Test scan coverage log saved')
            }

            if (testError) {
                stageWarning('-- ⚠️ -- Test checks have failed: Please check log')
                return
            }

            context.echo('-- 📰 -- Test scan found no problems')
            context.withChecks('Tests') {
                context.junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: 'code/target/nextest/**/*.xml')
            }
        } catch (Exception e) {
            context.echo("${e.getMessage()}")
            context.error('--❌️-- Error detected during cargo test process - ')
        }
    }

    def rustClippyScan(def configData, def repoName, def newVersion) {
        def clippyArgs = ValuesUtils.getVariable(configData, 'clippyArgs', 'test')
        def cliplog = "${repoName}-${newVersion}_Clippy.log"

        try {
            def clippyResult = context.sh(
                script: """
                    #!/bin/bash
                    set +x -eo pipefail
                    cliplog="\$(realpath "${context.WORKSPACE}/${cliplog}")"
                    touch "\$cliplog"
                    cd ./${buildSources}
                    cargo clippy ${clippyArgs} 2>&1 | sed -e '/Checking/d' -e '/Compiling/d' -e '/Finished/d' > "\$cliplog"
                    cat "\$cliplog" >&2
                    [[ -s "\$cliplog" ]] && exit 1
                    echo "cargo clippy results:
                    cargo clippy found no warnings
                    " > "\$cliplog"
                """,
                returnStatus: true,
                label: 'Executing Clippy scan')    

            context.archiveArtifacts(artifacts: cliplog)

            if (clippyResult != 0) {
                stageWarning('-- ⚠️ -- Clippy Log contains warnings')
                return
            }
            context.echo('-- 📰 -- Clippy scan found no problems')
        } catch (Exception e) {
            context.echo('--❌️-- Error detected during cargo clippy process')
        }
    }

    def rustAuditScan(def configData, def repoName, def newVersion) {
        def auditParams = ValuesUtils.getVariable(configData, 'auditparams', 'audit')
        def auditlog = "${repoName}-${newVersion}_Audit.log"

        try {
            def auditResult = context.sh(
                script: """ 
                    set +x -o pipefail
                    auditlog="\$(realpath "${context.WORKSPACE}/${auditlog}")"
                    touch "\$auditlog"
                    cd ./${buildSources}
                    cargo audit -c never ${auditParams} > "\$auditlog" 2>&1
                    RC=\$?
                    [[ \$RC -ne 0 ]] && exit \$RC
                    echo "cargo audit results:
                    cargo audit found no vulnerabilities
                    " > "\$auditlog"
                """,
                returnStatus: true,
                label: 'Executing audit scan')

            context.archiveArtifacts(artifacts: auditlog)
            if (auditResult != 0) {
                stageWarning('-- ⚠️ -- Audit Log contains errors')
                return
            }
            context.echo('-- 📰 -- Cargo Audit found no vulnerabilities')
        } catch (Exception e) {
            context.echo('--❌️-- Error detected during audit process')
        }
    }

    def buildRustProject(def configData, def repoName, def targetArch) {
        def buildCommand = ValuesUtils.getVariable(configData, 'buildCommand', 'build')
        def buildArgs = ValuesUtils.getVariable(configData, 'buildArgs', 'build')
        def customBuildCommand = ValuesUtils.getVariable(configData, 'customBuildCommand', 'build')
        def customBuildArgs = ValuesUtils.getVariable(configData, 'customBuildArgs', 'build')
        def lambdaPipeline = ValuesUtils.getVariable(configData, 'lambdaPipeline', 'build')

        try {
            context.echo("-- 🏭 -- Building ${repoName}")

            def buildScript
            def buildOutput

            if (lambdaPipeline == true) {
                buildScript = 'cargo lambda build --release 2>&1 | tee'
            } else if (targetArch != null && targetArch != '' && lambdaPipeline != true) {
                buildScript = "cross ${customBuildCommand} ${customBuildArgs} ${targetArch} 2>&1 | tee"
            } else {
                buildScript = "cargo ${buildCommand} ${buildArgs} 2>&1 | tee"
            }

            if (targetArch != null && targetArch != '') {
                context.echo("-- 🏭 -- Building for target targetArch : ${targetArch}")
                buildOutput = context.sh(
                    script: """
                        #!/bin/bash
                        set +x
                        cd ./${buildSources}
                        rustup target add ${targetArch}
                        ${buildScript}
                    """,
                    returnStdout: true,
                    encoding: 'UTF-8',
                    label: 'Executing build').trim()
            } else {
                buildOutput = context.sh(
                    script: """
                        #!/bin/bash
                        set +x
                        cd ./${buildSources}
                        rustup toolchain list
                        ${buildScript}
                """,
                returnStdout: true,
                encoding: 'UTF-8',
                label: 'Executing build').trim()
            }        

            context.echo("-- ℹ️ -- Build execution: \n ${buildOutput}")

            //Temp fix due to UI not controllable issues
            def buildDeprecatedError = buildOutput.contains('future versions of rustup will require --force-non-host')
            if (buildDeprecatedError != false) {
                buildOutput = buildOutput.replaceAll('(?m)^(\\s)*(error: DEPRECATED:).*(\r\n|\r|\n)?', '')
            }
            //Temp fix due to UI not controllable issues

            def buildError = buildOutput.contains('error:')

            if (buildError != false) {
                context.error('-- ⚠️ -- The build process failed. Please check the logs')
            }
        } catch (Exception e) {
            context.error("--❌️-- Error detected during the build process ${e.getMessage()}")
        }
    }

    def buildMultipleRustProject(def configData, def multipleArchitectures) {
        def buildCommand = ValuesUtils.getVariable(configData, 'buildCommand', 'build')
        def buildArgs = ValuesUtils.getVariable(configData, 'buildArgs', 'build')

        try {
            context.echo('-- 🏗️ -- Multiple target architectures detected. Starting build process...')

            multipleArchitectures.each {architecture ->
                context.echo("-- 🏭 -- Building for target architecture : ${architecture}")

                def buildScript = "cross ${buildCommand} ${buildArgs} ${architecture} 2>&1 | tee"

                def buildOutput = context.sh(
                    script: """
                        #!/bin/bash
                        set +x
                        rustup target add ${architecture}
                        cd ./${buildSources}
                        ${buildScript}
                    """,
                    returnStdout: true,
                    encoding: 'UTF-8',
                    label: 'Executing build').trim()

                context.echo("-- ℹ️ -- Build results for architecture ${architecture}: \n ${buildOutput}")

                context.sh(
                    script: """
                    cd ${buildSources}/target/${architecture}/release
                    ls
                """, label:'-- ℹ️ -- Build Folder contents')

                //Temp fix due to UI not controllable issues
                def buildDeprecatedError = buildOutput.contains('future versions of rustup will require --force-non-host')
                if (buildDeprecatedError != false) {
                    buildOutput = buildOutput.replaceAll('(?m)^(\\s)*(error: DEPRECATED:).*(\r\n|\r|\n)?', '')
                }
                //END Temp fix due to UI not controllable issues

                def buildError = buildOutput.contains('error:')

                if (buildError != false) {
                    context.error("-- ⚠️ -- The build process for architecture ${architecture} failed. Please check the logs")
                }
            }
        } catch (Exception e) {
            context.error("--❌️-- Error detected during the build process ${e.getMessage()}")
        }
    }

//
// Compressing
//

    def compressArtifact(def configData, def newVersion, def fileExtension) {
        def zipScript  = ValuesUtils.getVariable(configData, 'zipScript',  'zip')
        def zipInclude = ValuesUtils.getVariable(configData, 'zipInclude', 'zip')
        if (zipInclude == null) zipInclude = ''
        def sourceFolder = ValuesUtils.getVariable(configData, 'zipSourceFolder', 'zip')
        def artifactName = ValuesUtils.getVariable(configData, 'targetZipName', 'zip')
        if (artifactName == null || artifactName == '') {
            context.error('--❌️-- Missing/invalid value for artifactName in conf.yml')
        }
        artifactName = artifactName + ".${newVersion}"
        //Delete if file already exist
        context.sh(script: "rm -rf ${artifactName}${fileExtension}", label: 'Delete old version of zip file')
        if (zipScript != '' && zipScript != null) {
            context.sh(script: "${zipScript} ${artifactName}${fileExtension} ${sourceFolder} ${zipInclude}", label: 'Zip file using custom script')
        } else {
            context.zip(glob: "${zipInclude}", zipFile: "${artifactName}${fileExtension}", dir: "${sourceFolder}")
        }
        return artifactName
    }

    def compressMultipleArtifacts(def configData, def newVersion, def multipleArchitectures) {
        def artifactNames = []
        def zipInclude = ValuesUtils.getVariable(configData, 'zipInclude', 'zip')
        if (zipInclude == null) zipInclude = ''
        def zipScript = ValuesUtils.getVariable(configData, 'zipScript', 'zip')
        def artifactName = ValuesUtils.getVariable(configData, 'targetZipName', 'zip') + ".${newVersion}"
        def zipSourceFolder = ValuesUtils.getVariable(configData, 'zipSourceFolder', 'zip')
        try {
            context.echo('-- 🏗️ -- Multiple target architectures detected. Starting zipping process...')
            if (zipScript != '' && zipScript != null) {
                context.sh(
                    script: "${zipScript} ${artifactName}.zip ${zipSourceFolder} ${zipInclude}", 
                    label: 'Compressing files using custom script',
                    returnStdout: true,
                    encoding: 'UTF-8'
                ).trim().readLines().each { line ->
                    line = line.trim()
                    if (line != '') artifactNames << line
                }
                if (artifactNames.size() == 0) {
                    artifactNames << artifactName
                }
            } else {
                if (artifactName == null || artifactName == '') {
                    throw new Exception('Missing/invalid value for targetZipName in conf.yml')
                }
                multipleArchitectures.each {architecture ->
                    context.echo("-- 🏭 -- Compressing file: ${artifactName}")
                    //Delete if file already exist
                    context.sh(script: "rm -rf ${artifactName}.zip", label: 'Delete old version of zip file')
                    context.zip(glob: "${zipInclude}", zipFile: "${artifactName}.zip", dir: "${buildSources}/target/${architecture}/release/${zipSourceFolder}")
                    artifactNames << artifactName
                }
            }
        } catch (Exception e) {
            context.error("--❌️-- Error detected during the zipping process. ${e.getMessage()}")
        }
        return artifactNames
    }

    def getArtifactNames(def configData, def multipleArchitectures) {
        def artifactNames = []
        try {
            multipleArchitectures.each {architecture ->
                def customArtifacts = ValuesUtils.getVariableArrayList(configData, 'customArtifacts', 'upload')
                def customName = customArtifacts.find { data -> data.architecture == architecture }.customArtifactName

                artifactNames.add(customName)
            }
        } catch (Exception e) {
            context.error("--❌️-- Error detected getting the desired artifact name. ${e.getMessage()}")
        }
        return artifactNames
    }
//
// Uploading
//
    def uploadSingleArtifact(def configData, def artifactName, def fileExtension = ".zip") {
        def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
        if (artifactPath == null || artifactPath == '') {
            throw new Exception('Missing/invalid value for artifactPath in conf.yml')
        }
        pushUtils.uploadArtifact(configData, artifactName)
        // Return the paths to the artifacts that were uploaded
        return [artifactPath + '/' +  artifactName + fileExtension]
    }

    def uploadMultipleArtifacts(def configData, def artifactNames, def fileExtension) {
        try {
            def artifactPaths = []
            context.echo('-- 🏗️ -- Multiple target architectures detected. Starting upload process...')
            def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
            def localPath = ValuesUtils.getVariable(configData, 'artifactLocalPath', 'upload')
            def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
            if (artifactPath == null || artifactPath == '') {
                throw new Exception('Missing/invalid value for artifactPath in conf.yml')
            }

            artifactNames.each {artifactName ->
                context.echo("-- 🏭 -- Uploading : ${artifactName}")
                artifactName = artifactName + fileExtension
                context.functiongroup_artifactory.uploadGenericArtifact(cred, targetRepository, artifactPath,
                        artifactName, localPath, artifactoryURL)
                artifactPaths << artifactPath + '/' + artifactName
            }
            // Return the paths to the artifacts that were uploaded
            return artifactPaths
        } catch (Exception e) {
            context.error('--❌️-- Error detected during the upload process')
        }
    }

    def uploadCustomArtifact(def configData, def artifactName, def fileExtension, def newVersion) {
        try {
            context.echo('-- 🏗️ -- Custom artifact upload detected. Starting upload process...')
            context.echo("-- 🏭 -- Uploading : ${artifactName}")

            def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')

            def customArtifacts = ValuesUtils.getVariableArrayList(configData, 'customArtifacts', 'upload')
            def localPath = customArtifacts.find { data -> data.customArtifactName == artifactName }.path

            def changeFileName = context.libraryResource(resource: 'custom/rust-project/scripts/fileName.sh')
            context.writeFile(file: 'fileName.sh', text: changeFileName)

            def combinedArtifactName = artifactName + ".${newVersion}" + fileExtension
            def pathWithFileName = localPath + artifactName + fileExtension
            def pathWithNewFileName = localPath + combinedArtifactName

            context.sh(script: """
                bash ./fileName.sh ${pathWithFileName} ${pathWithNewFileName}
                rm -rf ./fileName.sh
            """, label: 'Preparing file to upload')

            def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
            if (artifactPath == null || artifactPath == '') {
                throw new Exception('Missing/invalid value for artifactPath in conf.yml')
            }
            context.functiongroup_artifactory.uploadGenericArtifact(cred, targetRepository, artifactPath,
                combinedArtifactName, localPath, artifactoryURL)
            // Return the paths to the artifacts that were uploaded
            return [artifactPath + '/' +  combinedArtifactName]
        } catch (Exception e) {
            context.error('--❌️-- Error detected during the custom upload process')
        }
    }

    def uploadCustomArtifacts(def configData, def artifactNames, def fileExtension, def newVersion) {
        try {
            def artifactPaths = []
            context.echo('-- 🏗️ -- Custom artifact upload detected. Starting upload process...')
            def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
            def customArtifacts = ValuesUtils.getVariableArrayList(configData, 'customArtifacts', 'upload')
            def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
            if (artifactPath == null || artifactPath == '') {
                throw new Exception('Missing/invalid value for artifactPath in conf.yml')
            }
            def changeFileName = context.libraryResource(resource: 'custom/rust-project/scripts/fileName.sh')

            context.writeFile(file: 'fileName.sh', text: changeFileName)
            artifactNames.each {artifactName ->
                context.echo("-- 🏭 -- Uploading : ${artifactName}")

                def localPath = customArtifacts.find { data -> data.customArtifactName == artifactName }.path

                def combinedArtifactName = artifactName + ".${newVersion}" + fileExtension
                def pathWithFileName = localPath + artifactName + fileExtension
                def pathWithNewFileName = localPath + combinedArtifactName

                context.sh(script: """
                    bash ./fileName.sh ${pathWithFileName} ${pathWithNewFileName}
                    rm -rf ./fileName.sh
                """, label: 'Preparing file to upload')

                context.functiongroup_artifactory.uploadGenericArtifact(cred, targetRepository, artifactPath,
                    combinedArtifactName, localPath, artifactoryURL)
                artifactPaths << artifactPath + '/' + combinedArtifactName
            }
            // Return the paths to the artifacts that were uploaded
            return artifactPaths
        } catch (Exception e) {
            context.error('--❌️-- Error detected during the custom upload process')
        }
    }

    def publishCrates(def configData) {
        //
        // Basically this is "cargo publish" ... but wwe use cargo-workspace to make it easier
        //
        // Publish has a HUGE issue - in the verify stage it receives a redirect from Artifactory
        // which it cannot then authenticate - unfortunately LOCALLY it appears to work ... 
        //
        // At the moment any "already published" crates will be reported and the stage turned UNSTABLE
        //
        // cargo workspace has some very intriguing features for version management - we need
        // to look into these
        //
        // There are a number of strategies to to consider to beef up version management
        //
        // * Make the "unstable" an actual error
        // * Publish crates that don't already exist, but still warn/throw an error
        // * Remove any pre-existing version and republish - NOPE
        // * Dynamically modify the version based on the build number
        //   See "pre-release" notes here https://doc.rust-lang.org/cargo/reference/manifest.html#the-version-field
        // * Only publish when a certain branch (or pattern) is targeted - I PREFER THIS ONE
        //
        // Which we choose is open to discussion
        //
        // The script optimizes the output to present PUBLISHED and UNPUBLISHED messages that are
        // then used to communicate with the rest of the pipeline and the user
        //
        // Any custom script is expected to use the same protocol onto stdout
        //
        try {
            def stdout
            def publishScript = ValuesUtils.getVariable(configData, 'publishScript', 'publish')
            if (publishScript != '' && publishScript != null) {
                stdout = context.sh(
                    script: "${publishScript} ${targetRepositoryInfo.cargoId}",
                    label: "Publishing to ${targetRepositoryCargoId} with custom script",
                    returnStdout: true,
                    encoding: 'UTF-8'
                ).trim()
            } else {
                stdout = context.sh(
                    script:"""
                        #!/bin/bash
                        set +x
                        cd ./${buildSources}
                        cargo ws publish --registry ${targetRepositoryCargoId} --publish-as-is 2>&1 | sed -e '/error.*http\\.cainfo/d' \\
                            -e 's/^info published *\\([^ ]*\\) *v\\([^ ]*\\)/PUBLISHED:crates\\/\\1\\/\\1-\\2.crate/' \\
                            -e 's/^info already published *\\(.*\\)/ALREADY_PUBLISHED:\\1 already published/' 
                    """, 
                    label: "Publishing to ${targetRepositoryCargoId}",
                    returnStdout: true,
                    encoding: 'UTF-8'
                ).trim()
            }
            context.echo(stdout)
            def stdoutLines = stdout.readLines()
            stdoutLines.findAll {line -> line.startsWith('ALREADY_PUBLISHED:') }
                .each {line -> stageWarning("-- ⚠️ -- Publication: ${line - 'ALREADY_PUBLISHED:'}") }
            if (stdout.contains('error:')) {
                throw new Exception('--❌️-- Publication ended with errors')
            }
            // Return the paths to the artifacts that were published
            return stdoutLines.findAll { line -> line.startsWith('PUBLISHED:') }.collect { line -> (line - 'PUBLISHED:').trim() }
        } catch (Exception e) {
            context.error("--❌️-- Error detected during the crate publication process: ${e}")
        }
    }

//
// Updating properties
//
    def updateArtifactProperties(def configData, def artifactPaths, def type = 'generic') {
        def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'update')
        def props = ValuesUtils.getVariableArrayList(configData, 'artifactProperties', 'update')
        //Props is an ArrayList of LinkedHashmap, each with one entry! Ergh
        //replace the type with that passed to us
        props = props.findAll {prop -> prop.prop != 'type' }
        props <<  [prop: 'type', value: type]

        ArtifactoryApi artfApi = new ArtifactoryApi(artifactoryURL, cred)

        try {
            artifactPaths.each {artifactPath ->
                def lastSlashIndex = artifactPath.lastIndexOf('/')
                def artifactName = artifactPath.substring(lastSlashIndex + 1)
                artifactPath = artifactPath.substring(0, lastSlashIndex)
                context.echo("-- 🏭 -- Update artifact properties for: ${artifactPath}/${artifactName}")
                artfApi.updateArtifactProperties(targetRepository, artifactPath, artifactName, props)
            }
        } catch (Exception e) {
            context.error("--❌️-- Error detected during the update artifact properties process ${e}")
        }
    }

    void setRustDockerAgent(String dockerPath, String dockerTargetName) {
        def dockerFile

        dockerPath = ValuesUtils.removeStartEndChars(dockerPath, '/', true, true)
        def projectType = ValuesUtils.removeStartEndChars(dockerTargetName, '.Dockerfile', false, true)
        def localDockerPath = "${context.WORKSPACE}/" + dockerPath
        def exists = context.fileExists localDockerPath
        if (exists) {
            /* Docker file in workspace root */
            dockerFile = context.readFile(localDockerPath)
        } else {
            def config = context.readYaml file: 'conf.yml'
            def dockerImage = dockerTargetName
            if (config.dockerImage != null && config.dockerImage != '') {
                dockerImage = "${dockerTargetName}-${config.dockerImage}"
            }
            try {
                dockerFile = context.libraryResource "custom/${projectType}-project/${dockerImage}"
            } catch (Exception e) {
                context.error("--❌️-- Error detected during the set up of the docker image. Error: ${e.getMessage()}")
            }
        }

        context.echo("--🐳-- Using ${dockerFile} Docker image - writing to ${dockerTargetName}")                      
        context.writeFile file: dockerTargetName, text: dockerFile
    }
}
