#!/usr/bin/env groovy

import org.pdxc.rest.GitApi
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.assure.util.WarningUtils
import org.assure.pushpipeline.PushPipelineUtils
import org.assure.pushpipeline.CheckovPipelineUtils

/**
 * Pipeline creation and execution.
 *
 * @param stagesMap Specific data for each stage.
 * @param dockerPath Full path and name of a dockerFile to be used on the pipeline. If not provided, default is used.
 * @return void
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'checkov.Dockerfile') {

    String pipelineName = 'checkov'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the dockerFile
    String dockerName
    // Current repository name
    def repoName
    // List of repositories to scan
    def repositories
    // Scan satuses
    def failedScans = []
    def successScans = []
    // Timestamp
    String currentTime
    // Number of max parallel executions for the repositories scan stage
    def executionLimits = 10

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 1, unit: 'HOURS')
        }

        stages {
            stage('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        checkovUtils = new CheckovPipelineUtils()
                        warningUtils = new WarningUtils()
                        dockerName = "${pipelineName}.Dockerfile"
                        checkovUtils.setCheckovDockerAgent(dockerPath, dockerName)
                    }
                }
            }
            stage('Pipeline info') {
                steps {
                    script {
                        configData = checkovUtils.pipelineInfoSteps(pipelineName)
                        def now = new Date()
                        currentTime = now.format("yyyy-MM-dd.HH:mm", TimeZone.getTimeZone('UTC'))
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo("Configured repository name matches current repository: ${repoName}")
                    }
                }
            }
            stage('Repositories Scan') {
                agent {
                    dockerfile {
                        args '-u root:root'
                        filename "${pipelineName}.Dockerfile"
                        reuseNode true
                        registryCredentialsId 'assure-docker-lambda'
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('📥 Get repositories'){
                        steps{
                            script{
                                catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                    def failedRepositories
                                    def listName = ValuesUtils.getVariable(configData, 'listFileName', 'repositories')
                                    def repos = checkovUtils.getRepositories(listName)
                                    (repositories, failedRepositories) = checkovUtils.cleanRepositoriesList(repos)
                                    if (!failedRepositories.isEmpty()) {
                                        error("-- ❌️ -- Missing repository name/s. Please Check your list items.")
                                    }
                                    echo('Loaded repositories: \n\n' + repositories.join('\n\n'))
                                }
                            }
                        }
                    }
                    stage('📲 Scan repositories'){
                        steps {
                            script {
                                def parallelSteps = [:]
                                def parallelCount = 0
                                repositories.each { repo ->
                                    parallelSteps[repo.repoName] = {
                                        stage(repo.repoName) {
                                            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                try{
                                                    script{
                                                        checkovUtils.cloneRepository(configData, repo)
                                                        def (checkovData, uuidCheckov) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.checkov_conf_file, "checkov/checkovConfiguration.yml.gpg")
                                                        
                                                        checkovUtils.scanRepository(configData, repo, dockerName, uuidCheckov, currentTime)
                                                        def checkovStatusCode = checkovUtils.checkovErrorCheck(repo.repoName, uuidCheckov)
                                                        if(checkovStatusCode == 1){
                                                            failedScans.add(repo.repoName)
                                                            error("Checkov quality gate not passed")
                                                        } else {
                                                            successScans.add(repo.repoName)
                                                        }
                                                    }
                                                } catch (Exception e) {
                                                    if (e.getMessage() != "Checkov quality gate not passed"){
                                                        error("-- ❌️ -- Something went wrong while scanning: ${repo.repoName}")
                                                    } else {
                                                        error("-- ⚠️ -- Checkov quality gate not passed for: ${repo.repoName} Check the log to fix the issues.")
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    parallelCount++
                                    if (parallelCount >= executionLimits) {
                                        parallel parallelSteps
                                        parallelSteps = [:]
                                        parallelCount = 0
                                    }
                                }
                                if (!parallelSteps.isEmpty()) {
                                    parallel(parallelSteps)
                                }
                            }
                        }
                    }
                    stage('📜 Generate Log Files'){
                        steps {
                            script {
                                if (successScans.size() > 0 || failedScans.size() > 0) {
                                    checkovUtils.compressLogs(currentTime)
                                } else {
                                    error("-- ❌️ -- No scans executed. No log files were generated. Please check the log.")
                                }
                            }
                        }
                    }
                    stage('📧 Send notifications'){
                        steps {
                            script {
                                List<String> emailRecipients = ValuesUtils.getVariableArrayList(configData, 'emailList', 'repositories')
                                checkovUtils.sendEmailNotifications(emailRecipients, successScans, failedScans, currentTime)
                            }
                        }
                    }
                }
            }
        }
        post {
            always {
                script {
                    postActions(configData)
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}