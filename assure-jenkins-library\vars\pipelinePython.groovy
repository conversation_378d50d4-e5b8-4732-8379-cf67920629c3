#!/usr/bin/env groovy

import org.pdxc.rest.GitApi
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.util.DefaultConfiguration
import org.pdxc.util.FileUtils
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.pdxc.util.Utils
import org.assure.util.AssureUtils
import org.assure.util.WarningUtils
import org.assure.envservice.EnvServiceApi
import org.assure.pullpipeline.PullPipelineUtils
import org.assure.pushpipeline.PushPipelineUtils
import groovy.json.JsonOutput


boolean validDeployCustom(String branch, Map configData) {

    def isValid = (ValuesUtils.getVariable(configData, 'deployment', 'custom')) && 
            (branch ==~ /(^feature\/{1}+.*)/ ||
            branch ==~ /(^fix\/{1}+.*)/ ||
            branch ==~ /(^prepare\/{1}+.*)/ ||
            branch == 'development' ||
            branch == 'master'
            ) && (!("${branch}" == 'master') || ValuesUtils.getVariable(configData, 'deployment_from_master', 'custom'))

    return isValid
}

/**
 * Pipeline creation and execution.
 *
 * @param stagesMap Specific data for each stage.
 * @param dockerPath Full path and name of a dockerFile to be used on the pipeline. If not provided, default is used.
 * @return void
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'python.Dockerfile') {
    String pipelineName = 'python'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the artifact generated
    String artifactName
    // Calculated new version
    String newVersion
    // Name of the dockerFile
    String dockerName
    // Current repository name
    def repoName
    // QA vars
    def checkovStatusCode = 0
    def sonarStatusCode = 0
    
    PushPipelineUtils   pushUtils
    
    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 1, unit: 'HOURS')
        }

        stages {
            stage ('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        warningUtils = new WarningUtils()
                        dockerName = "${pipelineName}.Dockerfile"
                    }
                }
            }
            stage('Pipeline info') {
                steps {
                    script {
                        def conf = libraryResource "custom/${pipelineName}-project/${pipelineName}-conf.yml"
                        writeFile file: "${pipelineName}-conf.yml", text: conf
                        def defaultConfigYml = readYaml file: "${pipelineName}-conf.yml"
                        def repoData = readYaml file: 'conf.yml'

                        configData = defaultConfigYml + repoData
                        configData = pushUtils.writeConfigData(configData, 'generic')

                        println 'Loaded configuration values: \n\n' + JsonOutput.prettyPrint(JsonOutput.toJson(configData))

                        writeYaml file: 'conf.yml', data: configData, overwrite: true

                        pushUtils.setDockerAgentPython(dockerPath, dockerName)
                        (buildDockerRegistryUrl, buildDockerRegistryCreds) = pushUtils.getDockerRegistryUrlAndCreds(configData, dockerName, pipelineName)                  

                        pushUtils.executePostStageFunction(stagesMap, 'info')
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo "Configured repository name matches current repository: ${repoName}"

                        pushUtils.executePostStageFunction(stagesMap, 'validate')
                    }
                }
            }
            stage ('Artifact & Deploy') {
                agent {
                    dockerfile {
                        args '-u root:root'
                        filename 'python.Dockerfile'
                        reuseNode true
                        registryCredentialsId buildDockerRegistryCreds
                        registryUrl buildDockerRegistryUrl
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('Set up') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'setup') } }
                        steps {
                            script {
                                // Configure Git global data
                                def cred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'setup')
                                def mail = ValuesUtils.getVariable(configData, 'gitEmail', 'setup')
                                def user = ValuesUtils.getVariable(configData, 'gitUsername', 'setup')
                                def url = ValuesUtils.getVariable(configData, 'gitHubUrl', 'setup')
                                functiongroup_git.setup(cred, mail, user, url)
                                
                                // Calculate and set new version to be built
                                echo 'Calculate and set new version:'
                                def currentVersion = ValuesUtils.getVariable(configData, 'version', 'setup')
                                newVersion = currentVersion + "+${env.BUILD_NUMBER}"
                                echo "Current Version: ${currentVersion} --- New Version: ${newVersion}"

                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Install') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'install') } }
                        steps {
                            script {
                                def path = ValuesUtils.getVariable(configData, 'dependenciesFilePath', 'install')
                                def scriptName = ValuesUtils.getVariable(configData, 'scriptName', 'install')
                                def scriptParams = ValuesUtils.getVariable(configData, 'scriptParams', 'install')
                                
                                pushUtils.executeScript(path, scriptName, scriptParams)

                                pushUtils.executePostStageFunction(stagesMap, 'install')
                            }
                        }
                    }
                    stage('Test') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'test') } }
                        steps {
                            script {
                                def commands = ValuesUtils.getVariableArrayList(configData, 'testCommands', 'test')

                                def dependenciesPath = ValuesUtils.getVariable(configData, 'dependenciesFilePath', 'test')
                                dependenciesPath = (dependenciesPath == null || dependenciesPath == '') ? '.' : dependenciesPath
                                def dependenciesPathModified = ValuesUtils.removeStartEndChars(dependenciesPath, '/', true, true)

                                commands.each { command -> 
                                    sh script: """  
                                                    cd ${dependenciesPathModified}
                                                    ${command}""", 
                                        label: "Execute command: ${command}"
                                }                                
                            }
                        }
                        post {
                            always {
                                script {
                                    // report??                                    
                                    echo "reports??"
                                }
                            }
                            success {
                                script {
                                    pushUtils.executePostStageFunction(stagesMap, 'test')
                                }
                            }
                        }
                    }                    
                    stage('Zipping artifact') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'zip') } }
                        steps {
                            script {
                                def zipScript = ValuesUtils.getVariable(configData, 'zipScript', 'zip')
                                def zipInclude = ValuesUtils.getVariable(configData, 'zipInclude', 'zip')
                                if (zipInclude == null) zipInclude = ''
                                def sourceFolder = ValuesUtils.getVariable(configData, 'zipSourceFolder', 'zip')
                                artifactName = ValuesUtils.getVariable(configData, 'targetZipName', 'zip') + ".${newVersion}"
                                //Delete if file already exist
                                sh script: "rm -rf ${artifactName}.zip", label: 'Delete old version of zip file'
                                if (zipScript != "" && zipScript != null) {
                                    sh script: "${zipScript} ${artifactName}.zip ${sourceFolder} ${zipInclude}", label: 'Zip file using custom script'
                                } else {
                                    zip glob: "${zipInclude}", zipFile: "${artifactName}.zip", dir: "${sourceFolder}"
                                }

                                pushUtils.executePostStageFunction(stagesMap, 'zip')
                            }
                        }
                    }
                    stage('Code quality'){
                        when { expression { pushUtils.notSkipStage(stagesMap, 'codequality') } }
                        parallel {                             
                            stage('Checkov Scan') {
                                    when { expression { pushUtils.notSkipStage(stagesMap, 'checkov') } }
                                    steps {
                                        script {
                                            def checkovQualityGate = ValuesUtils.getVariable(configData, 'checkovQualityGate', 'checkov')
                                            def (checkovData, uuidCheckov) = pushUtils.decryptYmlFile(configData.passphrase_id, configData.checkov_conf_file, "checkov/checkovConfiguration.yml.gpg")
                                            def scriptPath = "checkov/checkov.sh"

                                            pushUtils.checkovScan('generic', repoName, repoName, scriptPath, uuidCheckov, dockerName)

                                            def checkName = 'Vulnerabilities'
                                            withChecks("${checkName}") {
                                                junit(allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: "**/results*.xml")
                                            }
                                            pushUtils.markGitHubCheckNeutral(configData, checkName)

                                            checkovStatusCode = pushUtils.checkovErrorCheck(repoName, uuidCheckov)
                                            
                                            if(checkovStatusCode != 0 && checkovQualityGate){
                                                currentBuild.result = 'ABORTED'
                                                error ("-- ❌ -- Pipeline ABORTED ❗❗ Checkov Scan 🛸 status: FAIL (checkovQualityGate is ${checkovQualityGate})")
                                                return
                                            }
                                            
                                            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                                if(checkovStatusCode != 0){
                                                    error("-- ⚠️ -- Checkov Scan 🛸 status FAIL ❗❗ Please check 👀 Log file 📋 for details")
                                                    return
                                                }
                                            }
                                            pushUtils.executePostStageFunction(stagesMap, 'checkov')
                                        }
                                    }
                                }
                            stage('SonarQube Scan') {
                                when { expression { pushUtils.notSkipStage(stagesMap, 'sonar') } }
                                    steps {
                                        script {
                                            withCredentials([string(credentialsId:'ASSURE-SONAR-HOST', variable:'SONARHOST')]) {
                                                withCredentials([string(credentialsId:'ASSURE-SONAR-TOKEN', variable:'SONARTOKEN')]) {
                                                    sonarStatusCode = pushUtils.sonarScan(SONARHOST, SONARTOKEN, configData, false, false, dockerName)
                                                }
                                            }
                                        }
                                    }
                            }
                        }
                        post {
                            success {
                                script {
                                    pushUtils.executePostStageFunction(stagesMap, 'codequality')
                                }
                            }
                        }
                    }
                    stage('Upload artifact') {
                        when { expression { return ((pushUtils.notSkipStage(stagesMap, 'upload')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                        steps {
                            script {
                                def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'upload')
                                def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'upload')
                                if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                                def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'upload')
                                def localPath = ValuesUtils.getVariable(configData, 'artifactLocalPath', 'upload')
                                def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'upload')
                                functiongroup_artifactory.uploadGenericArtifact(cred, repo, artifactPath,
                                        artifactName + '.zip', localPath, artifactoryURL)

                                pushUtils.executePostStageFunction(stagesMap, 'upload')
                            }
                        }
                    }
                }
            }
            stage('Update artifact properties') {
                when { expression { return ((pushUtils.notSkipStage(stagesMap, 'update')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                steps {
                    script {
                            def artifactoryURL = ValuesUtils.getVariable(configData, 'artifactoryURL', 'update')
                            if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
                            def cred = ValuesUtils.getVariable(configData, 'artifactoryCredentials', 'update')
                            ArtifactoryApi artfApi = new ArtifactoryApi(artifactoryURL, cred)

                            def repo = ValuesUtils.getVariable(configData, 'artifactRepository', 'update')
                            def props = ValuesUtils.getVariableArrayList(configData, 'artifactProperties', 'update')
                            def notSkipQa     = pushUtils.notSkipStage(stagesMap, 'codequality')
                            def notSkipCheckov  = pushUtils.notSkipStage(stagesMap, 'checkov')
                            def notSkipSonar  = pushUtils.notSkipStage(stagesMap, 'sonar')

                            if(!notSkipQa || !notSkipCheckov  || !notSkipSonar) {
                                configData.artifactProperties.add([prop: 'qa', value: 'skip'])  
                            }
                            else if (checkovStatusCode  != 0 || sonarStatusCode != 0 ){
                                    configData.artifactProperties.add([prop: 'qa', value: 'fail'])
                            }
                            else
                                configData.artifactProperties.add([prop: 'qa', value: 'pass'])
                            
                            def artifactPath = ValuesUtils.getVariable(configData, 'artifactPath', 'update')
                            artfApi.updateArtifactProperties(repo, artifactPath, artifactName + '.zip', props)

                            pushUtils.executePostStageFunction(stagesMap, 'update')
                    }
                }
            }
            stage('Generate Release Notes') { 
                // This stage generates release notes in github using the github api
                when {
                    expression { return (pushUtils.notSkipStage(stagesMap, 'releaseNotes') && (BRANCH_NAME == 'master') && (configData.releaseNotes == true)) }
                }
                steps {
                    script {
                            pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)

                            pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                    }
                }
            }
            stage('Branch protection') {
                /* This stage updates the branch protection rule for BRANCH releases */
                when{
                    expression { return ((pushUtils.notSkipStage(stagesMap, 'protection')) && (BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/)) }
                }
                steps {
                    script {
                        def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                        pushUtils.branchProtect(repoName, gitHubCred)
                    }
                }
            }
        }
        post {
            always {
                script {
                    postActions(configData)
                    if (currentBuild.description == 'Code quality process failed') currentBuild.result = 'UNSTABLE'
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}