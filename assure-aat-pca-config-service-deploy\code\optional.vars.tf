variable "bundle_version_assure_aat_pca_config_service_lambda" {
  default = "latest"
}

variable "bundle_version_assure_aat_pca_config_event_service_lambda" {
  default = "latest"
}

variable "bundle_version_assure_aat_pca_preprocessing_handler_lambda" {
  default = "latest"
}

variable "config_service_lambda_memory_size" {
  description = "Lambda memory requirement in MB"
  type        = string
  default     = "3072"
}

variable "config_service_ephemereal_storage_size" {
  description = "Ephemereal storage used by the lambda, default is 512Mb, max is 10240"
  default     = "2048"
}

variable "config_service_lambda_timeout" {
  description = "Lambda timeout in seconds"
  type        = string
  default     = "900"
}

variable "lambda_memory_size" {
  description = "Lambda memory requirement in MB"
  type        = string
  default     = "1024"
}

variable "ephemereal_storage_size" {
  description = "Ephemereal storage used by the lambda, default is 512Mb, max is 10240"
  default     = "1024"
}

variable "lambda_timeout" {
  description = "Lambda timeout in seconds"
  type        = string
  default     = "900"
}

// Define params for environment variables
variable "s3_folder_input_docs_path" {
  type = string
  description = "s3_folder_input_docs_path"
  default = "pca/input-data/"
}

variable "s3_folder_output_docs_path" {
  type = string
  description = "s3_folder_output_docs_path"
  default = "pca/output-data/"
}

variable "s3_folder_reports_path" {
  type = string
  description = "s3_folder_reports_path"
  default = "pca/reports/"
  
}

variable "s3_folder_template_docs_path" {
  type = string
  description = "s3_folder_template_docs_path"
  default = "pca/template-data/"
}