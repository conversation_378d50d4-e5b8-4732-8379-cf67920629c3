import json
import os
from datetime import datetime

import boto3
import ruamel.yaml
from aws_lambda_powertools import Logger
from botocore.exceptions import ClientError

# Initialize structured logging for AWS Lambda
logger = Logger(service="Code-Analyzer")


class DynamoDBTableManager:
    def __init__(self, config_file="config.yaml"):
        """
        Initialize the table manager by loading configuration and setting up DynamoDB client and table.
        If 'delete_table' is enabled in the config, deletes the table before (re)creating it.
        """
        # Load configuration from YAML
        with open(config_file, "r") as file:
            self.config = ruamel.yaml.safe_load(file)

        # Extract DynamoDB and key configuration
        cfg = self.config['dynamodb_config']
        self.table_name = cfg['table_name']
        self.delete_table = cfg['delete_table']
        self.partition_key = cfg['partition_key']
        self.fields = self.config['fields']
        self.composite_key_fields = self.config['composite_key_logic']['fields']
        self.separator = self.config['composite_key_logic'].get('separator', "#")

        # Use proper AWS credentials based on the environment (Lambda or local)
        session = boto3 if "AWS_LAMBDA_FUNCTION_NAME" in os.environ \
            else boto3.Session(profile_name=cfg['profile_name'])

        # Initialize DynamoDB client and table resource
        self.dynamodb = session.resource('dynamodb')
        self.client = session.client('dynamodb')
        self.table = self.dynamodb.Table(self.table_name)

        # Optionally delete existing table (for fresh deployments/testing)
        if self.delete_table:
            self.delete_table_if_exists()

        # Create a table if not already present
        self.create_table_if_not_exists()

    def generate_composite_key(self, **config_data):
        """
        Generate a unique composite key string by joining configured fields with a separator.
        """
        try:
            return self.separator.join(config_data[field] for field in self.composite_key_fields)
        except KeyError as e:
            logger.error(f"Missing composite key field: {e}")
            return None

    @staticmethod
    def _handle_dynamodb_response(operation, **kwargs):
        """
        Generic wrapper to safely perform a DynamoDB operation and catch errors.
        Returns a consistent response structure with statusCode and body.
        """
        try:
            return operation(**kwargs)
        except ClientError as e:
            logger.error(f"DynamoDB operation failed: {e}")
            return {'statusCode': 500, 'body': json.dumps({'error': 'DynamoDB error', 'details': str(e)})}

    def create_table_if_not_exists(self):
        """
        Create the DynamoDB table if it doesn't already exist.
        Configures key schema, attribute definitions, and throughput.
        """
        if self.table_name in self.client.list_tables()['TableNames']:
            logger.info(f"Table '{self.table_name}' already exists.")
            return

        try:
            # Create table with partition key and throughput settings
            self.client.create_table(TableName=self.table_name,
                                     KeySchema=[{'AttributeName': self.partition_key, 'KeyType': 'HASH'}],
                                     AttributeDefinitions=[{'AttributeName': self.partition_key, 'AttributeType': 'S'}],
                                     ProvisionedThroughput={
                                         'ReadCapacityUnits': self.config['dynamodb_config']['read_capacity_units'],
                                         'WriteCapacityUnits': self.config['dynamodb_config']['write_capacity_units']})
            # Wait for the table to become active
            self.table.wait_until_exists()
            self.table = self.dynamodb.Table(self.table_name)
            logger.info(f"Created table '{self.table_name}' successfully.")
        except ClientError as e:
            logger.error(f"Failed to create table: {e}")

    def delete_table_if_exists(self):
        """
        Delete the DynamoDB table if it currently exists.
        Used for clean setup or reset.
        """
        try:
            self.table.delete()
            self.table.wait_until_not_exists()
            logger.info(f"Deleted table '{self.table_name}' successfully.")
        except ClientError as e:
            logger.error(f"Failed to delete table: {e}")

    def config_exists(self, **config_data):
        """
        Check if a configuration entry already exists in the table
        by using the generated composite key.
        """
        composite_key = self.generate_composite_key(**config_data)
        if not composite_key:
            return False
        try:
            response = self.table.get_item(Key={'uniqueid': composite_key})
            return 'Item' in response
        except ClientError as e:
            logger.error(f"Error checking existence: {e}")
            return False

    def delete_config(self, **config_data):
        """
        Delete a configuration entry using its composite key.
        Returns the appropriate HTTP-style response dict.
        """
        composite_key = self.generate_composite_key(**config_data)
        if not composite_key:
            return {'statusCode': 400, 'body': json.dumps({'error': 'Invalid composite key data'})}

        if not self.config_exists(**config_data):
            return {'statusCode': 404, 'body': json.dumps({'error': 'Configuration not found'})}

        response = self._handle_dynamodb_response(self.table.delete_item, Key={'uniqueid': composite_key})
        return {'statusCode': 204} if response.get('statusCode') != 500 else response

    def create_config(self, **config_data):
        """
        Create (or overwrite) a configuration entry with the current timestamp.
        Ensures idempotency with 'if_not_exists' for created_date.
        """
        composite_key = self.generate_composite_key(**config_data)
        if not composite_key:
            return {'statusCode': 400, 'body': json.dumps({'error': 'Invalid composite key data'})}

        now = datetime.utcnow().isoformat()

        # Prepare values for fields and timestamps
        expression_values = {f":{f}": config_data[f] for f in self.fields}
        expression_values.update({':composite_key': composite_key,
                                  ':last_updated_date': now,
                                  ':created_date': now})

        # Construct update expression for upsert logic
        update_expr = [f"{f} = :{f}" for f in self.fields]
        update_expression = (f"SET composite_key = :composite_key, "
                             f"last_updated_date = :last_updated_date, "
                             f"created_date = if_not_exists(created_date, :created_date), "
                             f"{', '.join(update_expr)}")

        # Perform upsert operation
        response = self._handle_dynamodb_response(self.table.update_item,
                                                  Key={'uniqueid': composite_key},
                                                  UpdateExpression=update_expression,
                                                  ExpressionAttributeValues=expression_values,
                                                  ReturnValues="ALL_NEW")
        if response.get('statusCode') == 500:
            return response

        return {'statusCode': 200,
                'body': json.dumps({'message': 'Config upserted successfully',
                                    'data': response.get('Attributes', {})})}

    def update_config(self, **config_data):
        """
        Update an existing configuration entry with new values and updated timestamp.
        """
        composite_key = self.generate_composite_key(**config_data)
        if not composite_key:
            return {'statusCode': 400, 'body': json.dumps({'error': 'Invalid composite key data'})}

        if not self.config_exists(**config_data):
            return {'statusCode': 404, 'body': json.dumps({'error': 'Configuration not found'})}

        now = datetime.utcnow().isoformat()

        # Build update expression and values
        expression_values = {f":{f}": config_data[f] for f in self.fields}
        expression_values[':last_updated_date'] = now

        update_expr = [f"{f} = :{f}" for f in self.fields]
        update_expression = (f"SET last_updated_date = :last_updated_date, {', '.join(update_expr)}")

        # Execute the update
        response = self._handle_dynamodb_response(self.table.update_item,
                                                  Key={'uniqueid': composite_key},
                                                  UpdateExpression=update_expression,
                                                  ExpressionAttributeValues=expression_values,
                                                  ReturnValues="UPDATED_NEW")
        if response.get('statusCode') == 500:
            return response

        return {'statusCode': 200,
                'body': json.dumps({'message': 'Config updated successfully',
                                    'data': response.get('Attributes', {})})}

    def get_config(self, **config_data):
        """
        Fetch a configuration entry from DynamoDB using the composite key.
        """
        composite_key = self.generate_composite_key(**config_data)
        if not composite_key:
            return {'statusCode': 400, 'body': json.dumps({'error': 'Invalid composite key data'})}

        response = self._handle_dynamodb_response(self.table.get_item, Key={'uniqueid': composite_key})
        if response.get('statusCode') == 500:
            return response

        item = response.get('Item')
        if not item:
            return {'statusCode': 404, 'body': json.dumps({'error': 'Configuration not found'})}

        return {'statusCode': 200, 'body': json.dumps(item)}
