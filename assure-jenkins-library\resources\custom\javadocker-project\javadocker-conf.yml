##### Notification (post)
sendMail: "false"
emailFrom: "<EMAIL>"
emailTo: ""
attachmentFileEmail: ""
#teamsSecretHookId: "TEAMS_PUSH_WEBHOOK"

####POM.xml
dependenciesPackageFile: "pom.xml"
dependenciesPackageAttribute: "version"
dependenciesPackagePath: ""

##### Artifactory
artifactoryUrl: "docker.dxc.com"
artifactoryCredentials: "diaas-rw"
artifactRepository: "diaas-docker"
artifactProperties:
  - prop: "status"
    value: "ready"
  - prop: "type"
    value: "docker"

##### Checkov
checkov_conf_file: "checkovConfiguration.yml"
passphrase_id: "PASSPHRASE_ID"
checkovQualityGate: false

##### SonarQube
sonarSources: "."
sonarBinaries: "target"
sonarExclusions: "test/**/*,tests/**/*,test/*,handlers/test/**/*,pipelinesTempFiles/*"
sonarQualityGate: false
sonarCoverage: "sonar.coverage.jacoco.xmlReportPaths"
sonarCoveragePath: "./target/site/jacoco/jacoco.xml"

###### Environment service configuration
# env_service_conf_file: "envServData.yml"

###### Environment Status Maping
# environment_status_mapping: [
# ["ready", "sdlc-environment"]
# ]

###### Docker inspect
artifactRepositoryJson: "assure-generic"

###### Release Notes Auto-Generation
releaseNotes: false

###### Java version
javaVersion: 11

#### Maven Version
mavenVersion: 8
