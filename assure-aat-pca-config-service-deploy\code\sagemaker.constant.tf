variable "sagemaker_model_name" {
  description = "Put the Name of the sagemaker model to identify"
  type        = string
  default     = "sagemaker-model"
}

variable "sagemaker_endpoint_configuration_name" {
  description = "Put the Name of the sagemaker endpoint configuration to identify"
  type        = string
  default     = "sagemaker-endpoint-config"
}

variable "sagemaker_endpoint_configuration_production_variant_name" {
  description = "Put the Name of the sagemaker endpoint configuration production variant"
  type        = string
  default     = "sagemaker-endpoint-config"
}

variable "sagemaker_endpoint_configuration_production_variant_initial_instance_count" {
  description = "Put the count of the sagemaker endpoint configuration production variant instamce"
  type        = number
  default     = 1
}

variable "sagemaker_endpoint_configuration_production_variant_instance_type" {
  description = "Put the type of the sagemaker endpoint configuration production variant instance"
  type        = string
  default     = "ml.m5.2xlarge"
  validation {
    condition     = can(regex("^ml\\.", var.sagemaker_endpoint_configuration_production_variant_instance_type))
    error_message = "Instance type must be a valid SageMaker instance type starting with 'ml.'"
  }

}

variable "sagemaker_endpoint_name" {
  description = "Put the Name of the sagemaker endpoint to identify"
  type        = string
  default     = "pca-model-endpoint-v13"
}

variable "sagemaker_prebuilt_ecr_image_repository_name" {
  description = "Put the Name of the sagemaker prebuilt ecr image respository to identify"
  type        = string
  default     = "sagemaker-scikit-learn"
}

variable "sagemaker_prebuilt_ecr_image_tag" {
  description = "Put the tag of the sagemaker prebuilt ecr image to identify"
  type        = string
  default     = "1.0-1-cpu-py3"
}
