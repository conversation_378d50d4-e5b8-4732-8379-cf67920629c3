{"extends": "recommended", "rules": {"CatchException": {"enabled": false}, "CompileStatic": {"enabled": false}, "DuplicateMapLiteral": {"enabled": false}, "DuplicateNumberLiteral": {"enabled": false}, "DuplicateStringLiteral": {"enabled": false}, "FactoryMethodName": {"enabled": false}, "FieldTypeRequired": {"enabled": false}, "IfStatementBraces": {"enabled": false}, "ImplementationAsType": {"enabled": false}, "InvertedCondition": {"enabled": false}, "InvertedIfElse": {"enabled": false}, "JavaIoPackageAccess": {"enabled": false}, "LineLength": {"enabled": false}, "MethodParameterTypeRequired": {"enabled": false}, "MethodReturnTypeRequired": {"enabled": false}, "MethodSize": {"enabled": false}, "NestedBlockDepth": {"enabled": false}, "NoDef": {"enabled": false}, "ParameterReassignment": {"enabled": false}, "ThrowException": {"enabled": false}, "UnnecessaryGetter": {"enabled": false}, "UnnecessaryParenthesesForMethodCallWithClosure": {"enabled": false}, "UnnecessarySetter": {"enabled": false}, "VariableTypeRequired": {"enabled": false}}}