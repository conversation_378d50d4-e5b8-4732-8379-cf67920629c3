#!/usr/bin/env groovy

import org.pdxc.rest.GitApi
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.pdxc.util.Utils
import org.assure.util.WarningUtils
import org.assure.pushpipeline.PushPipelineUtils
import org.assure.pushpipeline.RustPipelineUtils

/**
 * Pipeline creation and execution.
 *
 * @param stagesMap Specific data for each stage.
 * @param dockerPath Full path and name of a dockerFile to be used on the pipeline. If not provided, default is used.
 * @return void
 */
def call(LinkedHashMap stagesMap, String dockerPath = 'rust.Dockerfile') {
    String pipelineName = 'rust'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the artifact generated
    String artifactName
    def artifactPaths = []
    // Calculated new version
    String newVersion
    // Name of the dockerFile
    String dockerName
    // Current repository name
    def repoName
    // File Extension for custom upload
    def fileExtension
    // Target architecture
    def targetArch
    // Target repo info
    def targetRepositoryInfo

    def buildDockerRegistryUrl
    def buildDockerRegistryCreds
    def prBranch = false

    PushPipelineUtils   pushUtils
    RustPipelineUtils   rustUtils

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
            timeout(time: 1, unit: 'HOURS')
        }

        stages {
            stage('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        configData = pushUtils.pipelineInfoSteps(pipelineName)
                        rustUtils = new RustPipelineUtils()
                        warningUtils = new WarningUtils()
                        prBranch = "${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/
                    }
                }
            }
            stage('Pipeline info') {
                steps {
                    script {
                        dockerName = "${pipelineName}.Dockerfile"
                        rustUtils.setRustDockerAgent(dockerPath, dockerName)
                        (buildDockerRegistryUrl, buildDockerRegistryCreds) = pushUtils.getDockerRegistryUrlAndCreds(configData, dockerName, pipelineName)
                        pushUtils.executePostStageFunction(stagesMap, 'info')
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo("Configured repository name matches current repository: ${repoName}")
                        pushUtils.executePostStageFunction(stagesMap, 'validate')
                    }
                }
            }
            stage('Artifact & Deploy') {
                agent {
                    dockerfile {
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        filename "${pipelineName}.Dockerfile"
                        reuseNode true
                        registryCredentialsId buildDockerRegistryCreds
                        registryUrl buildDockerRegistryUrl
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages {
                    stage('Set up') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'setup') } }
                        steps {
                            script {
                                (newVersion, currentVersion, fileExtension, targetRepositoryInfo, targetArch) = rustUtils.rustSetupSteps(configData)
                                echo("target repository info: ${targetRepositoryInfo}")
                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Test') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'test') } }
                        steps {
                            script {
                                def clippyExecution = ValuesUtils.getVariable(configData, 'clippyExecution', 'test')

                                rustUtils.rustTestScan(configData, repoName, newVersion)

                                if (clippyExecution != true) {
                                    echo('-- ⚠️ -- Clippy scan was disabled')
                                } else {
                                    rustUtils.rustClippyScan(configData, repoName, newVersion)
                                }
                                pushUtils.executePostStageFunction(stagesMap, 'test')
                            }
                        }
                    }
                    stage('Audit') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'audit') } }
                        steps {
                            script {
                                rustUtils.rustAuditScan(configData, repoName, newVersion)
                                pushUtils.executePostStageFunction(stagesMap, 'audit')
                            }
                        }
                    }
                    stage('Build') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'build') } }
                        steps {
                            script {
                                rustUtils.buildRustProject(configData, repoName, targetArch)
                                pushUtils.executePostStageFunction(stagesMap, 'build')
                            }
                        }
                    }
                    stage('Deploy') {
                        when { 
                            expression { pushUtils.notSkipStage(stagesMap, 'deploy') }
                            anyOf {
                                equals expected: 'generic', actual: targetRepositoryInfo.type
                                equals expected: 'cargo',   actual: targetRepositoryInfo.type
                                equals expected: false, actual: prBranch
                            }
                        }
                        steps {
                            script {
                                if (targetRepositoryInfo.type == 'generic') {
                                    //
                                    // The repo is a generic repo - zip and upload
                                    //
                                    if (pushUtils.notSkipStage(stagesMap, 'zip')) {
                                        stage('Zipping artifact') {
                                            artifactName = rustUtils.compressArtifact(configData, newVersion, fileExtension)
                                            pushUtils.executePostStageFunction(stagesMap, 'zip')
                                        }
                                    } else {
                                        echo('-- ⚠️ -- Zipping artifact skipped')
                                    }
                                    if (pushUtils.notSkipStage(stagesMap, 'upload')) {
                                        stage('Upload artifact') {
                                            if (fileExtension != '.zip') {
                                                echo('-- 🏭 -- Uploading custom artifact/s.')
                                                artifactPaths = rustUtils.uploadCustomArtifact(configData, artifactName, fileExtension, newVersion)
                                            } else {
                                                artifactPaths = rustUtils.uploadSingleArtifact(configData, artifactName)
                                            }

                                            pushUtils.executePostStageFunction(stagesMap, 'upload')
                                        }
                                    } else {
                                        echo('-- ⚠️ -- Upload artifact skipped')
                                    }
                                } else if (targetRepositoryInfo.type == 'cargo') {
                                    //
                                    // The repo is a cargo registry - publish the crates
                                    //
                                    if (pushUtils.notSkipStage(stagesMap, 'upload')
                                    && targetRepositoryInfo.cargoId != null
                                    && targetRepositoryInfo.cargoId != '') {
                                        stage('Publish crates') {
                                            artifactPaths = rustUtils.publishCrates(configData)
                                            pushUtils.executePostStageFunction(stagesMap, 'upload')
                                        }
                                    } else {
                                        echo("-- ⚠️ -- Cargo publish skipped: ${targetRepositoryInfo}")
                                    }
                                } else {
                                    rustUtils.stageWarning("-- ⚠️ -- Unsupported Artifactory repository details:  ${targetRepositoryInfo}")
                                }
                            }
                        }
                    }
                    stage('Update artifact properties') {
                        when {
                            expression { pushUtils.notSkipStage(stagesMap, 'update') }
                            expression { artifactPaths.size() > 0 }
                        }
                        steps {
                            script {
                                if (targetRepositoryInfo.type == 'cargo') {
                                    rustUtils.updateArtifactProperties(configData, artifactPaths, 'crate')
                                } else {
                                    rustUtils.updateArtifactProperties(configData, artifactPaths)
                                }
                                pushUtils.executePostStageFunction(stagesMap, 'update')
                            }
                        }
                    }
                }
            }
            stage('Generate Release Notes') { 
                // This stage generates release notes in github using the github api
                when {
                    expression { pushUtils.notSkipStage(stagesMap, 'releaseNotes') }
                    equals expected: 'master', actual: BRANCH_NAME 
                    equals expected: true, actual: configData.releaseNotes
                }
                steps {
                    script {
                        pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)
                        pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                    }
                }
            }
            stage('Branch protection') {
                when {
                    expression { pushUtils.notSkipStage(stagesMap, 'protection') }
                    expression { BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/ }
                }
                steps {
                    script {
                        def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                        pushUtils.branchProtect(repoName, gitHubCred)
                    }
                }
            }
        }
        post {
            always {
                script {
                    postActions(configData)
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}
