############################################################################################################
####################################### MODIFICATION AREA ###################################################
######################## Modify these values according to your project needs.  ##############################
### Rest of customizable values are listed in README.md section 2.1.3 Common custom configuration values  ###
############################## Only modify them is special behaviour is needed ##############################
#############################################################################################################

#### Name of current repository for validation. It has to match the name of the repository where this file is.
repositoryName: "name-of-current-repository-validation"

#### Artifactory
artifactPath: "path_to_upload_artifact"     #i.e: "assure-platform/underwriting/underwriting-lambda"

##### Zip (zip)
targetZipName: "name-of-the-targetZipName"
zipInclude: ""                               #i.e: "handlers/**, node_modules/**, services/**, utils/**, *.js, package.json"

##### Execute npm install (install)
scriptName: "install"
scriptParams: ""                            #i.e: "--only=prod"

#############################################################################################################
################################### END OF MODIFICATION AREA ################################################
#############################################################################################################

##### GitHub data #####
gitHubCredential: "pdxc-jenkins" # for assure, assure-external or assure-delivery org the value is: "assure-github" 
gitEmail: "<EMAIL>"
gitUsername: "Jenkins User"
