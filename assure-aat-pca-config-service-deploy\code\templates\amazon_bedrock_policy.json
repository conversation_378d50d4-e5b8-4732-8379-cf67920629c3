{"Version": "2012-10-17", "Statement": [{"Sid": "VisualEditor0", "Effect": "Allow", "Action": ["lambda:ListFunctions", "lambda:ListEventSourceMappings", "lambda:ListLayerVersions", "lambda:ListLayers", "lambda:GetAccountSettings", "lambda:CreateEventSourceMapping", "lambda:ListCodeSigningConfigs", "lambda:CreateCodeSigningConfig", "bedrock:*"], "Resource": "*"}, {"Sid": "VisualEditor1", "Effect": "Allow", "Action": "lambda:*", "Resource": "arn:aws:lambda:${aws_region}:${aws_account_number}:function:*"}]}