#!/usr/bin/env groovy

/**
 * Docker pipeline template: definition of a generic pipeline for docker artifacts.
 */

import org.pdxc.rest.GitApi
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.util.DefaultConfiguration
import org.pdxc.jenkins.JenkinsContext
import org.pdxc.util.ValuesUtils
import org.pdxc.util.Utils
import org.assure.util.AssureUtils
import org.assure.util.WarningUtils
import org.assure.envservice.EnvServiceApi
import org.assure.pushpipeline.PushPipelineUtils
import org.assure.pullpipeline.PullPipelineUtils
import static groovy.json.JsonOutput.prettyPrint
import static groovy.json.JsonOutput.toJson

boolean validDeployCustom(String branch, Map configData) {

    def isValid = (ValuesUtils.getVariable(configData, 'deployment', 'custom') == true) && 
            (branch ==~ /(^feature\/{1}+.*)/ ||
            branch ==~ /(^fix\/{1}+.*)/ ||
            branch ==~ /(^prepare\/{1}+.*)/ ||
            branch == 'development'
            ) || (("${branch}" == 'master') && (ValuesUtils.getVariable(configData, 'deployment_from_master', 'custom') == true))

    return isValid
}

def prepareStage(def name, def config, def version, def utils, def stagesMap) {

    def dockerFile = "customFiles/${name}/Dockerfile"
    def cred = ValuesUtils.getVariable(config, 'artifactoryCredentials', 'upload')
    def repo = ValuesUtils.getVariable(config, 'artifactRepository', 'upload')
    def path = ValuesUtils.getVariable(config, 'artifactPath', 'upload').toLowerCase()
    def tag = "${version}"
    def targetPath = ValuesUtils.getVariable(config, 'buildTarget', 'upload')
    targetPath = (targetPath != null) ? targetPath : '.'
    def url = ValuesUtils.getVariable(config, 'artifactoryUrl', 'upload')
    def props = ValuesUtils.getVariableArrayList(config, 'artifactProperties', 'update')
    def artifactPath = ValuesUtils.getVariable(config, 'artifactPath', 'update').toLowerCase()
    def artifactoryURL = ValuesUtils.getVariable(config, 'artifactoryURL', 'update')
    if (artifactoryURL == null) artifactoryURL = DefaultConfiguration.PDXC_ARTIFACTORY_URL
    ArtifactoryApi artfApi = new ArtifactoryApi(artifactoryURL, cred)
    def skipCheckov = (utils.notSkipStage(stagesMap, 'checkov') == true) ? config.put('skip', false) : config.put('skip', true)
    def skipPrisma = (utils.notSkipStage(stagesMap, 'prisma') == true) ? config.put('skip', false) : config.put('skip', true)

    return {
        try{
            stage (name) {
                stage("Building ${name}") {
                    script{
                        def (dockerJsonUrl, checkovStatusCode) = utils.uploadMultiDockerArtifact(cred, repo + '/' + path, name, tag, dockerFile, targetPath, url, config)
                        props.add([prop: 'docker_inspect', value: "${dockerJsonUrl}"])
                        utils.executePostStageFunction(stagesMap, 'upload')
                    }
                }
                stage("Updating ${name}") {
                    script{
                        artfApi.updateArtifactProperties(repo, artifactPath, name+"/"+"${version}", props)
                        //Props to latest
                        props.add([prop: 'image_version_tag', value: "${version}"]) // added property image_version_tag
                        props = props.findAll {p -> p.prop != 'status' } // remove 'status' property from props                  
                        artfApi.updateArtifactProperties(repo, artifactPath, name+"/latest", props) // update latest
                        utils.executePostStageFunction(stagesMap, 'update')
                    }
                }
                stage("Custom deployment ${name}") {
                    script{
                        if (validDeployCustom("${BRANCH_NAME}", config) == true) {
                            
                            def artefact = [:]
                            def properties = []

                            try {
                                artefact.put('repo', config.artifactRepository)
                                artefact.put('path', artifactPath + '/' + name + '/' + tag)
                                artefact.put('name', 'manifest.json')

                                config.artifactProperties.each { item -> properties.add([key: item.prop, value: item.value])}            
                                artefact.put('properties', properties)

                                def value = AssureUtils.getFromRegEx(artefact.path as String, config.regex_docker_version)
                                value = ValuesUtils.removeStartEndChars(artefact.path.substring(0, (artefact.path.size() - value.size())), '/', true, true)

                                def uniquename = AssureUtils.getFromRegEx(value as String, config.regex_docker_identifier)
                                value = ValuesUtils.removeStartEndChars(value.substring(0, (value.size() - uniquename.size())), '/', true, true)

                                def branch = AssureUtils.getFromRegEx(value as String, config.regex_docker_branch)

                                def artifactName = value.substring(0, (value.size() - branch.size())) + uniquename
                    
                                artifactName = '/' + artefact.repo + '/' + artifactName
                                artifactName = artifactName.replace('/feature', '')

                                artefact.put('uniquename', artifactName)

                            } catch (Exception e) {
                                Utils.throwExceptionInstance('Exception', "-- ⚠ -- Artifact could not be composed. Error: ${e.getMessage()}")
                            }

                            envService = new EnvServiceApi(config.environment_service_url, config.environment_service_credential, config.oauth_host, config.env_api_key)
                            def deploymentPackagesResponse = envService.getWithCustomAuth(config.environment_service_url + '/deployment-packages?artefact=' + artefact.uniquename)
                            envService.setToken()

                            def targetEnvs
                            def artifacts = []                          

                            targetEnvs = config.findAll { k, v -> k.endsWith("_environment_resource_name") }
                            def tempTargets = targetEnvs.findAll{ it.key != "sdlc-environment_environment_resource_name" }
                            targetEnvs = tempTargets

                            // Check if environment is locked
                            def environments = envService.getWithCustomAuth(config.environment_service_url + "/environments")._links.item

                            environments.findAll { item ->                                                    
                                targetEnvs.each { k,v ->                                                            
                                        if (item.summary.resource_name == v && item.summary.is_locked == true) {
                                            targetEnvs.remove(k)
                                            echo "-- ⚠️🚮 -- Your environment ${v} is locked"                                                                
                                        }
                                }
                            }

                            def parallelSteps = [:]                            
                            targetEnvs.keySet().each { targetEnv ->                                
                                def artifactUniqueName = AssureUtils.getFromRegEx(artefact.name, config.regex_artifact_uniquename)
                                def environment = ValuesUtils.removeStartEndChars(targetEnv, '_environment_resource_name', false, true)
                                def urlPath = envService.composeURLPath(config, environment, true)

                                deploymentPackagesResponse._links.item.each { deploymentPackage ->
                                    def mergeMap = [:]
                                    mergeMap.put('uniquename', artifactUniqueName)
                                    mergeMap.putAll(deploymentPackage.summary)
                                    mergeMap.putAll(artefact)
                                    def (result, invalidArtifacts) = PullPipelineUtils.composeAndValidateArtefact(mergeMap, config)
                                    echo "❌ Discarded ❌ : ${invalidArtifacts}"
                                    if (result != []) artifacts.add(result)
                                }

                                echo "🏆 Artefacts 🏆 : ${artifacts}"

                                def deployResultList = []

                                stage(environment) {                        
                                    try {
                                        artifacts.each { artifact ->
                                            try {
                                                stage(artifact.service_name) {                                                                
                                                    Map deployment 
                                                    try {
                                                        deployment = envService.deploy(environment, artifact.service_name, [artifact], config)
                                                        deployResultList.add(deployment?.success)
                                                    } catch (Exception e) {
                                                        echo "--❌️-- Error detected during deployment of ${artifact.name} for deployment package ${artifact.service_name}"
                                                        deployResultList.add(false)
                                                        Utils.throwExceptionInstance("Failure during deployment ${artifact.name}")
                                                    }
                                                    
                                                    if (!deployment?.success) {
                                                        echo "--❌️-- Error detected during deployment of ${artifact.name} for deployment package ${artifact.service_name}"
                                                        Utils.throwExceptionInstance("Failure during deployment of ${artifact.name}")
                                                    } else {
                                                        echo "--🥳-- ${artifact.name} successfully deployed"
                                                    }                                                    
                                                }
                                            } catch (Exception e) {
                                                echo "-- ❌ -- Failure during Deploy of ${artifact.name} to environment ${targetEnv}"
                                                deployResultList.add(false)
                                            }
                                        }
                                    } catch (Exception e) {
                                        error("-- ❌ -- Error during deployment to ${targetEnv}. Please check logs. Error: ${e}")                                                
                                    } 
                                }                               
                            }                           
                        }
                        else {
                            echo 'Custom deploy is disabled'
                        }
                    }
                }
            }
        } catch (Exception e) {
            currentBuild.result = 'UNSTABLE'
            error("-- ❌ -- Error during process. Please check logs. Error: ${e}")
            Utils.throwExceptionInstance('Exception', "-- ⚠ -- Error during process. Error: ${e.getMessage()}")
        }
    }
}


def call(LinkedHashMap stagesMap) {
    String pipelineName = 'multidocker'
    // Configuration values loaded from the conf.yml file.
    Map configData
    // Name of the artifact generated
    String artifactName
    // Calculated new version
    String newVersion
    // Current repository name
    def repoName
    // Input json containing components details
    def inputJson
    // JSONArray containing WAR details
    def components
        
    PushPipelineUtils pushUtils

    pipeline {
        agent { label 'ubuntu-22.04' }

        options {
            skipDefaultCheckout(false)
            skipStagesAfterUnstable()
        }
        
        stages {
            stage ('Pipeline setup') {
                agent {
                    docker {
                        image 'diaas-docker/pipelines/nodegit:latest'
                        args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                        reuseNode true
                        registryUrl 'https://artifactory.dxc.com/diaas-docker'
                        registryCredentialsId 'diaas-rw'
                    }
                }
                steps {
                    script {
                        JenkinsContext.setContext(this)
                        pushUtils = new PushPipelineUtils()
                        warningUtils = new WarningUtils()                 
                    }
                }
            }
            stage('Pipeline Info') {
                steps {
                    script {
                        def conf = libraryResource "custom/docker-project/docker-conf.yml"
                        writeFile file: "docker-conf.yml", text: conf
                        def defaultConfigYml = readYaml file: 'docker-conf.yml'
                        def repoData = readYaml file: 'conf.yml'
                        configData = defaultConfigYml + repoData
                        configData = pushUtils.writeConfigData(configData, 'docker')

                        writeYaml file: 'conf.yml', data: configData, overwrite: true

                        inputJson = readJSON file: 'components.json'

                        println 'Loaded configuration values: \n\n' + prettyPrint(toJson(configData))
                        println 'Loaded components values: \n\n' + prettyPrint(toJson(inputJson))

                        pushUtils.executePostStageFunction(stagesMap, 'info')
                    }
                }
            }
            stage('Validate pipeline') {
                steps {
                    script {
                        repoName = new GitApi().getCurrentRepositoryName()
                        if (repoName != configData.repositoryName) {
                            error 'This pipeline stops here! Please check your yml file: repositoryName is incorrect'
                        }
                        echo "Configured repository name matches current repository: ${repoName}"

                        pushUtils.executePostStageFunction(stagesMap, 'validate')
                    }
                }
            }
            stage("Process WAR files & Docker image creation"){
                agent {             
                    docker {
                                image 'diaas-docker/pipelines/dockerpipeline:1.0.0'
                                args '-u root:root -v "/var/run/docker.sock:/var/run/docker.sock:rw"'
                                reuseNode true
                                registryUrl 'https://artifactory.dxc.com/diaas-docker'
                                registryCredentialsId 'diaas-rw'
                    }
                }
                options {
                    skipDefaultCheckout(true)
                }
                stages{
                    stage('Set up') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'setup') } }
                        steps {
                            script {                        
                                // Calculate and set new version to be built
                                echo 'Calculate and set new version:'
                                def currentVersion = ValuesUtils.getVariable(configData, 'artifactoryTag', 'setup')
                                newVersion = currentVersion + "-${env.BUILD_NUMBER}"
                                echo "Current Version: ${currentVersion} --- New Version: ${newVersion}"
                                configData.put('artifactoryTag', "${newVersion}")
                                writeYaml file: 'conf.yml', data: configData, overwrite: true
                                pushUtils.executePostStageFunction(stagesMap, 'setup')
                            }
                        }
                    }
                    stage('Get WAR files') {
                        when { expression { pushUtils.notSkipStage(stagesMap, 'warfiles') } }
                        steps {
                            script {
                                components = inputJson.get("components")
                                for(int i=0; i< components.size(); i++){
                                    // download zip from PDXC
                                    withCredentials([usernamePassword(credentialsId: 'diaas-rw', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                                        def statusCode = sh script:"""
                                        curl -u "${env.USERNAME}:${env.PASSWORD}" \
                                        -O "https://artifactory.csc.com/artifactory/${components[i].get("warArtifactoryPath")}"
                                    """, label: "Executing script"
                                    }
                                }
                            }
                        }
                    }
                    stage('Build Docker images') {                        
                        when { expression { return ((pushUtils.notSkipStage(stagesMap, 'build')) && !("${BRANCH_NAME}" ==~ /(^PR{1}-\d.*)/)) } }
                        steps {
                            catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
                                script {
                                    def envServFile = libraryResource(resource: "${configData.env_service_conf_file}.gpg", encoding: "Base64")
                                    writeFile(file: "${configData.env_service_conf_file}.gpg", text: envServFile, encoding: "Base64")

                                    def envServData = AssureUtils.decryptYmlFile(configData.passphrase_id, configData.env_service_conf_file, '.')

                                    configData.put('version', "${newVersion}")
                                    configData = envServData + configData

                                    def checkovScript = libraryResource(resource: "checkov/multidockerCheckovInstall.sh", encoding: "Base64")
                                    writeFile(file: "multidockerCheckovInstall.sh", text: checkovScript, encoding: "Base64")

                                    sh(
                                        script: """ 
                                                set +x
                                                bash +x ./multidockerCheckovInstall.sh
                                                rm -rf ./multidockerCheckovInstall.sh
                                                set -x
                                            """,
                                                returnStdout: true,
                                                label: 'Executing checkov installation').trim()

                                    def parallelSteps = [:]
                                    inputJson.components.each { component ->
                                        parallelSteps[component.name] = prepareStage(component.name, configData, newVersion, pushUtils, stagesMap)
                                    }
                                    parallel parallelSteps

                                    pushUtils.executePostStageFunction(stagesMap, 'build')
                                }
                            }
                        }
                    }
                    stage('Generate Release Notes') { 
                        // This stage generates release notes in github using the github api
                        when {
                            expression { return (pushUtils.notSkipStage(stagesMap, 'releaseNotes') && (BRANCH_NAME == 'master') && (configData.releaseNotes == true)) }
                        }
                        steps {
                            script {
                                    pushUtils.createReleaseNotes(configData, newVersion, BRANCH_NAME)

                                    pushUtils.executePostStageFunction(stagesMap, 'releaseNotes')
                            }
                        }
                    }
                    stage('Branch protection') {
                        /* This stage updates the branch protection rule for BRANCH releases */
                        when{
                            expression { return ((pushUtils.notSkipStage(stagesMap, 'protection')) && (BRANCH_NAME ==~ /(^release\/{1}+\d+\d+\.\d+.*)/)) }
                        }
                        steps {
                            script {
                                def gitHubCred = ValuesUtils.getVariable(configData, 'gitHubCredential', 'protect')
                                pushUtils.branchProtect(repoName, gitHubCred)
                            }
                        }
                    }                
                }                  
            }
        }
        post {
            always {
                script {                    
                    postActions(configData)
                    if (pipelineName != null && pipelineName != '') warningUtils.createWarning(pipelineName, configData)
                }
            }
        }
    }
}
