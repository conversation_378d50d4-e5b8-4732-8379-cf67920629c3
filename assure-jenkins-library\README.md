# Assure Platform Jenkins Projects Library

- [1. How to use this library](#1-how-to-use-this-library)
  - [1.1. <PERSON><PERSON> Global Shared Library](#11-load-jenkins-global-shared-library)
  - [1.2. <PERSON>ad Assure Jenkins Projects Library Example](#12-load-assure-jenkins-projects-library-example)
- [2. Push Pipeline Definition](#2-push-pipeline-definition)
  - [2.1. Generic Push Pipelines](#21-generic-push-pipelines)
    - [2.1.1. Functions extensions through Assure Library](#211-functions-extensions-through-assure-library)
    - [2.1.2. Dynamic configuration](#212-dynamic-configuration)
    - [2.1.3. Common custom configuration values](#213-common-custom-configuration-values)
    - [2.1.4. Different configuration values per stage](#214-different-configuration-values-per-stage)
    - [2.1.5. Skip complete stage](#215-skip-complete-stage)
    - [2.1.6. Execute another function as last step of a stage](#216-execute-another-function-as-last-step-of-a-stage)
    - [2.1.7. Add additional stages](#217-add-additional-stages)
    - [2.1.8. Custom docker for Agent](#218-custom-docker-for-agent)
    - [2.1.9. Lambda -JS- Push Pipeline](#219-lambda-js-push-pipeline)
      - [*******. Implementation](#2191-implementation)
      - [*******. Stages and execution](#2192-stages-and-execution)
      - [*******. Customizable values -conf.yml-](#2193-customizable-values-confyml)
      - [*******. Consumption Example](#2194-consumption-example)
    - [2.1.10. Terraform Deploy Projects Push Pipeline and Silver Image](#2110-terraform-deploy-projects-push-pipeline-and-silver-image)
      - [********. Implementation](#21101-implementation)
      - [********. Stages and execution](#21102-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21103-customizable-values-confyml)
      - [********. Consumption Example](#21104-consumption-example)
    - [2.1.11. SPA UI Push Pipeline](#2111-spa-ui-push-pipeline)
      - [********. Implementation](#21111-implementation)
      - [********. Stages and execution](#21112-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21113-customizable-values-confyml)
      - [********. Consumption Example](#21114-consumption-example)
    - [2.1.12. DotNet Push Pipeline](#2112-dotnet-push-pipeline)
      - [********. Implementation](#21121-implementation)
      - [********. Stages and execution](#21122-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21123-customizable-values-confyml)
      - [********. Consumption Example](#21124-consumption-example)
    - [2.1.13. Docker Push Pipeline](#2113-docker-push-pipeline)
      - [********. Implementation](#21131-implementation)
      - [********. Stages and execution](#21132-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21133-customizable-values-confyml)
      - [********. Optional values -conf.yml-](#21134-optional-values-confyml)
      - [********. Consumption Example](#21135-consumption-example)
    - [2.1.14. Data Push Pipeline](#2114-data-push-pipeline)
      - [********. Implementation](#21141-implementation)
      - [********. Stages and execution](#21142-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21143-customizable-values-confyml)
      - [********. Consumption Example](#21144-consumption-example)
    - [2.1.15. Custom Push Pipeline](#2115-custom-push-pipeline)
      - [********. Implementation](#21151-implementation)
      - [********. Stages and execution](#21152-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21153-customizable-values-confyml)
      - [********. Consumption Example](#21154-consumption-example)
    - [2.1.16. MultiDocker Push Pipeline](#2116-multidocker-push-pipeline)
      - [********. Implementation](#21161-implementation)
        - [********.1 Required Directory Structure & Files](#211611-required-directory-structure--files)
      - [********. Stages and execution](#21162-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21163-customizable-values-confyml)
      - [********. Optional values -conf.yml-](#21164-optional-values-confyml)
      - [********. Consumption Example](#21165-consumption-example)
    - [2.1.17. Java Docker Push Pipeline](#2117-java-docker-push-pipeline)
      - [********. Implementation](#21171-implementation)
      - [********. Stages and execution](#21172-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21173-customizable-values-confyml)
      - [********. Optional values -conf.yml-](#21174-optional-values-confyml)
      - [********. Consumption Example](#21175-consumption-example)
    - [2.1.18. Java Zip Push Pipeline](#2118-java-zip-push-pipeline)
      - [********. Implementation](#21181-implementation)
      - [********. Stages and execution](#21182-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21183-customizable-values-confyml)
      - [********. Optional values -conf.yml-](#21184-optional-values-confyml)
      - [********. Consumption Example](#21185-consumption-example)
    - [2.1.19. NPM Push Pipeline](#2119-npm-push-pipeline)
      - [********. Implementation](#21191-implementation)
      - [********. Stages and execution](#21192-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21193-customizable-values-confyml)
      - [********. Consumption Example](#21194-consumption-example)
    - [2.1.20. Lambda Python Push Pipeline](#2120-lambda-python-push-pipeline)
      - [********. Implementation](#21201-implementation)
      - [********. Stages and execution](#21202-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21203-customizable-values-confyml)
      - [********. Consumption Example](#21204-consumption-example)
    - [2.1.21. Rust Push Pipeline](#2121-rust-push-pipeline)
      - [********. Implementation](#21211-implementation)
      - [********. Stages and execution](#21212-stages-and-execution)
      - [********. Referencing Artifactory cargo package registries](#21214-referencing-artifactory-cargo-package-registries)
      - [********. Customizable values -conf.yml-](#21214-customizable-values-confyml)
      - [********. Consumption Example](#21214-consumption-example)
    - [2.1.22. Multi Rust Push Pipeline](#2122-multi-rust-push-pipeline)
      - [********. Implementation](#21221-implementation)
      - [********. Stages and execution](#21222-stages-and-execution)
      - [********. Customizable values -conf.yml-](#21223-customizable-values-confyml)
      - [********. Consumption Example](#21224-consumption-example)
    - [2.2. Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines)
    - [2.3. Deployment from Push pipelines](#23-deployment-from-push-pipelines)
    - [2.4. Quality Assurance: Checkov & SonarQube flow diagram](#24-quality-assurance-checkov--sonarqube-flow-diagram)
- [3. Utility Pipeline Definition](#3-utility-pipeline-definition)
  - [3.1. Checkov Utility Pipeline](#31-checkov-utility-pipeline)
    - [3.1.1. Implementation](#311-implementation)
    - [3.1.2. Stages and execution](#312-stages-and-execution)
    - [3.1.3. Customizable values -conf.yml-](#313-customizable-values-confyml)
    - [3.1.4. Repositories list](#314-repositories-list)
    - [3.1.5. Consumption Example](#315-consumption-example)
  - [3.2. SonarQube Utility Pipeline](#32-sonar-utility-pipeline)
    - [3.2.1. Implementation](#321-implementation)
    - [3.2.2. Stages and execution](#322-stages-and-execution)
    - [3.2.3. Customizable values -conf.yml-](#323-customizable-values-confyml)
    - [3.2.4. Repositories list](#324-repositories-list)
    - [3.2.5. Consumption Example](#325-consumption-example)
- [4. References](#4-references)

This Assure Platform Library serves two different purposes:

1. **Push Pipeline Definition** for projects that can take advantage of using a predefined generic pipeline sharing
   most common stages and steps and just having to define specific custom configuration values and, in some cases,
   extending them by adding some extra functions.

2. **Assure Private Library** to store and define functions and resources that can be shared across pipelines but due
   to the very Assure customization cannot be added to the Jenkins Global Shared Library.

> Note: From DXC Assure Platform we have contributed to the creation of the [PDXC Jenkins Shared Library project
> ](https://github.dxc.com/Platform-DXC/devops-jenkins-sharedlibs) by creating and sharing several functions and
> pipelines that can be used by the whole DXC community.
>
> More info on [Jenkins Shared Libs](https://jenkins.io/doc/book/pipeline/shared-libraries/)

## 1. How to use this library

In order to load this library from your pipeline and be able to consume any of the components (Pipelines, Functions
, Resources...) you will need to use the Jenkins "load" function.

Then you can make use of some of the functions that we have already created
and made available in the Jenkins Global Shared Library.

### 1.1. Load Jenkins Global Shared Library

In order to be able to use any function defined on the Shared Library, you just need to load the Library:

```groovy
@Library('pdxc-pipeline-lib') _
```

However, it is recommended that the loaded library is a specific version to prevent breaking changes in the Shared
Library causes our pipeline to break. This is done by referencing the specific tag or branch:

```groovy
@Library('pdxc-pipeline-lib@assure') _
```

With this command you can already start using everything that is defined on the _/vars_ folder of the Shared Library.
On the other hand, if you want to use any Class defined in the _/src_ folder, you will need to import it specifically:

```groovy
import org.pdxc.jenkins.api.RestApi
```

### 1.2. Load Assure Jenkins Projects Library Example

Just load required resource. For our scenario, we will:

1. Load Jenkins Global Shared Library & Assure Jenkins Projects Library
2. Invoke a global variable to be run in a manner similar to a specific type of Jenkins Project

```groovy
/* Accessing multiple libraries with one statement */
@Library(['pdxc-pipeline-lib@assure', 'assure-jenkins-library@master']) _

functions = [:]
pipelineLambda(functions) // Invoke a Lambda Pipeline
```

> Note: here we are loading master version of the library, but we could also load a specific branch or tag.

## 2. Push Pipeline Definition

DXC Assure Digital Platform engineering team follows an automatic build process known as PUSH PIPELINE.

All what this process does is build and package artifact bundles for later release and deployment, moving them to an
intermediate artifact warehouse. This warehouse varies depending on the type of artifact:

- Business Service and Core Service artifacts are stored on PDXC Artifactory, under any of the Assure organizations. These
  artifacts are known as bundles.

- Infrastructure artifacts are stored on GitHub in a separate organization under the control of the platform team (`dxc-assure-tfapi`
or `assure-terraform-images`). These artifacts are also known as Silver Images.

For deployment management, for any type of artifact not creating an entry in Artifactory, it will be required to
create a descriptor that is deployed there to track any change and be part of the deployment process (see deployment section)

The essence of the PUSH PIPELINE is very simple: it is an automatic job implemented in Jenkins whose steps are
defined by a script file called Jenkinsfile.

Based on this, every DXC Assure Platform component will need to generate its own artifacts, descriptors, etc... and
that is managed by the PUSH PIPELINE. We have defined some generic pipelines that are already available to be used
just by importing / loading them and configuring some values.

In general, we understand that every pipeline will have some steps in common even when they are implemented differently:

1. **Validate pipeline**: ensure that all the conditions to be run are fulfilled:
   - Repository name is OK - to prevent that when a repo is copied from another the execution could override
     original one
   - Branches - in some cases build process should only be done for certain branches
   - ...
2. **Set up and Initialization**: configure and load whatever element is needed during pipeline execution, i.e. git or
   npm init, initialize some values...
3. **Code Quality**: analyze and validate the source code and publish the results. In some cases this might even be a
   requirement to be fulfilled in order to progress the build process, e.g. when including security and vulnerability scanning.
4. **Testing**: Unit / Component test to validate the functionality implemented on a controlled (mocked) environment
5. **Build / Package**: prepare the sources and create the bundle as needed
6. **Upload to Warehouse (Artifactory)**: Upload the generated artifact including the properties required by the [PULL
   Pipeline](https://github.dxc.com/assure/assure-promotion-pull-pipeline/blob/master/README.md)

In the sections below we detail the already implemented pipelines as well as how they can be consumed and extended:

### 2.1. Generic Push Pipelines

In this Assure Platform Jenkins Project Library we are implementing some generic pipelines already but we have also
developed and published some of them in the Global Jenkins Shared Library. Anyway we want to centralize the logic on
this library, so it should be avoided to just calling the pipelines defined there, and always consume them through a
pipeline defined here (just a proxy to the one in the Shared Library). This will allow us to:

1. Protect Assure from changes in the Shared Libraries: if there are changes, we might be able to fix any issue just by
   modifying this library.

2. Enable easy extension: if we decide that an extra step is required in all pipelines of a type, we could just add
   the required modifications in this library so every pipeline using it will automatically get the modifications.

3. Have a unified implementation across all the pipelines.

### 2.1.1. Functions extensions through Assure Library

> See below the skip and execute another function sections for further details.

Given that in Jenkins Shared Libraries we can only include generic functionality and that we are defining there some
pipelines that already require an Assure Customization that applies to every pipeline (see the get artifact path
function example), in many of these pipelines we already add a custom function when invoking the pipeline defined in
the Shared Library.

However, it is possible that a specific pipeline for a project will require an additional function or customization
to skip a stage. This means that additional functions and skip settings could be added from that single pipeline and
we need to combine both inputs: individual pipeline and Assure specifics. We have implemented this in the following
way:

- If a stage is marked to be skipped in any of the inputs, it will be skipped.
- If the same stage has additional functions to be executed, first the ones defined by Assure will be executed and
  then the ones for that single pipeline.
- If the stage only has functions from one of the inputs, that will be executed.

> Note: In the case of custom pipelines, it would not be needed to go through this library and the Shared Libraries
> could be used directly, always keeping in mind the versioning considerations.

These are the already implemented Push Pipelines available to be used for the creation of Assure pipelines:

- Lambda JavaScript Push Pipeline
- Terraform Deploy Projects Push Pipeline + Silver Image
- SPA UI Push Pipeline
- .Net Push Pipeline
- Docker Push Pipeline
- Java Zip Push Pipeline
- Java Docker Push Pipeline
- Multidocker Push Pipeline
- Data Push Pipeline
- NPM Push Pipeline
- Custom Push Pipeline
- Lambda -Python- Push Pipeline

> IMPORTANT!
>
> The basic implementation detail of the Generic Pipelines is using a file **conf.yml** that has to be placed at the
> root level of the repository and that will contain the different customizable values to be used by the pipeline, i.e.
> _urls, credentials identifiers, paths..._ that are specific for the artifact / component being built.
>

**List of stages and associated keys**

This table shows the different stages of the generic pipelines and the keys used to identify them for the skip and extension functionality.
 Only those where the skip / extend function is available are listed.

| Stage				| Key		| Used in       					| Comment         |
|-------------------------------|---------------|-------------------------------------------------------|-----------------|
| Pipeline info 		| info   	| ALL         						| |
| Validate pipeline 		| validate 	| ALL   						| |
| Set up 			| setup 	| ALL 							| |
| Build 			| build 	| DotNet, npm, UI 					| |
| Build Docker Images 		| build 	| MultiDocker						| Only for skip |
| Install 			| install 	| LambdaJS, npm, UI, Python 					| |
| Restore 			| restore 	| DotNet 						| | 
| Pack 				| pack 		| DotNet 						| |
| Test 				| test 		| DotNet, LambdaJS, Docker, MultiDocker, UI, npm, Python 	| |
| Zipping artifact 		| zip 		| ALL except Docker  and MultiDocker 			| |
| Code quality 			| codequality 	| DotNet, LambdaJS, Docker, MultiDocker, UI, npm, Python 	| |
| SonarQube scan 		| sonar 	| DotNet, LambdaJS, UI, npm 				| Only for skip |
| Checkov scan 			| checkov 	| DotNet, LambdaJS, Docker, MultiDocker, UI, npm, Python 	| Only for skip | 
| Upload artifact 		| upload 	| ALL 		| See 'Build Docker Image' for docker pipeline and 'Building <imagename< for multi docker |
| Build Docker Image 		| upload 	| Docker 						| |
| Building <imagename> 		| upload 	| MultiDocker 						| Only for extension |
| Get WAR files 		| warfiles 	| MultiDocker 						| Only for skip |
| Update artifact properties 	| update 	| ALL 							| See 'Updating <imagename> for multi docker |
| Updating <imagename> 		| update 	| MultiDocker 						| Only for extension |
| Generate Release Notes 		| releaseNotes 	| ALL | This stage will be executed only on the "master" branch |
| Branch protection 		| protection 	| ALL 							| Only for skip. This stage will be executed only when "release" branch |
| Deploy custom 		| deploy 	| ALL | Not in DotNet, Not in MultiDocker, Not in npm, Not in Python |

> Deploy pipeline does not include the skip or extend function

In each of the following sections, the specific customizable properties to be set are detailed.

#### 2.1.2. Dynamic configuration

The Generic Pipelines are using a default `conf.yml` file that contains the different customizable values to be used by the pipeline.

However, it might be possible to overwrite these values if necessary with your own `conf.yml` file which has to be placed at the root folder of your repo. The process will automatically merge both files by overwriting the default values with those from your `conf.yml` if the parameter already exists or adding them if not.

There is another way to override the default `conf.yml` values programmatically but it's not recommended. You should use the previous one whenever possible.

Example - _to override the conf.yml programmatically during pipeline execution._

```groovy
     def assureCustomStages = [:]
     assureCustomStages["overrideYaml"] = ["func": { conf ->
                                                 return PushPipelineUtils.writeConfigData(conf, this)
                                         }]
```

#### 2.1.3. Common custom configuration values

There are some custom values to be defined in the conf.yml file of a generic pipeline that are always required
independently on the type of pipeline.

These are the common values:

| Property | Description | Default value | Sample value |
| --------  | -------- | --------  | --------|
| ----          | **Pipeline default values** | ----       | ----- |
| repositoryName | This value should match the name of the repository where the executed pipeline is defined | diaas-generic | assure-environment-service-ui |
| -----         | **Jenkins Assure Repository Values (deprecated)**  | ------  |-----  |
| libraryOrg (deprecated)   | GitHub organization where the Assure Library resides | diaas | assure |
| libraryRepo (deprecated)   | Name of the Assure Jenkins Projects Library GitHub repository | assure-platform-jenkins-projects-library | assure-jenkins-library |
| libraryBranch (deprecated) | Branch of the repository to take (used for versioning)  | master | master |
| -----         | **GitHub initialization Values**  | ----- | ----- |
| gitHubCredential | Jenkins defined credential that is able to connect to GitHub |  | pdxc-jenkins |
| gitEmail      | Email address to be used when setting up git |  | <EMAIL> |
| gitUsername   | Username to be used when setting up git |  | Jenkins User |
| gitHubUrl     | (Optional) URL (only host) of the GitHub instance to be used. This can be skipped as it will take PDXC GitHub instance by default  |  | github.dxc.com |
| ----          | **Artifactory related Values** | ----------- |  ----- |
| artifactoryURL         | (Optional) Artifactory base url. This can be skipped as by default the PDXC instance is taken. | `https://artifactory.platformdxc-mg.com/artifactory` | |
| artifactoryCredentials | Name of the Jenkins credential that contains the data to connect to Artifactory  | diaas-rw | diaas-rw |
| artifactRepository     | Target Artifactory repository where the generated artifact has to be stored. | diaas-generic | assure-generic \| assure-docker |
| artifactPath           | Complete path, excluding branch and repository name, where the generated artifact has to be stored in Artifactory |  | assure-csam/console/ui/bundle |
| artifactLocalPath      | Folder in the local execution directory where the artifact to be uploaded to Artifactory can be found. Relative path from workspace. Normally it will be an empty string | "" | |
| artifactProperties     | Map of properties to be set up on the uploaded artifact. Please notice the format| artifactProperties:<br>&nbsp;&nbsp;- prop: "status"<br>&nbsp;&nbsp;&nbsp;&nbsp;value: "ready"<br>&nbsp;&nbsp;- prop: "type"<br>&nbsp;&nbsp;&nbsp;&nbsp;value: "generic" | artifactProperties:<br>&nbsp;&nbsp;- prop: "status"<br>&nbsp;&nbsp;&nbsp;&nbsp;value: "ready"<br>&nbsp;&nbsp;- prop: "type"<br>&nbsp;&nbsp;&nbsp;&nbsp;value: "generic" \| "tf" \| "docker" |
| ----          | **Notification Values** | ---- | ----|
| sendMail      | Flag (true / false) to indicate whether an email with the result of the Jenkins Job execution has to be sent at the end of the pipeline. | false | true / false |
| emailTo       | (Optional even when sendMail is true) - A static email address that will always receive the notification of the result. It can be empty as by default it also takes the pipeline execution requester in the mail 'To'. One option could be to include the email address of a MS Teams channel. | "" | <EMAIL> |
| emailFrom     | Which email should be set as sender of the notification email. It's recommended to stick with the default value |  <EMAIL> | |
| attachmentFileEmail | If a template has to be included in the email. Normally it will be blank. | "" | |
| teamsSecretHookId   | To send a MS Team message using a configured hook. This value would be the name of the Jenkins variable that contains the hook address. | TEAMS_PUSH_WEBHOOK |  |
| releaseNotes  | Flag (true/false) to indicate whether a release and it correspondent notes will be auto-generated in the Github repository | false | false / true |

#### 2.1.4. Different configuration values per stage

In some cases, the pipeline uses the same value in different stages and normally it is only required to define a
single value in the configuration file (conf.yml). However, it might be possible that the same value could not be
valid and we need to make use of a different value per stage.

In preparation for this scenario, all the generic pipelines have been developed using a specific function to retrieve
the values from the configuration file (see org.pdxc.jenkins.util.ValuesUtils class in Jenkins Shared Library). This
functions received not only the name of the property to be retrieved but also a prefix that could be used. The
function will try to retrieve a property called `prefix_propertyName` and if not found will look for `propertyName`
only.

So, in our generic pipelines, to retrieve a value from the conf.yml, we will always pass the `key` of the current
stage as prefix allowing the definition of different values.

Example:

- conf.yml:

  ```yaml
      artifactoryPath: "assure-platform/api-proxy-lambda"
      upload_artifactoryPath: "assure-platform/lambdafunctions"
  ```

- Jenkinsfile

  ```groovy
      stage("Set up")
          ...
          def path = getVariable(config, "artifactoryPath", "setup")
          echo "Path in Set up stage: ${path}"
          ...
      stage("Upload to Artifactory")
          ...
          def path = getVariable(config, "artifactoryPath", "upload")
          echo "Path in Upload stage: ${path}"
          ...
  ```

The outcome would be:

- Path in _Set up stage_: `assure-platform/api-proxy-lambda`  
- Path in _Upload stage_: `assure-platform/lambdafunctions`

#### 2.1.5. Skip complete stage

Even when the generic pipelines already define the list of stages to be executed, it is possible to skip most of them
if a specific pipeline do not require them. This in combination with the "add additional stages" and "execute another
function" capabilities will allow the developer to customize the pipeline modifying not only the values but the
stages executed.

For this, the generic pipeline receives a parameter called stagesMap of type Map. The keys for this map should be the
keys of the stages defined in the pipeline (see below the keys for each generic pipeline defined). The value
associated to the key will be another Map containing two potential properties: "skip" and "func" (defined in next
sections).

If the property "skip" is set and the value is "true", the corresponding stage will be completed skipped and not
executed. It is not required to define the values for every stage, only for those to be skipped.

Example - _to skip a stage with key "setup" during pipeline execution._

```groovy
     def stagesMap = [:]
     stagesMap["setup"] = ["skip": true]
     pipeline_generic(stagesMap)
```

**Please notice that some steps might not be available for being skipped.**

#### 2.1.6. Execute another function as last step of a stage

In addition to the steps and functions already included in a generic stage, it is possible that a custom pipeline
might require to execute some extra actions to complete the required logic. In order to enable this option, at the
end of each stage it has been implemented a mechanism to execute extra code that has to be provided as an input
parameter when invoking the pipeline.

This will allow developer to customize not only the values but part of the behaviour of the pipeline.

The approach is the same as to indicate that a stage has to be skipped, and even the same input parameter is used: a
Map that contains the required values associated with the stage defined key. In this case, a property called "func"
will be assigned to the specific function.

Example - _Print Jenkins Build number of setup stage_

```groovy
     def printBuildNumber() {
         echo "Build number: "${env.BUILD_NUMBER}""
     }

     def stagesMap = [:]
     stagesMap["setup"] = ["skip": false, "func": this.&printBuildNumber]
     pipeline_generic(stagesMap)
```

**Important: please notice the correct way to assign the function to the property using _this.&_**

> Note: if a step is skipped, the configured post functions will not be run neither.

#### 2.1.7. Add additional stages

This is the same as for the additional functions definition, but instead of having some simple functions executed as
an extra step of the stage where they are added, it is also possible to define a complete set of new stages that
will be executed after the defined one, continue the original workflow of the pipeline when they are completed.

This is the appropriate approach to:

1. Add additional stages to the base generic pipeline, i.e. when before or after a stage there is some extra logic
   with "enough entity" to be part of an individual (or some) extra stages.

2. Replace one of the base generic stages with a new one. In order to do this, the stage will be marked to be skipped
   and the logic to create the new stage will be added as a post function to the previous stage.

This logic is done using the same functionality described in the previous section ("func" property on the parameters
received by the pipeline) with the only difference that the passed function will define inside the stage or stages to
be added.

Example - _Install angular cli stage_

```groovy
     def addStageInstallCLI() {
         stage("Install angular cli") {
             sh "npm install -g @angular/cli"
         }
     }

     def stagesMap = [:]
     stagesMap["setup"] = ["skip": false, "func": this.&addStageInstallCLI]
     pipeline_generic(stagesMap)
```

When displaying this in the Jenkins UI, it will automatically display a stage called "Install angular cli" after the
"Set up" stage.

As this will be created complete stages, it is possible to define any type of parameters and settings as when
defining them on a standard pipeline (conditions, agents...) and they will have access to the variables on the same
way that pipeline where they are added.

> Please note that the stages in the function will be created using the scripted syntax.

#### 2.1.8. Custom docker for Agent

By default, for every generic pipeline, there is always a custom dockerfile defined and used by default that already
includes all the tools and set ups required to run the default stages. However, it is possible to define a different
docker file to be used in case that, for example, an additional stage is added or an additional function is called
that might require extra settings.

In order to do this, the pipelines receive an additional parameter where the path (local) to a different dockerfile
can be passed. So, if the repository contains a dockerfile and the call to the pipeline includes the path, this one
will be used for the complete execution of the pipeline.

So, we could just have a dockerFile created at the root level of our repository and just pass it to the pipeline:

```groovy
    def stagesMap = [:]

    pipeline_generic(stagesMap, "mycustomDockerFile")
```

#### 2.1.9. Lambda (JS) Push Pipeline

This pipeline is thought for Lambda components implemented on JS (like: [API Proxy Lambda](https://github.dxc.com/assure/assure-platform-api-invocation-proxy)).

The objective of the pipeline is to build the zip file and deploy it as an artifact to Artifactory with the correct
properties to be later taken by DXC Assure Pull Pipeline.

##### *******. Implementation

This is implemented in the Shared Library (`/vars/pipeLineLambda.groovy`) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the artifact needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for
further details.

##### *******. Stages and execution

There is a default dockerfile used from the Shared Libraries that contains the required installations:

- curl
- git
- nodejs
- npm

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is
    a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is
    a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Git initialization to be able to use git commands
    - npm credentials set up
    - Calculate version by reading the information from package.json file and updating "patch" with the build number.
      This is not updated in the GitHub repository but in the source files that will be zipped and will be used

4. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected. This
    testing would be against mocked third party and connectors to isolate the testing to the component itself.

    See [Implementing Testing for Push and Pull Pipeline section in Automation Guidelines](https://github.dxc.com/DIaaS/diaas-platform/blob/master/README.md) for implementation details.

5. **Install** (_install_): npm script execution to get all dependencies and generate the required sources in
    preparation for the next step. Command is configurable. A sample would be: "npm install".

6. **Zipping artifact** (_zip_): Take all the sources and create a zip file using the name specified in the
    configuration file.

7. **Code quality** (_codequality_): This step will take the source code and perform the static analysis to validate it. You can skip Checkov  and SonarQube analysis independently using the key (_checkov_) and (_sonar_) respectively. Post stage functions will be executed only for the _codequality_ key.

8. **OSS Governance** (_ossgovernance_): This step ensures compliance with open-source software governance policies by scanning dependencies and generating a report. The report is saved as artefact to identify any licensing or security issues.

9. **Upload artifact** (_upload_): Take the generated zip file and deploy it to Artifactory based on the path provided in the configuration file.

10. **Update artifact properties** (_update_): This step is required to set up the properties required by the DXC Assure Pull Pipeline in order to take and promote the artifacts: status, etc.

11. **Deploy custom** (_customDeploy_): Load the environment configuration, deploy the generated artefact and execute the post-deployment tests on the environments readed.

    See [Deployment from Push pipelines](#23-deployment-from-push-pipelines) section for further details.

12. **Generate Release Notes** (_releaseNotes_): This step will generate automatically the release with a complete changelog in the Github repository.

13. **Deploy artifact** (_deploy_): Load the life cycle environment configuration, deploy the generated artefact and execute the post-deployment tests on the environments readed.

    See [Deployment from Push pipelines](#23-deployment-from-push-pipelines) section for further details.

14. **Branch protection** (_protection_): This step will apply a branch protection rule (only applied for specific "release/\*" pattern branches).

15. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even on error cases). This is used to send the required notifications with the build process results and to clean the workspace.

##### *******. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property      | Description   | Default value | Sample value |
| ----          | ----          | ---- | ---- |
| npmrcCredential | Name of the Jenkins credential that contains the credentials to be used for npm | npmrc | |
| ----          | ----          | ---- | ---- |
| dependenciesPackageFile | Name of the json file that contains the version information. Normally it will be package.json | package.json | |
| dependenciesPackagePath | Path in the repository (workspace) where the json file can be found. Normally it will be at root level and the value will be an empty string. This is used also when doing the install. | "" | code |
| dependenciesPackageAttribute | Name of the attribute in the json file that contains the version data. Normally it will be 'version' | version | |
| ---- | ---- | ---- | ---- |
| scriptName | npm command to be executed on install step. | install | |
| scriptParams | additional parameters to be added to the npm command | --only=prod | --unsafe-perm |
| ---- | ---- | ---- | ---- |
| zipSourceFolder   | Folder to be zipped to create the artifact to be uploaded. Normally it will be root folder: "." | . | code |
| targetZipName     | Base name to be set for the Zip file to be created. The version will be automatically added after it. |  | assure-environment-service-lambda |
| zipInclude        | Pattern of files to include in the zip. Empty to include all files and directories, e.g. "src/\*\*" | "" (which includes everything from `zipSourceFolder`) | "api/\*\*, factory/\*\*, models/\*\*, node_modules/\*\*, providers/\*\*, errors/\*\*, properties/\*\*, config.js, index.js, routes.js, package.json" |
| zipScript | A custom script that will produce the archive. The script is passed three positional arguments: `targetZipName`, `zipSourceFolder` and `zipInclude`. Empty to use the internal Jenkins zip (the original pipeline implementation) | | `bash myZipScript.sh` |
| ---- | ---- | ---- | ---- |
| checkovQualityGate  | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in Checkov | false | true \| false |
| ---- | ---- | ---- | ---- |
| sonarSources       | Comma-separated folders containing main source files. Relative paths from dependenciesPackagePath. | "." | |
| sonarExclusions    | Files to leave out of analysis. Specify the files with relative paths to the project base directory, that match your [exclusion pattern](https://docs.sonarqube.org/latest/project-administration/narrowing-the-focus/). | "test/\*\*/\*,tests/\*\*/\*,test/\*,node_modules/\*\*/*\,handlers/test/\*\*/\*,\*\*/twistcli/\*\*/\*,twistcli/\*\*/\*,twistcli" | |
| sonarHost | (optional) SonarQube host name. You can override the default value if you want to use your own SonarQube instance | _assureSonarqubeHost_ | "https://youSonarQubeHostInstance"|
| sonarToken | (optional) | _assureSonarqubeToken_ | _yourSonarQubePersonalTolen_ |
| sonarQualityGate | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in SoanrQube | false | true \| false |
| sonarCoverage       | Coverage arguments | "sonar.javascript.lcov.reportPaths" | |
| sonarCoveragePath   | Coverage path arguments | "./coverage/lcov.info" | |
| ---- | ---- | ---- | ---- |
| nodeVersion   | node version that will be used as base docker image. | 18 | Supported versions: 18, 20, 21, 22 |
| npmVersion | npm version that will be used to be installed in the docker image. | 9 | Supported versions: 9, 10 |
| ---- | ---- | ---- | ---- |
| testCommands | Commands list that will be executed during the `test` stage | |  testCommands:<br>- "npm install"<br>- "npm run test:ci" |

##### *******. Consumption Example

In the following links you will find examples for the minimum required configuration regarding lambda artifacts:

- [Jenkins file Example](docs/samples/pipelinelambda/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinelambda/conf.yml)

#### 2.1.10. Terraform Deploy Projects Push Pipeline and Silver Image

For the Terraform deployment projects (normally for those where the repository ends on `-deploy` e.g. "assure-environment-service-**deploy**") we have defined a
generic logic that can be used for all of them, allowing to just have a Jenkinsfile skeleton in their repository
that will invoke and use this implemented logic.

A Terraform (TF) Silver Image is a copy of this deploy project but with all the necessary changes to make it
compliant with PDXC TF API. It basically consists on the same TF code but with external dependencies resolved and  
other changes. Although the Silver Image is a copy of this one, the PUSH pipeline logic is different. Hence, during  
the copy process, we need to overlay the target Jenkins file with a new one, which can be found in this repository
under the `resources/custom/deploy-project/tfapi` folder.

The objective of this pipeline is to create, for those branches considered as valid, a new repository on the Silver
Images organization [DXC Assure TFAPi](https://github.dxc.com/dxc-assure-tfapi) | [Assure Terraform Images](https://github.dxc.com/assure-terraform-images) with the required logic (in Terraform) to deploy the deployment package.

##### ********. Implementation

> **Even when this has been created to be used as generic pipeline, it does not implement the skip and extension
> functions as, for the moment, it has been found that all the -deploy project require the same steps and common
> logic. This could be included later without impacting the already implemented pipelines.**

This is implemented in the Shared Library (`/vars/pipelineDeploy.groovy`) as global variable which behave similarly to built-in steps.

##### ********. Stages and execution

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info**: Load the configuration file (conf.yml) and print its components for reference.

2. **Validate pipeline**: Ensure that the repository is correct to prevent undesired executions. Also
, it checks that the branch is one of the valid ones from where silver images can be created:

    - master
    - release/*
    - feature/*
    - fix/*
    - rnn.n*

    If the branch name does not match any of the above, the rest of the steps will be skipped.

3. **Set up**: In this step, git is initialize to be able to use git commands

4. **Create Silver Image Repository**: Silver Images are stored in a separate GitHub organization called 'dxc-assure
    -tfapi'. This stage creates the repo on that organization by means of the GitHub API

5. **Copy files from tfapi folder**: Overwrite Silver Image Jenkinsfile and any other relevant file with the ones
    under [resouces/custom/deploy-project/tfapi](resources/custom/deploy-project/tfapi) so we can execute a proper (and different) PUSH
    pipeline for the Silver Image itself.

6. **Checkov** (_checkov_): This step will take the source code and perform the static analysis to validate it.

    > When extending this stage it's possible to skip and add post stage functions.

7. **Resolve external dependencies**: This stage executes the bash script 'resolve.sh' which is in charge of
    resolving the external dependencies so the Silver Image is PDXC-compliant. This script receives one parameter to
    decide what to do when a branch with the same name as current branch does not exist in the TF modules repository.
    There are two alternatives:
    - Stop and end in an error. This is the expected behaviour when creating a release.
    - Continue and use the latest version of the modules available in master branch.

8. **TF Code Quality**: This stage aims to validate Terraform version.

9. **Get artefact data**:  Creates the `artefacts.json` file with the information extracted from the variables included in the terraform files "_constants.tf_" and "_*.vars.tf_" that match the following patterns:

    - "bundle_`<name | path | type | version | branch>`_`<suffix>`" e.g "bundle_name_lambda"
    - "image_`<name | path | tag | branch>`_`<suffix>`"

    See [Assure Platform Terraform Modules - Development Guidelines](https://github.dxc.com/assure/assure-platform-terraform-modules/blob/master/DevelopmentGuidelines.md) for further details.

10. **Commit Silver Image** (_commitSI_): Finally, we copy all the source code in this repository into the Silver Image and commit
    the change. Please note that we are excluding .git file, /tfapi folder and the cloned folder in the copy.
    Additionally, we copy the conf.yml contents to the new repository so they are used in the Silver Image pipeline. Also,
    the version that will be set up in the Silver Image is calculated. Is important to note that when using Silver Image
    during early phases, it contains the modules version from the date when the deploy project was pushed, any changes
    done to the modules afterwards without a new version from the deploy project being committed and pushed will not be
    in that particular Silver Image.

    > When extending this stage it's not possible to skip it but it's possible to add post stage functions.

11. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

12. **Branch protection**: this step will apply a branch protection rule (only applied for specific `release/*` pattern branches).

13. **Post actions**: List of actions performed in all cases when the pipeline execution ends (even on error cases).
    This is used to send the required notifications with the build process results and to clean the workspace.

> Note: Some additional steps that could be added later are:
>
> - **Code quality** (_codequality_): This step will take the source code and perform the static analysis to validate it.
>
> - **Test** (_test_): Execution of tests to validate that the implemented logic works as expected.
>
>   See [Implementing Testing for Push and Pull Pipeline section in Automation Guidelines
>   ](https://github.dxc.com/DIaaS/diaas-platform/blob/master/README.md) for implementation details.

##### ********. Customizable values (conf.yml)

Below values are customizable for this type of pipeline apart from the common ones:

| Property      | Description   | Default value | Sample value |
| ----          | ----          | ---- | ---- |
| lifecycleDisabled | Do we want to disable promotion of the generated artifact (silver image) in our DXC Assure Platform lifecycle (prevent the generation of the descriptor in Artifactory to be retrieved from Jenkins Pull pipeline)? | false | true \| false |
| silverImageOrganization: | Name of the GitHub organization where the Silver Image repository has to be created. For TF11 `dxc-assure-tfapi` should be used and for TF12 should be used `assure-terraform-images`  | dxc-assure-tfapi | assure-terraform-images |
| modulesReleaseVersion: | Specific Terraform Modules Version to be used (e.g: "release/21.1.2"). Release version must exist, otherwise pipeline will fail. | "" | |
| pipelineType | (Optional) Only required if you are using tf12 | | TERRAFORM_DEPLOY_PIPELINE_12 |
| ---- | **TF 0.12 Compatibility report values** | ---- | ---- |
| TF12Hints_sendMail | Flag (true / false) to indicate whether an email with notification of changes in TF 0.12 compatibility has to be sent. | false | true \| false |
| TF12Hints_emailTo  | (Optional even when sendMail is true) - A static email address that will receive the notification of the report if it changed. It can be empty as by default it also takes the pipeline execution requester in the mail 'To'. | "" | <EMAIL> |
| TF12Hints_emailFrom | Which email should be set as sender of the notification email. | <EMAIL> | Recommendation: <EMAIL> |
| ---- | ---- | ---- | ---- |
| checkovQualityGate  | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in Checkov | false | true \| false |
| ---- | ---- | ---- | ---- |
| terraform:<br>&nbsp;&nbsp;version: "tf-version" | Terraform version that will be used to execute the TF files. Supported versions: 0.11, 0.12, 0.13 | | terraform:<br>&nbsp;&nbsp;version: "0.13" |
| ---- | ---- | ---- | ---- |
| auto_update_tf_version | Allows you to update the terraform version automatically during the deployment process | false | true \| false |

> Notes for **artifactProperties** property:
>
> The value of the Artifactory property `"type"` for this kind of artifacts is `"tf"`:
>
> ```yml
>artifactProperties:
>  - prop: "status"
>    value: "ready"
>  - prop: "type"
>    value: "tf"
> ```
>
> Notes for **modulesReleaseVersion** property:
>
> - The conf.yml value is **applied** for every repo branch. This means that even when we are working in master, **if the version parameter is specified, that value will be taken.**
> - Preference: if a module is using the **?ref=xxx** option, that value **will take preference always** over the rest of values.
> - Failsafe mode: if the conf.yml value is set, the pipeline will **only work** if that branch exists in the modules, **without** allowing the major.minor matching and applying this even when the pipeline is run in a master, feature or fix branches.

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding terraform deployment projects:

- [Jenkins file Example](docs/samples/pipelinedeploy/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinedeploy/conf.yml)

#### 2.1.11. SPA UI Push Pipeline

This pipeline is thought for UI components implemented on react or angular (like: [Assure Operations Portal](https://github.dxc.com/DIaaS/diaas-assure-operations-portal)).

The objective of the pipeline is to build the zip file and deploy it as an artifact to Artifactory with the correct
properties to be later taken by DXC Assure Pull Pipeline when the execution of test is success.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipelineUi.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the artifact needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for further details.

##### ********. Stages and execution

There is a default dockerfile used from the Shared Libraries that contains the required installations:

- curl
- git
- bash
- openssh

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - npm credentials set up
    - Calculate version by reading the information from package.json file and updating "patch" with the build number.
      This is not updated in the GitHub repository but in the source files that will be zipped and will be used

4. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected. This testing would be against mocked third party and connectors to isolate the testing to the component itself.

    See [Implementing Testing for Push and Pull Pipeline section in Automation Guidelines](https://github.dxc.com/DIaaS/diaas-platform/blob/master/README.md) for implementation details.

5. **Install** (_install_): npm script execution to get all dependencies and generate the required sources in preparation for the next step. Command is configurable. A sample would be: "npm install".

6. **Build** (_build_): npm script execution to compile the application into an output directory. Command is configurable from package.json file. A sample would be: "npm run build".

7. **Zipping artifact** (_zip_): Take all the sources and create a zip file using the name specified in the
    configuration file.

8. **Code quality** (_codequality_): This step will take the source code and perform the static analysis to validate it.
    You can skip Checkov  and SonarQube analysis independently using the key (_checkov_) and (_sonar_) respectively. Post stage functions will be executed only for the _codequality_ key.

9. **OSS Governance** (_ossgovernance_): This step ensures compliance with open-source software governance policies by scanning dependencies and generating a report. The report is saved as artefact to identify any licensing or security issues.

10. **Upload artifact** (_upload_): Take the generated zip file and deploy it to Artifactory based on the path provided in the configuration file.

11. **Deploy custom** (_customDeploy_):  This step is required set up the properties required by the DXC Assure Pull Pipeline in order to take and promote the artifacts: status, etc.

    See [Deployment from Push pipelines](#23-deployment-from-push-pipelines) section for further details.

12. **Update artifact properties** (_update_): This step is required set up the properties required by the DXC Assure Pull Pipeline in order to take and promote the artifacts: status, etc.

13. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even on error cases). This is used to send the required notifications with the build process results and to clean the workspace.

14. **Generate Release Notes** (_releaseNotes_): This step will generate automatically the release with a complete changelog in the Github repository.

15. **Branch protection** (_protection_): This step will apply a branch protection rule (only applied for specific `release/*` pattern branches).

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property      | Description   | Default value | Sample value |
| ----          | ----          | ---- | ---- |
| npmrcCredential | Name of the Jenkins credential that contains the credentials to be used for npm | npmrc | |
| ----          | ----          | ---- | ---- |
| dependenciesPackageFile | Name of the json file that contains the version information. Normally it will be package.json | package.json | |
| dependenciesPackagePath | Path in the repository (workspace) where the json file can be found. Normally it will be at root level and the value will be an empty string. This is used also when doing the install. | "" | |
| dependenciesPackageAttribute | Name of the attribute in the json file that contains the version data. Normally it will be 'version' | version | |
| ----          | ----          | ---- | ---- |
| scriptName    | npm command to be executed on install step. | "install" | |
| scriptParams    |  additional parameters to be added to the npm command. | "--only=prod" | |
| build_scriptName | npm command to be executed on build step. | "build" | |
| build_scriptParams |  additional parameters to be added to the npm command. | "" | |
| ----          | ----          | ---- | ---- |
| zipSourceFolder | Folder to be zipped to create the artifact to be uploaded. Normally it will be `build` folder. | "build" | |
| targetZipName | Base name to be set for the Zip file to be created. The version will be automatically added after it. |  | "environment-service-ui" |
| zipInclude | Pattern of files to include in the zip. Empty to include all files and directories, e.g. "src/\*\*" | "" (which includes everything from `zipSourceFolder`) | |
| zipScript | A custom script that will produce the archive. The script is passed three positional arguments: `targetZipName`, `zipSourceFolder` and `zipInclude`. Empty to use the internal Jenkins zip (the original pipeline implementation) | | `bash myZipScript.sh` |
| ----          | ----          | ---- | ---- |
| sonarSources       | Comma-separated folders containing main source files. Relative paths from dependenciesPackagePath. | "." | |
| sonarExclusions    | Files to leave out of analysis. Specify the files with relative paths to the project base directory, that match your [exclusion pattern](https://docs.sonarqube.org/latest/project-administration/narrowing-the-focus/). | "test/\*\*/\*,\*\*/test/\*\*/\*,\*\*/twistcli/\*\*/\*,twistcli/\*\*/\*,twistcli" | |
| sonarQualityGate | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in SonarQube | false | true \| false |
| sonarCoverage       | Coverage arguments | "sonar.javascript.lcov.reportPaths" | |
| sonarCoveragePath   | Coverage path arguments | "./coverage/lcov.info" | |
| ----          | ----          | ---- | ---- |
| checkovQualityGate  | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in Checkov | false | true \| false |
| ----          | ----          | ---- | ---- |
| nodeVersion   | node version that will be used as base docker image. | 18 | Supported versions: 18, 20, 21, 22 |
| npmVersion | npm version that will be used to be installed in the docker image. | 9 | Supported versions: 9, 10 |


##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding ui artifacts:

- [Jenkins file Example](docs/samples/pipelineui/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelineui/conf.yml)

#### 2.1.12. DotNet Push Pipeline

This pipeline is thought for components implemented on C# (like: [DMS Library Service](https://github.dxc.com/DIaaS/diaas-dms-library-service)).

The objective of the pipeline is to build the zip file and deploy it as an artifact to Artifactory with the correct
properties to be later taken by DXC Assure Pull Pipeline when the execution of test is success.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipelineDotnet.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the artifact needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for further details.

##### ********. Stages and execution

There is a default dockerfile used from the Shared Libraries that contains the required installations:

- nuget
- dotnet-sdk

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - npm credentials set up
    - Calculate version by reading the information from package.json file and updating "patch" with the build number.
      This is not updated in the GitHub repository but in the source files that will be zipped and will be used

4. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected. This testing would be against mocked third party and connectors to isolate the testing to the component itself.

    See [Implementing Testing for Push and Pull Pipeline section in Automation Guidelines](https://github.dxc.com/DIaaS/diaas-platform/blob/master/README.md) for implementation details.

5. **Restore** (_restore_): dotnet command to restore dependencies and tools of a project. Command is configurable from conf.yml file.

6. **Build** (_build_): dotnet command to build the project and its dependencies. Command is configurable from conf.yml.

7. **Pack** (_pack_): dotnet command to publish the application and its dependencies to a folder. Command is configurable from conf.yml.

8. **Zipping artifact** (_zip_): Take all the sources and create a zip file using the name specified in the
    configuration file.

9. **Code quality** (_codequality_): This step will take the source code and perform the static analysis to validate it.
    You can skip Checkov and SonarQube analysis independently using the key (_checkov_) and (_sonar_) respectively. Post stage functions will be executed only for the _codequality_ key.

10. **Upload artifact** (_upload_): Take the generated zip file and deploy it to Artifactory based on the path
    provided in the configuration file.

11. **Update artifact properties** (_update_): This step is required set up the properties required by the DXC Assure Pull Pipeline in order to take and promote the artifacts: status, etc.

12. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

13. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific `release/*` pattern branches).

14. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even on error cases). This is used to send the required notifications with the build process results and to clean the workspace.

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property      | Description   | Default value | Sample value |
| ----          | ----          | ---- | ---- |
| npmrcCredential | Name of the Jenkins credential that contains the credentials to be used for npm | npmrc | |
| ----          | ----          | ---- | ---- |
| dotnetVersion  | This property is used to select the sdk version. | 7 |  Supported versions: 6, 7, 8 |
| ----          | ----          | ---- | ---- |
| dependenciesPackageFile | Name of the json file that contains the version information. Normally it will be package.json | package.json | |
| dependenciesPackagePath | Path in the repository (workspace) where the json file can be found. Normally it will be at root level and the value will be an empty string. This is used also when doing the install. | "" | |
| dependenciesPackageAttribute | Name of the attribute in the json file that contains the version data. Normally it will be 'version' | version | |
| ----          | ----          | ---- | ---- |
| csProjectPath | Path to the csproj file from which the version will be extracted |  | "src/Services/SecurityData/SecurityData.Api.Host.Lambda/SecurityData.Api.Host.Lambda.csproj" |
| csProjectTestPath | Path to the csproj file that will be used to execute the test command |  | "Services/SecurityData/SecurityData.Api.Host.UnitTests/SecurityData.Api.Host.UnitTests.csproj" |
| restore_commandName | dotnet command to restore step. |  | "restore" |
| restore_commandParams | restore arguments |  | "\*.sln --configfile ~/.config/NuGet/NuGet.Config" |
| build_commandName | dotnet command to build step. |  | "build" |
| build_commandParams | build arguments |  | "\*.sln" |
| pack_commandName | dotnet command to pack step. | | "publish" |
| pack_commandParams | pack arguments | | "src/Structure.API/Structure.API.csproj -c Release --no-restore -o ArtifactFolder" |
| ----          | ----          | ---- | ---- |
| zipSourceFolder | Folder to be zipped to create the artifact to be uploaded. Normally it will be root folder: "." | . | |
| targetZipName | Base name to be set for the Zip file to be created. The version will be automatically added after it. | environment-service | |
| zipInclude | Pattern of files to include in the zip. Empty to include all files and directories, e.g. "src/\*\*" | "" (which includes everything from `zipSourceFolder`) | |
| zipScript | A custom script that will produce the archive. The script is passed three positional arguments: `targetZipName`, `zipSourceFolder` and `zipInclude`. Empty to use the internal Jenkins zip (the original pipeline implementation) | | `bash myZipScript.sh` |
| ----          | ----          | ---- | ---- |
| sonarSources       | Comma-separated folders containing main source files. Relative paths from dependenciesPackagePath. | "." | |
| sonarExclusions    | Files to leave out of analysis. Specify the files with relative paths to the project base directory, that match your [exclusion pattern](https://docs.sonarqube.org/latest/project-administration/narrowing-the-focus/). | "\*\*/bin/\*\*/\*,\*\*/obj/\*\*/\*, \*\*/\*.js, \*\*/\*.css, \*\*/\*.cshtml.cs" | |
| sonarQualityGate | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in SonarQube | false | true \| false |
| sonarCoverage       | Coverage arguments | "sonar.cs.opencover.reportsPaths" | |
| sonarCoveragePath   | Coverage path arguments | "coverage.xml" | |
| ----          | ----          | ---- | ---- |
| checkovQualityGate  | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in Checkov | false | true \| false |
| ----          | ----          | ---- | ---- |
| version |  Number version to be set for the Zip file to be created. | "0.0.0.0" | |

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding DotNet artifacts:

- [Jenkins file Example](docs/samples/pipelinedotnet/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinedotnet/conf.yml)

#### 2.1.13. Docker Push Pipeline

This pipeline is thought for docker components (like: [Assure Ssm Parameter Config Handler](https://github.dxc.com/DIaaS/assure-ssm-parameter-config-handler)).

The objective of the pipeline is to build a docker image and deploy it as an image to Artifactory with the correct
properties to be later taken by DXC Assure Pull Pipeline when the execution of test is success.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipelineDocker.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the image needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for further details.

##### ********. Stages and execution

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Calculate version by reading the information from the artifactoryTag value.
      This is not updated in the GitHub repository

4. **Code quality** (_checkov_): This step will take the source code and perform the static analysis to validate it.

5. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected. This testing would be against mocked third party and connectors to isolate the testing to the component itself.

    See [Implementing Testing for Push and Pull Pipeline section in Automation Guidelines](https://github.dxc.com/DIaaS/diaas-platform/blob/master/README.md) for implementation details.

6. **Build and Upload Docker Image** (_upload_): this step build the docker image and take the generated docker image and deploy it to Artifactory based on the path provided in the configuration file.

7. **Deploy custom** (_deploy_): Load the environment configuration, deploy the generated artefact and execute the post-deployment tests on the environments readed.

    See [Deployment from Push pipelines](#23-deployment-from-push-pipelines) section for further details.

8. **Update artifact properties** (_update_): This step is required set up the properties required by the DXC Assure Pull Pipeline in order to take and promote the artifacts: status, etc.

9. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

10. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific `release/*` pattern branches).

11. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even on error cases). This is used to send the required notifications with the build process results and to clean the workspace.

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property            | Description                                                                                               | Default value | Sample value |
| ------------------- | --------------------------------------------------------------------------------------------------------- | ---------------------- | ------------------- |
| artifactoryTag | This property will be used to compose the docker image tag with the following format "`artifactoryTag-buildNumber`" e.g. "0.0.0-1" | 0.0.0 | |
| buildTarget | Folder where docker will be build. | . | |
| artifactoryFileName | Base name to be set for the docker image to be created. The version will be automatically added after it. | config | "base" |
| buildFromDockerFile | Name of the docker file to be used for build. Include path if it's required | | Dockerfile |
| ----          | ----          | ---- | ---- |
| checkovQualityGate  | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in Checkov | false | true \| false |
| nodeVersion   | node version that will be used as base docker image. | 18 | Supported versions: 18, 20, 21, 22 |
| npmVersion | npm version that will be used to be installed in the docker image. | 9 | Supported versions: 9, 10 |


> Note for **artifactProperties** property:
>
> The value of the Artifactory property `"type"` for this kind of artifacts is `"docker"`:
>
> ```yml
>artifactProperties:
>  - prop: "status"
>    value: "ready"
>  - prop: "type"
>    value: "docker"
> ```

##### ********. Optional values (conf.yml)

Apart from the customizable values already included in the above section. This pipeline can require some additional ones:

| Property                    | Description                                                                                          | Default value | Sample value |
| --------------------------- | ---------------------------------------------------------------------------------------------------- | ---------------------- | --------------------------- |
| npmcredentials-required     | Boolean value to enable the bellow properties                                                        |                   | false |
| npmcredentials-userpassword | Credential Id stored in jenkins. It's the npm registry user and password to add as a build argument. |                        | |
| npmcredentials-token        | Credential Id stored in jenkins. It's the npm registry token to add as a build argument.             |                        | |
| npmcredentials-auth         | Credential Id stored in jenkins. It's the npm registry auth to add as a build argument.              |                        | |
| testResultsPath             | Path to JUnit XML files. You can specify multiple patterns of files separated by commas.             |                | **/*.xml |

The Job level credentials section in the bellow url provides assistance in order to create the credentials in jenkins:
[https://github.dxc.com/pages/platform-dxc/devcloud-docs/jenkins/credentials/]

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding docker artifacts:

- [Jenkins file Example](docs/samples/pipelinedocker/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinedocker/conf.yml)

#### 2.1.14. Data Push Pipeline

This pipeline is thought for data pipelines components (like: [Assure System Schemas](https://github.dxc.com/DIaaS/assure-system-schemas)).

The objective of the pipeline is to build the zip file and upload it as an artifact to Artifactory with the correct properties.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipelineData.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the image needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for further details.

##### ********. Stages and execution

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Calculate version by reading the information from the artifactoryTag value.
      This is not updated in the GitHub repository

4. **Zipping artifact** (_zip_): Take all the sources and create a zip file using the name specified in the
    configuration file.

5. **Upload artifact** (_upload_): Take the generated zip file and deploy it to Artifactory based on the path
    provided in the configuration file.

6. **Deploy custom** (_customDeploy_): Load the environment configuration, deploy the generated artefact and execute the post-deployment tests on the environments readed.

    See [Deployment from Push pipelines](#23-deployment-from-push-pipelines) section for further details.

7. **Update artifact properties** (_update_): This step is required set up the properties required by the DXC Assure  
    Pull Pipeline in order to take and promote the artifacts: status, etc.

8. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even
    on error cases). This is used to send the required notifications with the build process results and to clean the
    workspace.

9. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

10. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific `release/*` pattern branches).

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline can be further customized adding below ones:

|  Property | Description | Default value | Sample value |
| :-------------: | :------------------------------: | :--------------------: | :--------------------: |
| zipSourceFolder | Folder to be zipped to create the artifact to be uploaded. Normally it will be root folder: "." |  | "." |
|  targetZipName  | Base name to be set for the Zip file to be created. The version will be automatically added after it. |  | environment-service |
|   zipInclude    |  Pattern of files to include in the zip. Empty to include all files and directories, e.g. "src/\*\*"  | "" (which includes everything from `zipSourceFolder`)| |
|   zipScript     | A custom script that will produce the archive. The script is passed three positional arguments: `targetZipName`, `zipSourceFolder` and `zipInclude`. Empty to use the internal Jenkins zip (the original pipeline implementation) | | `bash myZipScript.sh` |
| --------------- | ---------------  |  ---------------   |  ---------------   |
| version |  Number version to be set for the Zip file to be created. | "0.0.0" | |
| nodeVersion   | node version that will be used as base docker image. | 18 | Supported versions: 18, 20, 21, 22 |
| npmVersion | npm version that will be used to be installed in the docker image. | 9 | Supported versions: 9, 10 |


##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding data artifacts:

- [Jenkins file Example](docs/samples/pipelinedata/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinedata/conf.yml)

#### 2.1.15. Custom Push Pipeline

This pipeline is thought for custom pipelines components (like: [Diaas DMS MimeType Service](https://github.dxc.com/DIaaS/diaas-dms-mimetype-service)).

The objective of the pipeline is to build a zip file containing specific dependencies (that suits your needs) and upload them as an artifact to Artifactory with the correct properties.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipelineCustom.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the image needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for further details.

##### ********. Stages and execution

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Calculate version by reading the information from the artifactoryTag value.
      This is not updated in the GitHub repository

4. **Zipping artifact** (_zip_): Take all the sources and create a zip file using the name specified in the
    configuration file.

5. **Upload artifact** (_upload_): Take the generated zip file and deploy it to Artifactory based on the path
    provided in the configuration file.

6. **Deploy custom** (_customDeploy_): Load the environment configuration, deploy the generated artefact and execute the post-deployment tests on the environments readed.

    See [Deployment from Push pipelines](#23-deployment-from-push-pipelines) section for further details.

7. **Update artifact properties** (_update_): This step is required set up the properties required by the DXC Assure  
    Pull Pipeline in order to take and promote the artifacts: status, etc.

8. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

9. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific "release/\*" pattern branches).

10. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even
    on error cases). This is used to send the required notifications with the build process results and to clean the
    workspace.

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline can be further customized adding below ones:

|    Property     | Description  | Default value | Sample value |
| :-------------: | :----------: | :-----------: | :----------: |
| zipSourceFolder |    Folder to be zipped to create the artifact to be uploaded. Normally it will be root folder: "." | | "." |
|  targetZipName  | Base name to be set for the Zip file to be created. The version will be automatically added after it. | | environment-service |
|   zipInclude    |  Pattern of files to include in the zip. Empty to include all files and directories, e.g. "src/\*\*"  | "" (which includes everything from `zipSourceFolder`)| "." |
|   zipScript     | A custom script that will produce the archive. The script is passed three positional arguments: `targetZipName`, `zipSourceFolder` and `zipInclude`. Empty to use the internal Jenkins zip (the original pipeline implementation) | | `bash myZipScript.sh` |
| ------------ | ------------ | ------------ | ------------ |
|     version     | Number version to be set for the Zip file to be created. | "0.0.0" | |

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding custom artifacts:

- [Jenkins file Example](docs/samples/pipelinecustom/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinecustom/conf.yml)

#### 2.1.16. MultiDocker Push Pipeline

This pipeline is designed for the creation of different Docker images in batch as a requirement for Assure Policy Asia Integral API process, which usually have 15~20 WAR files and have to generate the Dockerfile for each one.

The objective of the pipeline is to automate the build process of the different Docker images in a parallel/sequential stages (**Note: \*\*\*custom deployment is a Work In Progress\*\*\***) and upload/update them in Artifactory with the correct
properties to be later taken by DXC Assure Pull Pipeline when the execution of test is success.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipelineMultiDockerParallel.groovy) as global variable which behave similarly to built-in steps.

###### ********.1 Required Directory Structure & Files

In order to let run the execution of the pipeline a mandatory directory structure containing different files is required.

```text
Example:

(root repository)
+- files                            # Common necessary files required by Dockerfile(s)
|   +- cacerts
|   +- jssecacerts
|   +- ...
+- customFiles
|   +- omni-business-case-services  # Component name folder
|       +- Dockerfile               # Specific Dockerfile for component
|   +- omni-business-date-services
|       +- Dockerfile
|   +- .
|   +- .
|   +- .
|   +- N-component-name
|        
+- components.json                  # JSON file containing name(s) & WAR(s) Artifactory path(s)

```

**components.json** _example_

```json
{
    "components": [
        {
            "name": "omni-business-date-services",
            "warArtifactoryPath": "digitalinsurance-docker/integral/api/war/fix/build/2021.05.06.18/omni-business-date-services.war"
        },
        {
            "name": "omni-business-case-services",
            "warArtifactoryPath": "digitalinsurance-docker/integral/api/war/fix/build/2021.05.06.18/omni-business-case-services.war"
        }
    ]
}
```

##### ********. Stages and execution

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Set up** (_setup_): In this step, the tools used are initialized.

2. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is a mandatory step that cannot be skipped.

3. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is a mandatory step that cannot be skipped.

4. **Process WAR Files & Docker Image Creation**

    - **Setup** (_test_): in this step, the version to be built is calculated.
        - Calculate version by reading the information from the artifactoryTag value.
      This is not updated in the GitHub repository.

    - **Get WAR files** (_warfiles_): this step download the WAR files specified in the **components.json** file which includes the name and the WAR Artifactory path where the specific file is located.  
    For each component specified in the **components.json**, a parallel execution will take place, defining a new series of _sequential_ stages:

    - **Building _component_name_** (_build_): this step build the docker image for the specified component.

    - **Checkov scan** (_checkov_): this step executes the Checkov Scan for specific docker image component just generated, creating a log report with the results.

    - **Upload _component_name_** (_upload_): this step takes the generated docker image for the specified component and upload it to Artifactory based on the path provided in the configuration file.

    - **Updating _component_name_** (_update_): this step is required set up the properties over the docker image generated required by the DXC Assure Pull Pipeline in order to take and promote the artifacts: status, etc.

    - **Pipeline post actions** (_post_): list of actions performed in all cases when the pipeline execution ends (even on error cases). This is used to send the required notifications with the build process results and to clean the workspace.

5. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

6. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific _/release_ pattern branches).

_Pipeline Run Example_

![Pipeline Run Example](docs/img/multidocker_example.png)

_Artifactory Structure Created Example_

![Jfrog Structure Example](docs/img/multidocker_jfrog_structure.png)

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| artifactoryTag | This property will be used to compose the docker image tag with the following format "`artifactoryTag-buildNumber`" e.g. "0.0.0-1" | "0.0.0" | |
| buildTarget | Folder where docker will be build. | | "." |
| buildFromDockerFile | Name of the docker file to be used for build. Include path if it's required | | "Dockerfile" |

> Note for **artifactProperties** property:
>
> The value of the Artifactory property `"type"` for this kind of artifacts is `"docker"`:
>
> ```yml
>artifactProperties:
>  - prop: "status"
>    value: "ready"
>  - prop: "type"
>    value: "docker"
> ```

##### ********. Optional values (conf.yml)

Apart from the customizable values already included in the above section. This pipeline can require some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| npmcredentials-required | Boolean value to enable the bellow properties | | false \| true |
| npmcredentials-userpassword | Credential Id stored in jenkins. It's the npm registry user and password to add as a build argument. | | |
| npmcredentials-token | Credential Id stored in jenkins. It's the npm registry token to add as a build argument. | | |
| npmcredentials-auth | Credential Id stored in jenkins. It's the npm registry auth to add as a build argument. | | |

The Job level credentials section in the bellow url provides assistance in order to create the credentials in jenkins:
[https://github.dxc.com/pages/platform-dxc/devcloud-docs/jenkins/credentials/]

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding multi-docker artifacts:

- [Jenkins file Example](docs/samples/pipelinemultidocker/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinemultidocker/conf.yml)

#### 2.1.17. Java Docker Push Pipeline

This pipeline is thought for java applications (like: [CSR Orchestrator Application](https://github.dxc.com/assure-delivery/csr-orchestrator-application)).

The objective of the pipeline is to install the java application into the jenkins workspace to be used inside a docker image, building the docker image and deploy it as an image to Artifactory with the correct properties to be later taken by DXC Assure Pull Pipeline when the execution of test is success.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipelineJavaDocker.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the image needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for further details.

##### ********. Stages and execution

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Calculate version by reading the information from the pom.xml or dependenciesPackageFile value.
      This is not updated in the GitHub repository

4. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected. This testing would be against mocked third party and connectors to isolate the testing to the component itself.

    See [Implementing Testing for Push and Pull Pipeline section in Automation Guidelines](https://github.dxc.com/DIaaS/diaas-platform/blob/master/README.md) for implementation details.

5. **Install** (_install_): mvn script execution to get all dependencies and generate the required sources in preparation for the next step. Command is configurable. A sample would be: "mvn clean install".

6. **Code quality** (_codequality_): This step will take the source code and perform the static analysis to validate it. You can skip Checkov and SonarQube analysis independently using the key (_checkov_) and (_sonar_) respectively. Post stage functions will be executed only for the _codequality_ key.

7. **Build and Upload Docker Image** (_upload_): this step build the docker image and take the generated docker image and deploy it to Artifactory based on the path provided in the configuration file.

8. **Update artifact properties** (_update_): This step is required set up the properties required by the DXC Assure Pull Pipeline in order to take and promote the artifacts: status, etc.

9. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

10. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific _/release_ pattern branches).

11. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even on error cases). This is used to send the required notifications with the build process results and to clean the workspace.

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| buildTarget | Folder where docker will be build. |  | "." |
| artifactoryFileName | Base name to be set for the docker image to be created. The version will be automatically added after it. | config | |
| buildFromDockerFile | Name of the docker file to be used for build. Include path if it's required | Dockerfile | |
| artifactRepositoryJson |  Artifactory parent repository folder to upload .json file generated by docker inspect command. Json generated file includes the Artifactory urls on "RepoTags" property (versioned + latest docker images) i.e: ![docker_inspect](docs/img/docker_inspect_example.png)    |     "assure-generic" | |
| ----          | ----          | ---- | ---- |
| dependenciesPackageFile | Name of the json file that contains the version information. Normally it will be package.json | "pom.xml" | |
| dependenciesPackagePath | Path in the repository (workspace) where the pom file can be found. Normally it will be at root level and the value will be an empty string. This is used also when doing the install. | "." | |
| dependenciesPackageAttribute | Name of the attribute in the pom file that contains the version data. Normally it will be 'version' | "version" | |
| ----          | ----          | ---- | ---- |
| sonarSources       | Comma-separated folders containing main source files. | "." | |
| sonarExclusions    | Files to leave out of analysis. Specify the files with relative paths to the project base directory, that match your [exclusion pattern](https://docs.sonarqube.org/latest/project-administration/narrowing-the-focus/). | "test/\*\*/\*,tests/\*\*/\*,test/\*,handlers/test/\*\*/\*" | |
| sonarQualityGate | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in SonarQube | false | true \| false |
| sonarCoverage       | Coverage arguments | "sonar.coverage.jacoco.xmlReportPaths" | |
| sonarCoveragePath   | Coverage path arguments | "./target/site/jacoco/jacoco.xml" | |

| ----          | ----          | ---- | ---- |
| checkovQualityGate  | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in Checkov | false | true \| false |

> Note for **artifactProperties** property:
>
> The value of the Artifactory property `"type"` for this kind of artifacts is `"docker"`:
>
> ```yml
>artifactProperties:
>  - prop: "status"
>    value: "ready"
>  - prop: "type"
>    value: "docker"
> ```

##### ********. Optional values (conf.yml)

Apart from the customizable values already included in the above section. This pipeline can require some additional ones:

| Property                    | Description                                                                                          | Default / Sample value | Sample value |
| --------------------------- | ---------------------------------------------------------------------------------------------------- | ---------------------- | ---------------------- |
| npmcredentials-required     | Boolean value to enable the bellow properties                                                        | false                  | |
| npmcredentials-userpassword | Credential Id stored in jenkins. It's the npm registry user and password to add as a build argument. |                        | |
| npmcredentials-token        | Credential Id stored in jenkins. It's the npm registry token to add as a build argument.             |                        | |
| npmcredentials-auth         | Credential Id stored in jenkins. It's the npm registry auth to add as a build argument.              |                        | |
| javaVersion | java version that will be used as base docker image. Supported version: 11 , 17, 21 | 11 | 11 |
| mavenVersion   | Maven version that will be used as base docker image. Supported versions: 8, 9  | 8 | 8 |

The Job level credentials section in the bellow url provides assistance in order to create the credentials in jenkins:
[https://github.dxc.com/pages/platform-dxc/devcloud-docs/jenkins/credentials/]

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding java docker artifacts:

- [Jenkins file Example](docs/samples/pipelinejavadocker/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinejavadocker/conf.yml)

#### 2.1.18. Java Zip Push Pipeline

This pipeline is thought for java applications (like: [Assure Product Calculation](https://github.dxc.com/assure/assure-product-calculation-rest-v1-lambda)).

The objective of the pipeline is to build the zip file and deploy it as an artifact to Artifactory with the correct properties to be later taken by DXC Assure Pull Pipeline.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipelineJavaZip.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the artifact needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for further details.

##### ********. Stages and execution

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Calculate version by reading the information from the pom.xml value.
      This is not updated in the GitHub repository

4. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected. This testing would be against mocked third party and connectors to isolate the testing to the component itself.

    See [Implementing Testing for Push and Pull Pipeline section in Automation Guidelines](https://github.dxc.com/DIaaS/diaas-platform/blob/master/README.md) for implementation details.

5. **Build** (_build_): mvn script execution to get all dependencies and generate the required sources in preparation for the next step. Command is configurable. A sample would be: "mvn clean package".

6. **Code quality** (_codequality_): This step will take the source code and perform the static analysis to validate it.  You can skip Checkov  and SonarQube analysis independently using the key (_checkov_) and (_sonar_) respectively. Post stage functions will be executed only for the _codequality_ key.

7. **Zipping artifact** (_zip_): Take all the contents from the generated JAR file, together with the ones specified through the 'additionalResourcesFolder' property, and create a zip file using the name set in the configuration file ('targetZipName').

8. **Upload artifact** (_upload_): Take the generated zip file and deploy it to Artifactory based on the path provided in the configuration file.

9. **Update artifact properties** (_update_): This step is required to set up the properties required by the DXC Assure Pull Pipeline in order to take and promote the artifacts: status, etc.

10. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

11. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific _/release_ pattern branches).

12. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even on error cases). This is used to send the required notifications with the build process results and to clean the workspace.

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| artifactRepositoryJson |  Artifactory parent repository folder to upload .json file generated by docker inspect command. Json generated file includes the Artifactory urls on "RepoTags" property (versioned + latest docker images) i.e: ![docker_inspect](docs/img/docker_inspect_example.png) | "diaas-generic" | "assure-generic" |
| ----          | ----          | ---- | ---- |
| targetZipName | Base name to be set for the Zip file to be created. The version will be automatically added after | | java-basic |
| ----          | ----          | ---- | ---- |
| scriptName    | Command to be executed on install step. | "mvn" | |
| scriptParams | Additional parameters to be added to the mvn command | clean package | |
| dependenciesPackageFile | Name of the json file that contains the version information. Normally it will be package.json | "pom.xml" | |
| dependenciesPackagePath | Path in the repository (workspace) where the pom file can be found. Normally it will be at root level and the value will be an empty string. This is used also when doing the install. | "." | |
| dependenciesPackageAttribute | Name of the attribute in the pom file that contains the version data. Normally it will be 'version' | "version" | |
| ----          | ----          | ---- | ---- |
| sonarSources       | Comma-separated folders containing main source files. | "." | |
| sonarExclusions    | Files to leave out of analysis. Specify the files with relative paths to the project base directory, that match your [exclusion pattern](https://docs.sonarqube.org/latest/project-administration/narrowing-the-focus/). | "test/\*\*/\*,tests/\*\*/\*,test/\*,handlers/test/\*\*/\*" | |
| sonarQualityGate | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in SonarQube | false | true \| false |
| sonarCoverage       | Coverage arguments | "sonar.coverage.jacoco.xmlReportPaths" | |
| sonarCoveragePath   | Coverage path arguments | "./target/site/jacoco/jacoco.xml" | |
| ----          | ----          | ---- | ---- |
| checkovQualityGate  | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in Checkov | false | true \| false |

##### ********. Optional values (conf.yml)

Apart from the customizable values already included in the above section. This pipeline can require some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| targetFolder | Base folder where the resources have been created. Normally it has been configured on pom.xml | target | |
| additionalResourcesFolder | Extra resources that need to be zipped. It will included all files and directories that are inside that folder |"." | |
| javaVersion | java version that will be used as base docker image. Supported version: 11 , 17, 21 | 11 | 11 |
| mavenVersion   | Maven version that will be used as base docker image. Supported versions: 8, 9  | 8 | 8 |

The Job level credentials section in the bellow url provides assistance in order to create the credentials in jenkins:
[https://github.dxc.com/pages/platform-dxc/devcloud-docs/jenkins/credentials/]

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding java zip artifacts:

- [Jenkins file Example](docs/samples/pipelinejavazip/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinejavazip/conf.yml)

#### 2.1.19. NPM Push Pipeline

This pipeline is thought for NPM libraries (e.g: [Assure Platform Access Management Service nodejs utils](https://github.dxc.com/assure/assure-platform-access-management-node-utils)).

The objective of the pipeline is to build the NPM library and publish it in Artifactory with the correct properties to be later taken by DXC Assure Pull Pipeline.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipelineNpm.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the artifact needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for further details.

##### ********. Stages and execution

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Calculate version by reading the information from the artifactoryTag value. This is not updated in the GitHub repository

4. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected. This testing would be against mocked third party and connectors to isolate the testing to the component itself.

    See [Implementing Testing for Push and Pull Pipeline section in Automation Guidelines](https://github.dxc.com/DIaaS/diaas-platform/blob/master/README.md) for implementation details.

5. **Install** (_install_): npm script execution to get all dependencies and generate the required sources in
    preparation for the next step. Command is configurable. A sample would be: "npm install".

6. **Build** (_build_): npm script execution to compile the application into an output directory. Command is configurable from package.json file. A sample would be: "npm run build".

7. **Code quality** (_codequality_): This step will take the source code and perform the static analysis to validate it. You can skip Checkov  and SonarQube analysis independently using the key (_checkov_) and (_sonar_) respectively. Post stage functions will be executed only for the _codequality_ key.

8. **OSS Governance** (_ossgovernance_): This step ensures compliance with open-source software governance policies by scanning dependencies and generating a report. The report is saved as artefact to identify any licensing or security issues.

9. **Upload artifact** (_upload_): Take the generated artifact and deploy it to Artifactory based on the path provided in the configuration file.

10. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

11. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific _/release_ pattern branches).

12. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even on error cases). This is used to send the required notifications with the build process results and to clean the workspace.

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline can be further customized adding below ones:

| Property | Description | Default value | Sample value |
| :------: | :---------: | :-----------: | :----------: |
| npmrcCredential | Name of the Jenkins credential that contains the credentials to be used for npm | npmrc | |
| ----          | ----          | ---- | ---- |
| artifactRepository     | Target Artifactory repository where the generated artifact has to be stored. | diaas-npm-local | |
| masterTag | [Distribution tag](https://docs.npmjs.com/adding-dist-tags-to-packages) that will be added to the npm artifact when publishing. Only when master branch is used. | "next" | |
| releaseTag | [Distribution tag](https://docs.npmjs.com/adding-dist-tags-to-packages) that will be added to the npm artifact when publishing. Only when master branch is NOT used. | "latest" | |
| betaTag | [Distribution tag](https://docs.npmjs.com/adding-dist-tags-to-packages) that will be added to the npm artifact when publishing. Only when feature/fix or development branch are used. | "beta" | |
| ----          | ----          | ---- | ---- |
| dependenciesPackageFile | Name of the json file that contains the version information. Normally it will be package.json | package.json | |
| dependenciesPackagePath | Path in the repository (workspace) where the json file can be found. Normally it will be at root level and the value will be an empty string. This is used also when doing the install. | "" | |
| dependenciesPackageAttribute | Name of the attribute in the json file that contains the version data. Normally it will be 'version' | version | |
| scriptName    | npm command to be executed on install step. | "install" | |
| scriptParams    |  additional parameters to be added to the npm command. | "--only=prod" | |
| build_scriptName | npm command to be executed on build step. | "build" | |
| build_scriptParams |  additional parameters to be added to the npm command. | "" | |
| ----          | ----          | ---- | ---- |
| sonarSources       | Comma-separated folders containing main source files. Relative paths from dependenciesPackagePath. | "." | |
| sonarExclusions    | Files to leave out of analysis. Specify the files with relative paths to the project base directory, that match your [exclusion pattern](https://docs.sonarqube.org/latest/project-administration/narrowing-the-focus/). | "" | |
| sonarQualityGate | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in SonarQube | false | true \| false |
| sonarCoverage       | Coverage arguments | "sonar.javascript.lcov.reportPaths" | |
| sonarCoveragePath   | Coverage path arguments | "./coverage/lcov.info" | |
| ----          | ----          | ---- | ---- |
| checkovQualityGate  | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in Checkov | false | true \| false |
| ----          | ----          | ---- | ---- |
| nodeVersion   | node version that will be used as base docker image. | 18 | Supported versions: 18, 20, 21, 22 |
| npmVersion | npm version that will be used to be installed in the docker image. | 9 | Supported versions: 9, 10 |


##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding NPM artifacts:

- [Jenkins file Example](docs/samples/pipelinenpm/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinenpm/conf.yml)

#### 2.1.20. Lambda Python Push Pipeline

This pipeline is thought for Lambda components implemented on python.

The objective of the pipeline is to build the zip file and deploy it as an artifact to Artifactory with the correct
properties to be later taken by DXC Assure Pull Pipeline.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipeLinePython.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the artifact needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for
further details.

##### ********. Stages and execution

There is a default dockerfile used from the Shared Libraries that contains the required installations:

- curl
- git
- pip
- pytest

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is
    a mandatory step that cannot be skipped.

2. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is
    a mandatory steps that cannot be skipped.

3. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Git initialization to be able to use git commands
    - Calculate version by reading the information from version property (conf.yml file) and updating "patch" with the build number.
      This is not updated in the GitHub repository but in the source files that will be zipped and will be used

4. **Install** (_install_): Installation of packages to get all dependencies and generate the required sources in
    preparation for the next step. Command is configurable. A sample would be: "pip3 install -r requirements.txt".

5. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected.

6. **Zipping artifact** (_zip_): Take all the sources and create a zip file using the name specified in the
    configuration file.

7. **Code quality** (_codequality_): This step will take the source code and perform the static analysis to validate it. You can skip Checkov  and SonarQube analysis independently using the key (_checkov_) and (_sonar_) respectively. Post stage functions will be executed only for the _codequality_ key.

8. **Upload artifact** (_upload_): Take the generated zip file and deploy it to Artifactory based on the path
    provided in the configuration file.

9. **Update artifact properties** (_update_): This step is required set up the properties required by the DXC Assure  
    Pull Pipeline in order to take and promote the artifacts: status, etc.

10. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

11. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific _/release_ pattern branches).

12. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even
    on error cases). This is used to send the required notifications with the build process results and to clean the
    workspace.

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| dependenciesFilePath | Path in the repository (workspace) where the requirements file can be found. Normally it will be at root level and the value will be an empty string. This is used also when doing the install. | "" | |
| ---- | ---- | ---- |
| scriptName | command to be executed on install step. | "pip3 install" | |
| scriptParams | additional parameters to be added to the install command | "-r requirements.txt" | |
| ---- | ---- | ---- |
| zipSourceFolder   | Folder to be zipped to create the artifact to be uploaded. Normally it will be root folder: "." | "." | |
| targetZipName     | Base name to be set for the Zip file to be created. The version will be automatically added after it. | | python-lambda-bundle |
| zipInclude        | Pattern of files to include in the zip. Empty to include all files and directories, e.g. "site-packages/\*\*" | "" (which includes everything from `zipSourceFolder`)|
| zipScript | A custom script that will produce the archive. The script is passed three positional arguments: `targetZipName`, `zipSourceFolder` and `zipInclude`. Empty to use the internal Jenkins zip (the original pipeline implementation) | | `bash myZipScript.sh` |
| ---- | ---- | ---- |
| version | Number version to be set for the Zip file to be created.  | "0.0.0" | |
| ----          | ----          | ---- | ---- |
| sonarSources       | Comma-separated folders containing main source files. Relative paths from dependenciesPackagePath. | "." | |
| sonarExclusions    | Files to leave out of analysis. Specify the files with relative paths to the project base directory, that match your [exclusion pattern](https://docs.sonarqube.org/latest/project-administration/narrowing-the-focus/). | "test/\*\*/\*,\*\*/test/\*\*/\*,\*\*/twistcli/\*\*/\*,twistcli/\*\*/\*,twistcli" | |
| sonarQualityGate | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in SonarQube | false | true \| false |
| sonarCoverage       | Coverage arguments | "sonar.python.coverage.reportPaths" | |
| sonarCoveragePath   | Coverage path arguments | "coverage.xml" | |
| ----          | ----          | ---- | ---- |
| pythonVersion | Python version that will be used as base docker image. | 8 | Supported versions: 8, 9, 10, 11, 12|
| ----          | ----          | ---- | ---- |
| checkovQualityGate  | Allows you to break the pipeline in case your code doesn't meet the quality gates defined in Checkov | false | true \| false |

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding lambda python artifacts:

- [Jenkins file Example](docs/samples/pipelinepython/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinepython/conf.yml)

#### 2.1.21. Rust Push Pipeline

This pipeline is intended for projects written in Rust

The objective of the pipeline is _either_ to build an artifact zip file and deploy it to Artifactory with the correct properties to be later taken by DXC Assure Pull Pipeline, _or_ to publish project crates to an Artifactory-hosted Crates Registry in order to be used as dependencies in other projects.

Note that you do not need to publish to a Crate Registry in order to use library crates - they can be directly referenced fro Github - but version management
is far more robust when using a crate registry.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipeLineRust.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the artifact needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for
further details.

##### ********. Stages and execution

There is a default dockerfile used from the Shared Libraries that contains the required installations:

- curl
- git
- bash
- openssh
- openssl
- gnupg
- docker
- jq
- cargo-audit
- cross

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline setup** (_setup_): This stage check different scenarios:

    - Check if the pipeline must execute as a lambda project.
    - Select and prepare the docker agent used for the following steps between our pre-defined Dockerfiles. 

    This is a mandatory step that cannot be skipped.

2. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is
    a mandatory step that cannot be skipped.

3. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is
    a mandatory steps that cannot be skipped.

4. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Git initialization to be able to use git commands
    - Credential initialization for Artifactory-hosted package registries
    - Calculate version by reading the information from version property (conf.yml file) and updating "patch" with the build number.
      This is not updated in the GitHub repository but in the source files that will be zipped and will be used

5. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected.

6. **Audit** (_audit_): Audit the dependencies for crates with security vulnerabilities reported to the RustSec Advisory Database.

7. **Build** (_build_): Compile local packages and all of their dependencies. In this stage one of multiple target architectures (if selected) will be compiled based on the configuration file specifications for the defined architectures.

8. **Deploy** (_deploy_): A "container" stage that decides whether to use the `zip` and `upload` stages to deploy the project
   into a generic Artifactory repository, or to use the `publish` stage to publish the Rust crates in the project to an
   Artifactory Crates Registry. The decision is based upon the type of repository that is named in the `conf.yml` 
   `artifactRespository` property.
   
   If that property is missing or the repository is neither of type `cargo` nor `generic`, then no deployment sub-stages are executed
   
   1. **Zipping artifact** (_zip_): Take all the sources and create a zip file using the name specified in the
      configuration file. If it's a multiple-architecture project it will create each zip file for every architecture defined in the configuration file. 
      If a custom file extension is selected, this stage will be automatically skipped.

      This stage is only executed for `generic` repositories
   
   2. **Upload artifact** (_upload_): Take the generated zip file and deploy it to Artifactory based on the path
      provided in the configuration file. If a custom file extension is selected, it will deploy the desired file to Artifactory instead.
       
      This stage is only executed for `generic` repositories

   3. **Publish** (_upload_ STET): Use `cargo publish` to publish the non-private crates in the project to an Artifactory Crates Registry. See below
      for important considerations about version management and the project configuration necessary to use this feature
      
      This stage is only executed for `cargo` repositories

9. **Update artifact properties** (_update_): This step is required set up the properties required by the DXC Assure  
    Pull Pipeline in order to take and promote the artifacts: status, etc.

10. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

11. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific _/release_ pattern branches).

12. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even
    on error cases). This is used to send the required notifications with the build process results and to clean the
    workspace.

##### ********. Customizable values (conf.yml)
Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| dependenciesPackageFile | Name of the json file that contains the version information. Normally it will be Cargo.toml | Cargo.toml | |
| dependenciesPackagePath | Path in the repository (workspace) where the toml file can be found. | code | |
| dependenciesPackageAttribute | Name of the attribute in the json file that contains the version data. Normally it will be 'version' | version | |
| ----          | ----          | ---- | ---- |
  artifactoryCrateRegistries | a list of objects with `id` and `credentials` properties, listing the Artifactory-hosted package registries that project uses and the name of the Jenkins credentials that should be used to acces them | Deduced from the project Cargo configuration | (see [below](#21214-referencing-artifactory-cargo-package-registries))
| ----          | ----          | ---- | ---- |
| coverage          | Whether or not to calculate test coverage (using llvm-cov)          | true | false |
| coverageArgs          | Extra arguments to pass to the llvm-cov command           | ---- | --ignore-filename-regex target/deps |
| ----          | ----          | ---- | ---- |
| nextestArgs          | Command to execute tests using nextest          | `--no-fail-fast --profile jenkins` |  |
| customTestArgs          | Extra arguments to nextestArgs command          | ---- | --skip |
| ----          | ----          | ---- | ---- |
| buildCommand          | Command to execute the build          | auditable build | ---- |
| buildArgs          | Extra arguments to build command          | --release --locked | ---- |
| customBuildCommand          | Command to execute the build for custom architectures using cross compiler         | build | ---- |
| customBuildArgs          | Extra arguments to build command for custom architectures using cross compiler        | `--release --target` | ---- |
| ----          | ----          | ---- | ---- |
| targetArchitecture          | Target architecture         | "" | - "x86_64-unknown-linux-gnu" - "aarch64-unknown-linux-gnu" |
| ----          | ----          | ---- | ---- |
| dockerImage          | Define the dockerimage that will be used during the pipeline execution          | default | debian, default |
| ----          | ----          | ---- | ---- |
| clippyExecution          | Allow/Disable the execution of the cargo clippy command during the test stage         | true | true/false |
| clippyArgs          | Cargo clippy arguments          | --all-targets --all-features | ---- |
| ----          | ----          | ---- | ---- |
| auditparams          | Cargo audit arguments          | -q | ---- |
| ----          | ----          | ---- | ---- |
| lambdaPipeline          | Specify the project as a lambda project          | false | true/false |
| ----          | ----          | ---- | ---- |
| zipSourceFolder   | Folder to be zipped to create the artifact to be uploaded. Normally it will be root folder: "." | "." | |
| targetZipName     | Base name to be set for the Zip file to be created. The version will be automatically added after it. | | rust-bundle |
| zipInclude        | Pattern of files to include in the zip. Empty to include all files and directories, e.g. "site-packages/\*\*" | "" (which includes everything from `zipSourceFolder`) |
| ----          | ----          | ---- | ---- |
| fileExtension          | Custom file extension to upload to Artifactory. By default is '.zip'   | '.zip' | ".exe", ".bat" |
| customArtifacts          | Custom artifact selection parameters to upload to Artifactory. | ---- | - architecture: "{selectedArchitecture}" <br> &nbsp; customArtifactName: "{artifactName}" <br> &nbsp; path: "{pathToFile}" |

##### ********. Referencing Artifactory Cargo Package Registries

This pipeline supports projects that _reference_ Artifactory cargo package registries and projects that _publish_ to Artifactory cargo package registries. In general the use of such registries is enabled in a project by using a `.cargo/config.toml` file in the Cargo project (i.e. the directory where the top-level `Cargo.toml` file is found).

The entries in that file that pertain to Artifactory crate repsitories should be restricted to:

```TOML
[registries]
<internal crate id> = {index = "https://artifactory.dxc.com/artifactory/git/<name of registry>.git"}
```
for the older Git protocol, or
```TOML
[registries]
<internal crate id> = {index = "sparse+https://artifactory.dxc.com/artifactory/api/<name of registry>/index/"}
```
for the `sparse` protocol (though at the time of writing, getting the `sparse` protocol to work on crate publication is proving elusive).

If the following conditions apply:

* Your `.cargo/config.toml` _is_ at the top level of your rust code - where your `Cargo.toml` file is
* The ID of the Jenkins credentials secret that contains the Cargo registry access token (which, for the Artifactory GIT protocol, _must_ be prefixed with **`Bearer `**) is the same as the name of the registry in Artifactory...

then that's all you need to do. The pipeline itself will create the correct credentials structures to allow your project to be built, for both Cargo and GIT, by introspecting your configuration.

However, you have full control over how you manage secrets for your registries. You can override any configuration that the pipeline discovers by specifying a n`artifactoryCrateRegistries` list in your `conf.yml` file:

```YAML
artifactoryCrateRegistries:
    - id: <internal crate id>
      credentials: <the ID of the Jenkins credentials secret to use>
```

Multiple such entries are allowed, of course.

If you omit the `credentials` property, the value of the `id` property is used as
the credentials secret ID.

**Example**
Assuming a project that references the following Artifactory crates registries:

* `assure-crates`, secret name `assure-crates`, GIT protocol
* `ai-crates`, secret name `ai-crates`, Sparse protocol
* `bps-crates`, secret-name `keep-out`, Sparse protocol

one can imagine a `.cargo/config.toml` file like this

```TOML
[registries]
assure-crates = {index = "https://artifactory.dxc.com/artifactory/git/assure-crates.git"}
ai-stuff = {index = "sparse+https://artifactory.dxc.com/artifactory/api/ai-crates/index/"}
bps = {index = "sparse+https://artifactory.dxc.com/artifactory/api/bps-crates/index/"}
```

The introspection algorithm would be able to properly configure the credentials for the first two entries, but not the last. Therefore, the minimal `conf.yml` configuration for this project would be

```YAML
artifactoryCrateRegistries:
    - id: bps
      credentials: keep-out
```

A fuller (but unnecessary) `conf.yml` acheiving the same purpose...

```YAML
artifactoryCrateRegistries:
    - id: assure-crates
    - id: ai-stuff
      credentials: ai-crates
    - id: bps
      credentials: keep-out
```

The most verbose `conf.yml` acheiving the same purpose...

```YAML
artifactoryCrateRegistries:
    - id: assure-crates
      credentials: assure-crates
    - id: ai-stuff
      credentials: ai-crates
    - id: bps
      credentials: keep-out
```

Note, again, that no other registry-resolution configuration need be in your project (and, in fact, if it IS then you may break the credentials structure that is created).

_However_, **_locally_** you are highly likely to require certain other configuration options to allow for local builds. It is **_strongly_** suggested that you place these items in "global" configuration locations that are NOT saved to GitHub with your project.

**GIT Configuration**
If you are using the GIT protocol with your registry, you will need the following 
items added to your global GIT configuration:

- Run the following commands:
  ```
  git config --global credential.helper store
  git config --global credential.store --file
  ```
- Add an entry to your GIT credentials file (see the GIT documentation for where that will be located):
  ```
  https://<your artifactory user-id>:<your artifactory access token>@artifactory.dxc.com
  ```

If you use a different GIT credentials helper (one that is more secure, perhaps) then set that up similarly.

**Cargo Configuration**
Whichever protocol you use, you will need to add the following to a `.cargo/config.toml` file somewhere in the parent directory hierarchy of your Rust project(s):

```TOML
[registry]
global-credential-providers = ["cargo:token"]

[net]
git-fetch-with-cli = true
```

Then, for each Artifactory registry that you use, obtain a Cargo token...

* Log in to Artifactory
* Navigate to the repository that holds your registry
* At the top right you will see a button named **Set me up** - click it
* Click the "generate token" button.

You will now see

* The `index` URL that must go into your Rust project Cargo configuration files
* The token that can be used to access that registry

So now:
* In your local project set up the `.cargo/config.toml` file as indicated above _this WILL be saved to GitHub_
* Open a terminal at a directory within your project
* Run the following command
  ```
  cargo login --registry <the name you use for your registry> <the token you saved>
  ```

  If you are using the GIT protocol, don't forget to prefix the token value with `Bearer `

**PUBLISHING to an Artifactory Crate Registry**
If you want your project to be published to an Artifactory cargo package registry instead of being zipped and uploaded to a
generic repository, you essentially need to change only one property in your `conf.yml` file - set the 
`artifactRepository` property to the name of the Artifactory cargo package registry that you wish to target. 

You should consider the following points:

* The pipeline will attempt to publish _all_ non-private crates in your project to the registry.
* The pipeline performs _no_ automatic version management - it is your responsibility to update
  the `Cargo.toml` `package.version` property for each or your crates, as necessary.
* The pipeline will _not_ fail if it cannot update an existing package in the registry due to the version
  already being present, but, in such a case, the `Publish` pipeline stage will be marked `UNSTABLE` in order
  to draw your attention to the situation when you review the outcome of the pipeline. Note that, unfortunately,
  this will still result in a green tick mark for the build in Github.

**Version Management**
The management of version updates is up to the crate maintainer.

However, a useful approach is as follows:

* Firstly protect your default branch from ANY direct updates. Instead, require
  a PR, preferably with at least one approval
* Secondly only EVER publish when the pipeline runs for the default branch. This may seem like
  a complex change to the pipeline to accompish, _however_ it is extremely simple. Just make 
  your Jenkins file look like this...

    ```
    @Library(['pdxc-pipeline-lib@assure', 'assure-jenkins-library@master']) _ 
    def stagesMap = [:]
    stagesMap['deploy'] = ['skip': "${BRANCH_NAME}" != 'master']
    pipelineRust(stagesMap)
    ```
  More complex requirements can be extrapolated from that - maybe implement
  a function to assess whether the `deploy` stage should be skipped or not.

Both of these techniques are used in a number of existing pulished crate repos:

* https://github.dxc.com/assure/assure-platform-rust-utilities
* https://github.dxc.com/assure/assure-platform-rust-aws-provider
* ...

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding rust artifacts:

- [Jenkins file Example](docs/samples/pipelinerust/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinerust/conf.yml)

#### 2.1.22. Multi Rust Push Pipeline

This pipeline is intended for projects written in Rust that need to generate
binaries for multiple machine architectures and profiles.

The objective of the pipeline is to build artifact zip files _for each declared build architecture_ and deploy them to Artifactory with the correct
properties to be later taken by DXC Assure Pull Pipeline.

It can ALSO be used to generate a single zip file artifact containing all built binaries, as might be required for a multi-architecture Lambda function or layer.

Note that this pipeline _cannot_ be used to publish crates to Artifactory Crates Registries.

##### ********. Implementation

This is implemented in the Shared Library (/vars/pipeLineMultiRust.groovy) as global variable which behave similarly to built-in steps.

We are using the predefined pipeline "as is" with just one additional extension where we pass a function to be used
to calculate the correct path where the artifact needs to be uploaded in Artifactory. We need this because we have
implemented a generic version in the Shared Library and this is an "Assure specific logic".

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for
further details.

##### ********. Stages and execution

There is a default dockerfile used from the Shared Libraries that contains the required installations:

- curl
- git
- bash
- openssh
- openssl
- gnupg
- docker
- jq
- cargo-audit
- cross

These are the stages implemented (among parenthesis the key to be used for extension):

1. **Pipeline setup** (_setup_): This stage check different scenarios:

    - Check if the pipeline must execute as a lambda project.
    - Select and prepare the docker agent used for the following steps between our pre-defined Dockerfiles. 

    This is a mandatory step that cannot be skipped.

2. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is
    a mandatory step that cannot be skipped.

3. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is
    a mandatory steps that cannot be skipped.

4. **Set up** (_setup_): In this step, the tools used are initialized and also the version to be built is calculated:

    - Git initialization to be able to use git commands
    - Credential initialization for Artifactory-hosted package registries
    - Calculate version by reading the information from version property (conf.yml file) and updating "patch" with the build number.
      This is not updated in the GitHub repository but in the source files that will be zipped and will be used

5. **Test** (_test_): Execution of the component tests to validate that the implement logic works as expected.

6. **Audit** (_audit_): Audit the dependencies for crates with security vulnerabilities reported to the RustSec Advisory Database.

7. **Build** (_build_): Compile local packages and all of their dependencies. In this stage one or more of multiple target architectures (if selected) will be compiled based on the configuration file specifications for the defined architectures.

8. **Zipping artifact** (_zip_): Take all the sources and create a zip file using the name specified in the
    configuration file. If it's a multiple-architecture project it will create each zip file for every architecture defined in the configuration file. If a custom file extension is selected, this stage will be automatically skipped.

9. **Upload artifact** (_upload_): Take the generated zip files and deploy them to Artifactory based on the path
    provided in the configuration file. If a custom file extension is selected, it will deploy the desired file to Artifactory instead.

10. **Update artifact properties** (_update_): This step is required set up the properties required by the DXC Assure  
    Pull Pipeline in order to take and promote the artifacts: status, etc.

11. **Generate Release Notes** (_releaseNotes_): this step will generate automatically the release with a complete changelog in the Github repository.

12. **Branch protection** (_protection_): this step will apply a branch protection rule (only applied for specific _/release_ pattern branches).

13. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even
    on error cases). This is used to send the required notifications with the build process results and to clean the
    workspace.

##### ********. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#213-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| dependenciesPackageFile | Name of the json file that contains the version information. Normally it will be Cargo.toml | Cargo.toml | |
| dependenciesPackagePath | Path in the repository (workspace) where the toml file can be found. | code | |
| dependenciesPackageAttribute | Name of the attribute in the json file that contains the version data. Normally it will be 'version' | version | |
| ----          | ----          | ---- | ---- |
  artifactoryCrateRegistries | a list of objects with `id` and `credentials` properties, listing the Artifactory-hosted package registries that project uses and the name of the Jenkins credentials that should be used to acces them | Deduced from the project Cargo configuration | (see [above](#21214-referencing-artifactory-cargo-package-registries))
| ----          | ----          | ---- | ---- |
| nextestArgs          | Command to execute tests using nextest          | --no-fail-fast --profile jenkins |  |
| customTestArgs          | Extra arguments to nextestArgs command          | ---- | --skip |
| ----          | ----          | ---- | ---- |
| coverage          | Whether or not to calculate test coverage (using llvm-cov)          | true | false |
| coverageArgs          | Extra arguments to pass to the llvm-cov command           | ---- | --ignore-filename-regex target/deps |
| ----          | ----          | ---- | ---- |
| buildCommand          | Command to execute the build for multiple architectures          | build | ---- |
| buildArgs          | Extra arguments to build command for multiple architectures         | --release --target | ---- |
| ----          | ----          | ---- | ---- |
| compileArchitectures          | Target architectures          | "" | - "x86_64-unknown-linux-gnu" - "aarch64-unknown-linux-gnu" |
| ----          | ----          | ---- | ---- |
| dockerImage          | Define the dockerimage that will be used during the pipeline execution          | default | debian, default |
| ----          | ----          | ---- | ---- |
| clippyExecution          | Allow/Disable the execution of the cargo clippy command during the test stage         | true | true/false |
| clippyArgs          | Cargo clippy arguments          | --all-targets --all-features | ---- |
| ----          | ----          | ---- | ---- |
| auditparams          | Cargo audit arguments          | -q | ---- |
| ----          | ----          | ---- | ---- |
| targetArchitectures          | Specify the target architectures as an array.        | "" | windows, linux, arm |
| ----          | ----          | ---- | ---- |
| zipSourceFolder   | Folder to be zipped to create the artifact to be uploaded. Normally it will be root folder: "." | "." | |
| zipScript     | Custom zip script to zip the artifact to be uploaded. | | "rust-bundle" |
| zipInclude        | Pattern of files to include in the zip. Empty to include all files and directories, e.g. "site-packages/\*\*" | "" (which includes everything from `zipSourceFolder`) |
| ----          | ----          | ---- | ---- |
| fileExtension          | Custom file extension to upload to Artifactory. By default is '.zip'   | '.zip' | ".exe", ".bat" |
| customArtifacts          | Custom artifact selection parameters to upload to Artifactory. | ---- | - architecture: "{selectedArchitecture}" <br> &nbsp; customArtifactName: "{artifactName}" <br> &nbsp; path: "{pathToFile}" |

##### ********. Consumption Example

The following links contain examples for the minimum required configuration regarding rust artifacts:

- [Jenkins file Example](docs/samples/pipelinemultirust/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinemultirust/conf.yml)


### 2.2. Extending and consuming generic pipelines

#### Guideline: How to define a GitHub repository config.yml

1. Search & reuse [the default sample template](docs/samples) created for the different types of generic pipelines.

    Below types of generic pipelines has been created so far:

    - TERRAFORM DEPLOY PIPELINE
    - LAMBDA JS PIPELINE
    - LAMBDA PYTHON PIPELINE
    - UI PIPELINE
    - CUSTOM PIPELINE
    - DATA PIPELINE
    - DOCKER PIPELINE
    - MULTIDOCKER PIPELINE
    - DOTNET PIPELINE
    - NPM PIPELINE
    - JAVA ZIP PIPELINE
    - JAVA DOCKER PIPELINE

2. Fill in the specific fields required (these will be found at the top of the template).

3. The rest of the fields followed by below comment **END OF MODIFICATION AREA** can be used to add the different specific customizable values.

#### How config.yml composition is formed in pipeline runtime

```text
---------------------				   ----------------------------------
| GitHub Repository |				   |   Generic Pipelines Template   |
--------------------- ------------>  +  <---------  ---------------------------------
|		    |		     |		   |			            |
|     conf.yml      |		     | 		   |   type_of_pipeline-conf.yml    |
|		    |		     |	           |                                |
---------------------	 	     |             ----------------------------------
                                     |
                                     |
                                     |
                                     ↓
			------------------------------------------
			|      Runtime conf.yml composition      |
			------------------------------------------
			|			                 |
			|  conf.yml + type_of_pipeline-conf.yml  |
			|	                                 |
			|        - Overriding values -           |
			------------------------------------------
```

### 2.3. Deployment from Push pipelines

The push pipeline will take different path depending on the branch where is being runned:

- **Master branch**: In this branch, the push pipelines will deploy the generated artefact on the lifecycle environments depending on the service type (infrastructure or external/integrated). If the deployment was successful, then the pipeline will execute the post-deployment tests. See [Automatic Testing during PUSH and PULL pipelines](https://github.dxc.com/DIaaS/diaas-platform/blob/master/docs/TESTING_AUTOMATION.md) document for further details.

- **Feature/\* or fix/\*, 'development' and 'prepare-\*' branches**: in these branches, the push pipeline will load the environment(s) parameters from the conf.yml file. After that, it will deploy the generated artefact in that/those environment(s). The post-deployment testing will be executed if it’s enabled or the deployment was successful.

We can enable 2 different functionalities: Deploy and Post-deploy test

Please find below the properties required and available and how to use them:

**General properties**

1. **custom_deployment**
    - true - in any feature, fix, development and prepare- branch, perform the deployment of the generated artifact to the defined environment (see environment variables below)
    - false - do not perform any deployment

3. **custom_deployment_from_master**
    - true - in master branch, perform the deployment of the generated artifact to the defined environment (see environment variables below)
    - false - do not perform any deployment

4. **skipPostDeployTest** - only applies if custom_deployment is enabled
    - true - do not perform post deployment test after custom deployment
    - false - in any feature, fix, development and prepare- branch, perform the testing of the generated artifact after the custom deployment (see testing variables below)

This option (skipPostDeployTest) can be enabled/disabled by environment if there are more than one configured. In that case the property is: targetEnvironment_skipPostDeployTest

**Environment variables**

| Property                                    | Description                  | Example           |
|---------------------------------------------|------------------------------|-------------------|
| targetEnvironment_customerName              | environment's customer name  | dev-33_customerName: "dxcassure" |  
| targetEnvironment_accountName               | environment's account name   | dev-33_accountName: "Assure-STAGE"    |
| targetEnvironment_environment_resource_name | environment's resource name  | dev-33_environment_resource_name: "dev-33-Assure-STAGE" |

**Testing variables**

| Property                                    | Description                     | Example           |
|---------------------------------------------|---------------------------------|-------------------|
| targetEnvironment_environment_arn           | environment iam role arn        | dev-33_environment_arn: "arn:aws:iam::XXXXXXXXX:role/Tf-Ghop" |
| targetEnvironment_environment_region        | environment region              | dev-33_environment_region: "us-east-1" |
| targetEnvironment_environmentType           | environment type                | dev-33_environmentType: "dev" |
| targetEnvironment_environment_endpoint      | environment's endpoint          | dev-33_environment_endpoint: "dev-33.securityhub.stage.assure.dxc.com" |
| targetEnvironment_testTokenUrl              | url used by testing             | dev-33_testTokenUrl: "https://www.dev-33.securityhub.stage.assure.dxc.com/api/auth-management/realms" |

! It is possible to configure more than one target environment for the custom deployment. Just use a different prefix when defining the properties.

_Example_:

```yaml

    ##### Custom Deployment
    custom_deployment: true // enable custom deployment
    custom_deployment_from_master: true // enable custom deployment from master branch

    ##### Deployment variables:
    ######## Environment 1:
    dev-33_customerName: "dxcassure"
    dev-33_accountName: "Assure-STAGE"
    dev-33_environment_resource_name: "dev-33-Assure-STAGE"
    ######## Environment 2:
    dev-34_customerName: "dxcassure"
    dev-34_accountName: "Assure-STAGE"
    dev-34_environment_resource_name: "dev-34-Assure-STAGE"

    ##### Custom post deploy testing
    dev-33_skipPostDeployTest: false  // perform post deploy testing for environment 1
    dev-34_skipPostDeployTest: true   // skip post deploy testing for environment 2 - no extra properties are required

    ###### Environment 1 testing variables
    dev-33_environment_arn: "arn:aws:iam::XXXXXXXXX:role/Tf-Ghop"
    dev-33_environment_region: "us-east-1"
    dev-33_environmentType: "dev-33"
    dev-33_environment_endpoint: "dev-33.securityhub.stage.assure.dxc.com"
    dev-33_testTokenUrl: "https://www.dev-33.securityhub.stage.assure.dxc.com/api/auth-management/realms"

    ###### Environment 2 testing variables are not required
```

### 2.4. Quality Assurance: Checkov & SonarQube flow diagram

>
> This is applicable for **Lambda**, **UI**, **.Net**, **Npm** & **Docker** pipelines.
>
> - **\*NPM** pipeline **does not** generate an artifact (but a Library📚), therefore it does not include 'Update Artifact Properties' stage. This means there is **not 'qa'** logic added either.
>
> - **\*Docker** pipeline **only includes** Checkov scan.
>

Artifact property **'qa'** update based on results:

'pass' 🟩: it means that artifact has _passed_ the QA tests

'fail' 🟥: it means that the artifact has _not complied_ some of the QA tests

'skip' 🟧: it means that the pipeline has _avoided_ some or both of the steps of the QA tests.

## 3. Utility Pipeline Definition

DXC Assure Digital Platform engineering team developed an automatic tool execution process known as UTILITY PIPELINE.

The essence of the UTILITY PIPELINE is very simple: it is an automatic job implemented in Jenkins whose steps are
defined by a script file called Jenkinsfile. 

We implemented different tools for each individual pipeline that will allow users
to execute that tool and receive the results of that execution in a friendly and easy-to-configure way.

We have defined some generic pipelines that are already available to be used just by importing / loading them and configuring some values.

In general, we understand that every utility pipeline will have some steps in common even when they are implemented differently:

1. **Validate pipeline**: ensure that all the conditions to be run are fulfilled:
   - Repository name is OK - to prevent that when a repo is copied from another the execution could override
     original one
   - Branches - in some cases build process should only be done for certain branches
2. **Set up and Initialization**: configure and load whatever element is needed during pipeline execution, i.e. git or
   npm init, initialize some values...
3. **Tool execution**: execute the pipeline tooling to perform the desired scan/check, etc...
4. **Notifications**: notify the results to the defined user/users and store the execution logs.

#### 3.1. Checkov Utility Pipeline

The objective of the pipeline is to scan a list of repositories using Checkov as the selected tool and send the scan logs to the defined users.

##### 3.1.1. Implementation

This is implemented in the Shared Library (/vars/pipeLineCheckov.groovy) as global variable which behave similarly to built-in steps.

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for
further details.

##### 3.1.2. Stages and execution

There is a default dockerfile used from the Shared Libraries that contains the required installations:

- curl
- nodejs
- npm
- openjdk11
- python3
- git
- awscli
- pip
- bash
- gnupg
- checkov

These are the stages implemented (among parenthesis the key to be used for extension):

    All steps are mandatory steps and cannot be skipped.

1. **Pipeline setup** (_setup_): This stage check different scenarios:

    - Select and prepare the docker agent used for the following steps between our pre-defined Dockerfiles. 

2. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is
    a mandatory step that cannot be skipped.

3. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is
    a mandatory step that cannot be skipped.

4. **Get repositories** (_repositories_): Prepare the repositories list provided by the user for the next steps.

5. **Scan repositories** (_scan_): This step clones and scans each one of the provided repositories by the user.

6. **Generate Log Files** (_generate_): Check the checkov logs and creates a .zip file.

7. **Send Notifications** (_notification_): Send an email with the overall result of all the scans for each repository and a .zip compressed file with all the logs.

8. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even
    on error cases). This is used to send the required notifications with the build process results and to clean the
    workspace.

##### 3.1.3. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#223-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| listFileName       | Custom file name for the .yml repository list.  | list | customList |
| emailList          | List of all the email recipients.               | ---- | - '<EMAIL>' |

##### 3.1.4. Repositories list

To provide the pipeline with all the desired repositories to scan using the Checkov tool, this pipeline needs a yml file from which to read a list of repositories that follows the following syntax:

```yaml
repositories:
  # Option 1 - define all parameters
  - repoName: 'my-repository' # Repository name
    organization: 'assure'    # Selected Organization in Github
    branch: 'master'          # Selected branch

  # Option 2 - define all parameters except the organization, by default it'll be set to <assure>
  - repoName: 'my-repository-2'
    branch: 'development'

  # Option 3 - define only the repository name, by default organization will be set to <assure> and branch will be set to <master>
  - repoName: 'my-repository-3'
```

By default the pipeline expects a .yml file called _list.yml_, but this can be modified to whatever name the user want to define in the _conf.yml_ file using the property _listFileName_.

The following parameters are all that will be needed to set up the .yml file from where the pipeline will read all the repositories, their organization and the desired branch to be scanned:

| Parameter | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| repoName          | This parameter is mandatory and needs an existing repository to work.    | -------- | assure-repository-vulnerabilities |
| organization      | This parameter is optional. _assure_ will be selected by default.        | _assure_ | myOrganization |
| branch            | This parameter is optional. _master_ will be selected by default.        | _master_ | feature/customBranch |

##### 3.1.5. Consumption Example

The following links contain examples for the minimum required configuration regarding rust artifacts:

- [Jenkins file Example](docs/samples/pipelinecheckov/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinecheckov/conf.yml)
- [list.yml Example](docs/samples/pipelinecheckov/list.yml)

#### 3.2. Sonar Utility Pipeline

The objective of the pipeline is to scan a list of repositories using SonarQube as the selected tool and send the scan logs to the defined users. The pipeline by itself does not scan
the repository, instead it connects to the SonarQube API and retrieve the last scan made to the repository. Because of this, for this pipeline to work it's mandatory that the repository is currently onboarded as a Push Pipeline [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) and it's properly configured to retrieve the coverage in the SonarQube execution.

##### 3.2.1. Implementation

This is implemented in the Shared Library (/vars/pipeLineSonar.groovy) as global variable which behave similarly to built-in steps.

See [Extending and consuming generic pipelines](#22-extending-and-consuming-generic-pipelines) section for
further details.

##### 3.2.2. Stages and execution

There is a default dockerfile used from the Shared Libraries that contains the required installations:

- curl
- git
- gnupg
- bash
- wget
- zip

These are the stages implemented (among parenthesis the key to be used for extension):

    All steps are mandatory steps and cannot be skipped.

1. **Pipeline setup** (_setup_): This stage check different scenarios:

    - Select and prepare the docker agent used for the following steps between our pre-defined Dockerfiles. 

2. **Pipeline info** (_info_): Load the configuration file (conf.yml) and print its components for reference. This is
    a mandatory step that cannot be skipped.

3. **Validate pipeline** (_validate_): Ensure that the repository is correct to prevent undesired executions. This is
    a mandatory steps that cannot be skipped.

4. **Get repositories** (_repositories_): Prepare the repositories list provided by the user for the next steps.

5. **Scan repositories** (_scan_): This step clones and retrieves the status for each one of the provided repositories by the user from SonarQube.

6. **Generate Log Files** (_generate_): Check the SonarQube logs and creates a .zip file.

7. **Send Notifications** (_notification_): Send an email with the overall result of all the scans for each repository and a .zip compressed file with all the logs.

8. **Pipeline post actions** (_post_): List of actions performed in all cases when the pipeline execution ends (even
    on error cases). This is used to send the required notifications with the build process results and to clean the
    workspace.

##### 3.2.3. Customizable values (conf.yml)

Apart from the values already included in [Common custom configuration values](#223-common-custom-configuration-values) section, this pipeline requires some additional ones:

| Property | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| listFileName       | Custom file name for the .yml repository list.  | list | customList |
| emailList          | List of all the email recipients.               | ---- | - '<EMAIL>' |

##### 3.2.4. Repositories list

To provide the pipeline with all the desired repositories to scan using the SonarQube tool, this pipeline needs a yml file from which to read a list of repositories that follows the following syntax:

```yaml
repositories:
  # Option 1 - define all parameters
  - repoName: 'my-repository' # Repository name
    organization: 'assure'    # Selected Organization in Github
    branch: 'master'          # Selected branch

  # Option 2 - define all parameters except the organization, by default it'll be set to <assure>
  - repoName: 'my-repository-2'
    branch: 'development'

  # Option 3 - define only the repository name, by default organization will be set to <assure> and branch will be set to <master>
  - repoName: 'my-repository-3'
```

By default the pipeline expects a .yml file called _list.yml_, but this can be modified to whatever name the user want to define in the _conf.yml_ file using the property _listFileName_.

The following parameters are all that will be needed to set up the .yml file from where the pipeline will read all the repositories, their organization and the desired branch to be scanned:

| Parameter | Description | Default value | Sample value |
| -------- | ----------- | ------------- | ------------ |
| repoName          | This parameter is mandatory and needs an existing repository to work.    | -------- | assure-repository-vulnerabilities |
| organization      | This parameter is optional. _assure_ will be selected by default.        | _assure_ | myOrganization |
| branch            | This parameter is optional. _master_ will be selected by default.        | _master_ | feature/customBranch |

##### 3.2.5. Consumption Example

The following links contain examples for the minimum required configuration regarding rust artifacts:

- [Jenkins file Example](docs/samples/pipelinesonar/Jenkinsfile)
- [conf.yml Example](docs/samples/pipelinesonar/conf.yml)
- [list.yml Example](docs/samples/pipelinesonar/list.yml)

## 4. References

- [PDXC Jenkins Shared Library project](https://github.dxc.com/Platform-DXC/devops-jenkins-sharedlibs)

- [Jenkins Shared Libs](https://jenkins.io/doc/book/pipeline/shared-libraries/)

- [DXC Assure Platform Lifecycle](https://github.dxc.com/assure/assure-platform/blob/master/docs/processes/PLATFORM_LIFECYCLE.md)

- [DXC Assure Platform Automation Guidelines](https://github.dxc.com/assure/assure-platform/blob/master/docs/guidelines/AUTOMATION_DEVELOPMENT.md)

- [DXC Assure PULL Pipeline](https://github.dxc.com/assure/assure-promotion-pull-pipeline/blob/master/README.md)