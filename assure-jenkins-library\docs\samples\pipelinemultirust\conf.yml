#############################################################################################################
####################################### MODIFICATION AREA ###################################################
#############################################################################################################

#### Name of current repository for validation. It has to match the name of the repository where this file is.
repositoryName: "name-of-current-repository-validation"

#### Artifactory
artifactPath: "path_to_upload_artifact"     #i.e: "assure-platform/data-pipeline/embedded-user-report-lambda"

##### Zip (zip)
targetZipName: "name-of-the-targetZipName"  #i.e: "embedded-user-report-lambda-bundle"

#############################################################################################################
################################### END OF MODIFICATION AREA ################################################
#############################################################################################################

##### GitHub data #####
gitHubCredential: "pdxc-jenkins" # for assure, assure-external or assure-delivery org the value is: "assure-github"
gitEmail: "<EMAIL>"
gitUsername: "Jenkins User"