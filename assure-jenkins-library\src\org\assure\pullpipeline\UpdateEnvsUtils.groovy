/* groovylint-disable ThrowException */
package org.assure.pullpipeline

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.exception.ContextNotSetException
import org.pdxc.util.Utils
import org.pdxc.util.ValuesUtils
import org.assure.envservice.EnvServiceApi
import org.jenkinsci.plugins.workflow.libs.Library
import com.cloudbees.groovy.cps.NonCPS
import groovy.json.JsonOutput
import java.text.SimpleDateFormat


/**
* Utility class
*/
class UpdateEnvsUtils {
    
    private UpdateEnvsUtils() { }
    
    static def deploymentStage(def env, Map releaseData, LinkedHashMap configuration) {
                          
        def result = composeBodyAndRefresh(env, releaseData, configuration)
        
        return result
    }

    static def composeBodyAndRefresh(def env, Map releaseData, LinkedHashMap configuration) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }
                
        def result = []
        env.each { dp ->              
            try {          
                def releaseId = dp.summary.service_name + "_" + dp.version                
                def release = releaseData.get(releaseId)
            
                context.stage("${releaseId}") {
                    def tag = ""
                    if (release.descriptor.deploy_info.tag != "null" && release.descriptor.deploy_info.tag != null) tag = ", \"tag\": \"${release.descriptor.deploy_info.tag}\""

                    def artifactsMap = [:]
                    artifactsMap.put("artifacts", release.descriptor.artifacts)
                    def preparedArtifactJson = JsonOutput.toJson(artifactsMap)        
                    preparedArtifactJson = ValuesUtils.removeStartEndChars(preparedArtifactJson, "{", true, false)
                    preparedArtifactJson = ValuesUtils.removeStartEndChars(preparedArtifactJson, "}", false, true)    

                    def dependsOnData = (release.descriptor.dependsOn != null) ? release.descriptor.dependsOn : [] 
                    def dependsOnDataMap = [:]
                    dependsOnDataMap.put("dependsOn", dependsOnData)
                    def preparedDependsOnData = JsonOutput.toJson(dependsOnDataMap)
                    preparedDependsOnData = ValuesUtils.removeStartEndChars(preparedDependsOnData, "{", true, false)
                    preparedDependsOnData = ValuesUtils.removeStartEndChars(preparedDependsOnData, "}", false, true)
                    
                    def ssmConditions = (release.descriptor.ssm_conditions != null) ? release.descriptor.ssm_conditions : []
                    def ssmConditionsMap = [:]
                    ssmConditionsMap.put("ssm_conditions", ssmConditions)
                    def preparedssmConditionsJson = JsonOutput.toJson(ssmConditionsMap)
                    preparedssmConditionsJson = ValuesUtils.removeStartEndChars(preparedssmConditionsJson, "{", true, false)
                    preparedssmConditionsJson = ValuesUtils.removeStartEndChars(preparedssmConditionsJson, "}", false, true)

                    def environment_resource_id = dp.summary.environment_name + '-' + dp.summary.account_name
                    
                    def body = """{
                        "customer": \"${dp.summary.customer_name}\", 
                        "account": \"${dp.summary.account_name}\",
                        "environment": \"${environment_resource_id}\",
                        "username": \"assure-pipeline\",
                        "terraform_action": "plan",
                        "deploymentPackages": [{
                            "name": \"${dp.summary.service_name}\",
                            "deploy_info": {
                                "git_url": \"${release.descriptor.deploy_info.git_url}\"
                                ${tag}                         
                            },
                            ${preparedArtifactJson},                                                         
                            ${preparedDependsOnData},
                            ${preparedssmConditionsJson},
                            "version": \"${dp.version}\",                            
                            "skip_on_destroy": ${(release.descriptor.skip_on_destroy == true ) ? true : false},
                            "parameters": ${JsonOutput.toJson(dp.parameters)}                               
                        }]
                    }"""
                                        
                    context.echo "-- ⭐ -- BODY: ${body}"

                    def deployResult = [:]
                    deployResult.put ('name_dp', dp.summary.service_name)
                    deployResult.put('environment_name', dp.summary.environment_name)
                    deployResult.put('resource_name', dp.summary.resource_name)
                    deployResult.put('version', dp.version)

                    // PLAN
                    def planResult = planDeploymentPackage(dp.summary.service_name, dp, body, configuration, configuration.sleepTime, configuration.maxTime, configuration.attemptCount)
                    deployResult.put('success', planResult.success)
                    deployResult.put('action', "plan")
                    deployResult.put('location', planResult.location)
                    deployResult.put('tf_log_url', planResult.tf_log)                    
                    result.add(deployResult)
                    def sleepTime = 45
                    context.sh script: "sleep ${sleepTime}", label: "-- ⏳ -- Sleeping"

                    if (planResult.success == false) {
                        Utils.throwExceptionInstance("Failure during deployment")
                    }
                    else {
                        // REFRESH
                        body = body.replace('"terraform_action": "plan",', '"terraform_action": "apply",')                        
                        def refreshResult = refreshDeploymentPackage(dp.summary.service_name, dp, body, configuration, configuration.sleepTime, configuration.maxTime, configuration.attemptCount)
                        deployResult.put('success', refreshResult.success)
                        deployResult.put('action', "apply")
                        deployResult.put('location', refreshResult.location)
                        deployResult.put('tf_log_url', refreshResult.tf_log) 
                        result = []                        
                        result.add(deployResult)
                        if (deployResult.success == false) {
                            Utils.throwExceptionInstance("Failure during deployment")
                        }
                    }                    
                }
            } catch (Exception e) {                
                context.echo "-- ❌ -- Failure during Deploy of ${dp.summary.service_name}_${dp.version}"
            }
        }
       
        return result
    }

    static Map planDeploymentPackage(String deploymentPackage, def dpInfo, def body, LinkedHashMap data, int sleepTime, int maxTime, int attemptCount) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        //TMP
        EnvServiceApi envService = new EnvServiceApi(data.environment_service_url, data.environment_service_credential, data.oauth_host, data.env_api_key)

        context.echo "-- 🚀 -- Deploy deployment package: ${deploymentPackage}"
        def deployResult = [:]

        def urlRefresh = 'customers/' + dpInfo.summary.customer_name + '/accounts/' + dpInfo.summary.account_name + '/environments/' + dpInfo.summary.environment_name + '-' + dpInfo.summary.account_name
        
        def path = '/deployment-packages/' + deploymentPackage + ':refresh'        
 
        def response = envService.postWithCustomAuth(data.environment_service_url + '/' + urlRefresh + path, body, 'POST', 'APPLICATION_JSON')

        def statusURL = response.status
        deployResult.tf_log = response.tf_log_url        
        deployResult.location = ValuesUtils.removeStartEndChars(statusURL, '/status', false, true)
        def responseStatus
        def deployStatus = 'deployment-in-progress'
        def lastLog = ''               

        context.timeout(time: maxTime, unit: 'MINUTES') {
            def counter = 0            
            while (lastLog.contains('###END_STAGE_OK') == false && lastLog.contains('###END_STAGE_KO') == false && counter < attemptCount ) {            
                context.sh script: "sleep ${sleepTime}", label: "-- ⏳ -- Deployment in progress... Installation NOT completed"
                responseStatus = envService.getWithCustomAuth(deployResult.tf_log)                
                lastLog = responseStatus.response                
                counter++
            }
        }

        if (lastLog.contains('###END_STAGE_OK') == true) {
            context.echo "-- 🎉 -- Deployment of deployment package ${deploymentPackage} completed: Installed"
            deployResult.success = true
        } else {
            context.echo "-- 💥 -- Deployment of deployment package ${deploymentPackage} failed"
            deployResult.success = false
        }
        def sdf = new SimpleDateFormat('HH-mm-ss.SS').format(new Date())
        context.writeFile file: "deploylogs/log-${dpInfo.summary.resource_name}-${deploymentPackage}-${sdf}-deploy.log", text: "${lastLog}"
        context.sh script: "cat deploylogs/log-${dpInfo.summary.resource_name}-${deploymentPackage}-${sdf}-deploy.log", label: 'Created log'

        return deployResult        
    }

    static Map refreshDeploymentPackage(String deploymentPackage, def dpInfo, def body, LinkedHashMap data, int sleepTime, int maxTime, int attemptCount) {
        // TMP
        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        //TMP
        EnvServiceApi envService = new EnvServiceApi(data.environment_service_url, data.environment_service_credential, data.oauth_host, data.env_api_key)

        context.echo "-- 🚀 -- Deploy deployment package: ${deploymentPackage}"
        def deployResult = [:]

        def urlRefresh = 'customers/' + dpInfo.summary.customer_name + '/accounts/' + dpInfo.summary.account_name + '/environments/' + dpInfo.summary.environment_name + '-' + dpInfo.summary.account_name
        
        def path = '/deployment-packages/' + deploymentPackage + ':refresh'        
 
        def response = envService.postWithCustomAuth(data.environment_service_url + '/' + urlRefresh + path, body, 'POST', 'APPLICATION_JSON')

        def statusURL = response.status
        deployResult.tf_log = response.tf_log_url
        // TEMPORAL FIX
        statusURL = statusURL.replace('environment-service/environment-service', 'environment-service')
        deployResult.location = ValuesUtils.removeStartEndChars(statusURL, '/status', false, true)
        def responseStatus
        def deployStatus = 'deployment-in-progress'
        def lastLog = ''

        context.timeout(time: maxTime, unit: 'MINUTES') {
            def counter = 0
            while (deployStatus != 'installed' && deployStatus != 'installation-failed' && deployStatus != 'no-deployment-log' && counter < attemptCount ) {
                context.sh script: "sleep ${sleepTime}", label: "-- ⏳ -- Deployment in progress... Installation NOT completed"
                responseStatus = envService.getWithCustomAuth(statusURL)
                lastLog = responseStatus.content
                deployStatus = responseStatus.status
                counter++
            }
        }

        if (deployStatus == 'installed') {
            context.echo "-- 🎉 -- Deployment of deployment package ${deploymentPackage} completed: Installed"
            deployResult.success = true
        } else {
            context.echo "-- 💥 -- Deployment of deployment package ${deploymentPackage} failed: ${deployStatus}"
            deployResult.success = false
        }
        def sdf = new SimpleDateFormat('HH-mm-ss.SS').format(new Date())
        context.writeFile file: "deploylogs/log-${dpInfo.summary.resource_name}-${deploymentPackage}-${sdf}-deploy.log", text: "${lastLog}"
        context.sh script: "cat deploylogs/log-${dpInfo.summary.resource_name}-${deploymentPackage}-${sdf}-deploy.log", label: 'Created log'

        return deployResult        
    }
}