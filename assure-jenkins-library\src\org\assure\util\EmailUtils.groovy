/* groovylint-disable ThrowException */
package org.assure.util

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.exception.ContextNotSetException
import org.pdxc.notification.MailNotification

/**
* Email Utility class
*/
class EmailUtils {

    private EmailUtils() { }

    /**
    * This method builds the object for the email.
    * @param artifact Passed artifact.
    * @param errorStatus Error Status.
    * @param message Message.
    * @return emailBody Email Body object.
    */
    static def buildEmailObject (def artifact, String errorStatus, def message) {
        def emailBody = [committer: artifact.committer, 
                        environment: artifact.environment, 
                        errorStatus: errorStatus,
                        deploymentPackage: artifact.deploymentPackage, 
                        message: message]
        return emailBody
    }

    /**
    * This method creates the objects for the notifications.
    * @param stepArtifacts Artifacts of the current step.
    * @param artifact Passed current artifact.
    * @param errorStatus Error Status.
    * @param logUrl Deploy log url.
    * @param blueOceanUrl Pipeline url.
    * @param status Success boolean status
    */
    static def createEmailNotificationObjects (def stepArtifacts, def artifact, def errorStatus, def overallStatus, def logUrl, def blueOceanUrl, def releasesJson, boolean status) {
        def dpDuplicated = ((stepArtifacts.findAll { arts -> arts.deploymentPackage == artifact.deploymentPackage }).size() > 0) 

        def message = (status == true) ? composeSuccessNotification(artifact, logUrl, blueOceanUrl, dpDuplicated, releasesJson) :
            composeNotificationMessage(artifact, logUrl, blueOceanUrl, errorStatus, dpDuplicated)

        def existingEmailObject = ((stepArtifacts.findAll { arts -> (arts.committer == artifact.committer && 
            arts.environment == artifact.environment && arts.deploymentPackage == artifact.deploymentPackage)}).size() > 0)

        if (existingEmailObject != true) {
            stepArtifacts.add(buildEmailObject(artifact, errorStatus, message))
        } else {
            stepArtifacts.collect{ arts -> arts = concatenateMessagesAndCommitters(arts, artifact, message)}
        }
        return stepArtifacts
    }

    /**
    * This method concatenates messages and committers.
    * @param artifact Passed previous artifact
    * @param collector Passed current artifact
    * @param message Builded message
    */
    static def concatenateMessagesAndCommitters (def artifact, def collector, def message) {
        if (artifact.deploymentPackage == collector.deploymentPackage && artifact.environment == collector.environment) {
            artifact.message = artifact.message + message
            artifact.committer = artifact.committer + ", " + collector.committer
        }
        return artifact
    }
    
    /**
     * Create header and footer for the email notifications.
     * @return [footer, header] Footer and header for the notification email.
     */
    static def composeNotificationEmail() {
        def header = '''
                    <!DOCTYPE html>
                    <html lang="en">
                    <head>
                    <meta charset="UTF-8" />
                    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
                    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                    </head>
                    <body>
                    <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable">
                    <tr><td align="center" valign="top">
                    <table border="0" cellpadding="10" cellspacing="0" width="600" id="emailContainer">
                     '''
        def footer = '''
                    </td></tr></table></td></tr>
                    <tr><td align="center" valign="top">
                    <table border="0" cellpadding="0" cellspacing="0" width="100%" id="emailFooter">
                    <tr><td align="center" valign="top">
                    <i> This is a message generated automatically by the promotion pipeline, please do not reply to this email.
                    </i>
                    <br /><br />
                    <i> If you need assistance or clarification on any of the topics covered in this email, please use the established channels to contact the
                    <b>Automation team</b>.
                    </i>
                    </td></tr></table></td></tr>
                    </table></td></tr></table>
                    </body>
                    </html>
                     '''
        return [footer, header]
    }

    /**
     * Compose the title passing the email type.
     * @param emailTitle Type of desired email.
     * @return title Title for the email.
     */
    static def composeEmailTitle(String emailTitle) {
        def upper = '''
                    <tr><td align="center" valign="top">
                    <table border="0" cellpadding="10" cellspacing="0" width="100%" id="emailHeader">
                    <tr><td valign="top">
                    '''
        def bottom = '''
                    </td></tr></table></td></tr><tr>
                    <td align="center" valign="top">
                    <table border="0" cellpadding="10" cellspacing="0" width="100%" id="emailBody">
                    <tr><td valign="top">
                     '''
        def message = ''
        switch (emailTitle) {
            case 'notification':
                message = "Below you can see the status of the deployed artifact(s) and relevant information about them during the automated promotion process."
            break;
            default:
                message = ''
        }
        return upper + message + bottom
    }

    /**
     * Compose the development or test notification message.
     * @param artifact Passed artifact.
     * @param logUrl Url for the log.
     * @param blueOceanUrl Pipeline url.
     * @param errorStatus Error Status.
     * @return message Message of the notification.
     */
    static def composeNotificationMessage(def artifact, String logUrl, String blueOceanUrl, String errorStatus, boolean dpDuplicated) {
        def message = """
                    ${(dpDuplicated != true && dpDuplicated != null) ? ("<h4>-- 🗃️ -- Deployment Package: <b>${artifact.deploymentPackage}</b></h4>") : "" }
                    <span><b>-- 🗳️ -- Artifact</b>: ${artifact.name}</span>
                    <ul>
                    ${(artifact.environment.equals('development') || artifact.environment.equals('test')) ? 
                    ("<li>📦 <b>Deployment Package</b>: ${artifact.deploymentPackage}</li>") : "" }
                    <li>📜 <b>Status</b>: ${errorStatus}</li>
                    ${(artifact.deployresult != true) ? ("<li>📝 <b>Deploy Log</b>: <a href='${logUrl}'>See log</a></li>") : ""}
                    <li>👤 <b>Committer</b>: ${artifact.committer}</li>
                    <li>💬 <b>Commit</b>: <a href='${artifact.git_url}/commit/${artifact.commit}'>${artifact.commit}</a></li>
                    <li>🚧 <b>Pipeline</b>: <a href='${blueOceanUrl}'>Jenkins Pipeline</a></li>
                    </ul>
                    """
        return message
    }


    /**
     * Compose the success of the release service creation notification message.
     * @param artifact Passed artifact.
     * @param logUrl Url for the log.
     * @param blueOceanUrl Pipeline url.
     * @param dpDuplicated Boolean to achieve if the dp is duplicated.
     * @return message Message of the notification.
     */
     static def composeSuccessNotification(def artifact, String logUrl, String blueOceanUrl, boolean dpDuplicated, def releasesJson) {

        def context = JenkinsContext.getContext()
        if (context == null) {
            throw new ContextNotSetException()
        }

        def releaseVersion
        try {
            releasesJson.each {releaseArt ->
                def dpName = releaseArt.find { property -> property.key == 'deployment_package_name' }.value
                def dpVersion = releaseArt.find { property -> property.key == 'version' }.value
                if (dpName == artifact.deploymentPackage) {
                    releaseVersion = dpVersion
                }                                      
            }
        } catch (Exception e) {
            context.error "-- ❌ -- Error reading the new releases"
        }

       def message = """
                    ${(dpDuplicated != true) ? ("""
                    <h4>-- 🗃️ -- Deployment Package: <b>${artifact.deploymentPackage}</b></h4>
                    <ul>
                    <li>💾 Version: <b>${releaseVersion}</b></li>
                    <li>📜 <b>Status</b>: -- ✔️ -- Release service</li>
                    <li>🏭 <b>Release Service</b>: <a href='https://csam.assure.dxc.com/release-service/index.html#/releases/${artifact.deploymentPackage}/versions/${releaseVersion}'>Link to Release Service</a></li>  
                    <li>🚧 <b>Pipeline</b>: <a href='${blueOceanUrl}'>Jenkins Pipeline</a></li>
                    </ul>
                    """) : "" }
                    <span><b>-- 🗳️ -- Artifact</b>: ${artifact.name}</span>
                    <ul>
                    <li>👤 <b>Committer</b>: ${artifact.committer}</li>
                    <li>💬 <b>Commit</b>: <a href='${artifact.git_url}/commit/${artifact.commit}'>${artifact.commit}</a></li>
                    </ul>
                    """
        return message
    }

    static def composeRsErrorNotification (def art, def blueOceanUrl) {
        def message = """
                    <h4>-- 🗃️ -- Deployment Package: <b>${art.deploymentPackages}</b></h4>
                    <ul>
                    <li>💾 Version: <b>${art.version}</b></li>
                    <li>📜 <b>Status</b>: -- ❌ -- 🏭 Release Service Creation Error</li>
                    <li>🚧 <b>Pipeline</b>: <a href='${blueOceanUrl}'>Jenkins Pipeline</a></li>
                    </ul>
                    <span><b>-- 🗳️ -- Artifact</b>: ${art.name}</span>
                    <ul>
                    <li>👤 <b>Committer</b>: ${art.committer}</li>
                    <li>💬 <b>Commit</b>: <a href='${art.git_url}/commit/${art.commit}'>${art.commit}</a></li>
                    </ul>
                    """
        return message
    }

     /**
    * This method sends an email to notify of the artifact promotion status.
    * @param emailRecipient Email address to send the email.
    * @param message Message to compose the email.
    * @param emailTitle Title of the email.
    */
    static void sendEmailNotifications (String emailRecipient = '<EMAIL>, <EMAIL>', String body, String deploymentPackage, String emailTitle) {
        // Builds the mail and sends the notification
        if (emailRecipient != null && body != null) {
            MailNotification mail = new MailNotification()

            def emailTo 
            def subject 

            if (emailRecipient == '') {
                emailTo = '<EMAIL>'
                subject = "Something went wrong with the notification system"
            } else {
                emailTo = emailRecipient + ', <EMAIL>, <EMAIL>'
                subject = (deploymentPackage != null) ? "-- 🤖 -- Promotion status for DP: ${deploymentPackage}" : "-- 🤖 -- Automated message from the automation team"
            }

            def emailFrom = "<EMAIL>"
            def (footer, header) = composeNotificationEmail()
            def title = composeEmailTitle(emailTitle)
            def emailBody = header + title + body + footer 
            def reportFile = ""
            mail.configureMail(emailTo, emailFrom, subject, emailBody, reportFile)
            mail.send()   
        }
    }
}