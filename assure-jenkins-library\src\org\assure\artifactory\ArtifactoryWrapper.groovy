package org.assure.artifactory

import org.pdxc.jenkins.JenkinsContext
import org.pdxc.rest.ArtifactoryApi
import org.pdxc.util.ValuesUtils
import org.assure.util.AssureUtils

/**
* Wrapper class for Artifactory API.
*/
class ArtifactoryWrapper implements Serializable {
    
    ArtifactoryApi api;
    /* groovylint-disable-next-line FieldTypeRequired, NoDef */
    def context

    ArtifactoryWrapper(String url, String credential) {
        context = JenkinsContext.getContext()
        api = new ArtifactoryApi(url, credential)
    }
    
    /**
     * Get the selected artifact from Artifactory (for generic repositories)
     * @param path Path to the artifact (without base url)
     * @return Artifact object.
     */
    Map getArtifact(String artifactPath) {    
        return api.getPath("/" + ValuesUtils.removeStartEndChars(artifactPath, "/", true, false))        
    }

    Map getArtifactProperties (Map artifact) {
        def regexUN = '(bundle_name_[a-zA-Z-_]*)'
        def bundleNameKey = AssureUtils.getFromRegEx(artifact.tf_properties, regexUN)
        def props

        if (bundleNameKey == null) {
            regexUN = '(image_path_[a-zA-Z-_]*)'
            def artifactUniqueNamePath = AssureUtils.getFromRegEx(artifact.tf_properties,regexUN)

            def variableProperty = ValuesUtils.removeStartEndChars(artifactUniqueNamePath, 'image_path_', true, false)
            
            def branch = (artifact.tf_properties."image_branch_${variableProperty}" != null) ? artifact.tf_properties."image_branch_${variableProperty}" : artifact.tf_properties.artifact_branch_name

            def repositoryPath = artifact.tf_properties."image_path_${variableProperty}"
            repositoryPath = ValuesUtils.removeStartEndChars(repositoryPath, '/', true, false) + '/' + branch
            def packageName = artifact.tf_properties."image_name_${variableProperty}" + '/' +
                                artifact.tf_properties."image_tag_${variableProperty}" + '/' +
                                'manifest.json'
            
            props = api.getResponse(repositoryPath + '/' + packageName + '?properties').properties
            props.put ('name', artifact.tf_properties."image_name_${variableProperty}")
        }
        else {
            def variableProperty = ValuesUtils.removeStartEndChars(bundleNameKey, 'bundle_name_', true, false)

            def branch = (artifact.tf_properties."bundle_branch_${variableProperty}" != null) ? artifact.tf_properties."bundle_branch_${variableProperty}" : artifact.tf_properties.artifact_branch_name

            def repository = artifact.tf_properties."bundle_path_${variableProperty}"
            repository = ValuesUtils.removeStartEndChars(repository, '/', true, false) + '/' + branch
            def packageName = artifact.tf_properties."bundle_name_${variableProperty}" + '.' +
                                artifact.tf_properties."bundle_version_${variableProperty}" + '.' +
                                artifact.tf_properties."bundle_type_${variableProperty}"

            props = api.getResponse(repository + '/' + packageName + '?properties').properties

            props.put ('name', artifact.tf_properties."bundle_name_${variableProperty}")
        }
        
        return props
    }
}