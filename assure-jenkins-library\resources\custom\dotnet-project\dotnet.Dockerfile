FROM mcr.microsoft.com/dotnet/sdk:7.0

# Nuget
RUN apt-get update \
    && apt-get -y install nuget \
    && apt-get clean

# Mono-complete for certmgr
RUN apt-get -y install mono-complete \
    && apt-get clean

# Install certificate for Artifactory
RUN yes | certmgr -ssl https://artifactory.platformdxc-mg.com

# Install .NET 2.1 for Lambda
RUN apt-get -y install gpg \
    && apt-get clean
RUN wget -O- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > microsoft.asc.gpg
RUN mv microsoft.asc.gpg /etc/apt/trusted.gpg.d/
RUN wget https://packages.microsoft.com/config/debian/10/prod.list
RUN mv prod.list /etc/apt/sources.list.d/microsoft-prod.list
RUN chown root:root /etc/apt/trusted.gpg.d/microsoft.asc.gpg
RUN chown root:root /etc/apt/sources.list.d/microsoft-prod.list

RUN apt-get update \
    && apt-get -y install apt-transport-https \
    && apt-get update \
    && apt-get -y install dotnet-sdk-2.1 \
    && apt-get clean

RUN wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
RUN chmod a+x /usr/local/bin/yq

#Checkov
RUN apt-get install -y python3-pip \
    && pip3 install --upgrade pip setuptools \
    && pip3 install -U checkov \
    && apt-get clean