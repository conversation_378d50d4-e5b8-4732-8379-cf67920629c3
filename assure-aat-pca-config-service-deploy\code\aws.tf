variable "aws_region" {
  description = "The AWS region."
  default     = "us-east-1"
}

variable "diaas_aws_account" {
  description = "The AWS account number where resources are to be deployed (defaults to dynamic discovery - which doesn't always work!)"
  default     = ""
}

provider "aws" {
  region = "${var.aws_region}"
}

#
# Some resources MUST be deployed in US East 1 - e.g. Edge Lambdas and
# ACMs for CF distributions etc.
#
# So we have an alias provider to help us with that
#
# Environment resources that need to be so-deployed, but that also have a regional
# version (like ACMs) should only create the US East 1 version if the "normal"
# version isn't being created in US East 1
#
provider "aws" {
  alias  = "us-east-1"
  region = "us-east-1"
}


data aws_caller_identity "current" {}

locals {
	aws_target_account = "${var.diaas_aws_account == "" ? data.aws_caller_identity.current.account_id : var.diaas_aws_account}"
}

